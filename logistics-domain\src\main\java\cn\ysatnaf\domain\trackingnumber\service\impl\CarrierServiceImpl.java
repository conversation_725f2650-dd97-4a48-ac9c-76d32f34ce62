package cn.ysatnaf.domain.trackingnumber.service.impl;

import cn.ysatnaf.domain.trackingnumber.model.entity.Carrier;
import cn.ysatnaf.domain.trackingnumber.repository.CarrierRepository;
import cn.ysatnaf.domain.trackingnumber.service.CarrierService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 承运商基础数据服务实现
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class CarrierServiceImpl implements CarrierService {

    private final CarrierRepository carrierRepository;

    @Override
    public List<Carrier> listActiveCarriers() {
        return carrierRepository.findAllActive();
    }

    @Override
    public Carrier getCarrierById(Long id) {
        Carrier carrier = carrierRepository.findById(id);
        if (carrier == null) {
            throw new ServiceException("承运商不存在, ID: " + id);
        }
        return carrier;
    }

    @Override
    public Carrier getCarrierByCode(String code) {
        Carrier carrier = carrierRepository.findByCode(code);
         if (carrier == null) {
            throw new ServiceException("承运商不存在, Code: " + code);
        }
        return carrier;
    }
} 