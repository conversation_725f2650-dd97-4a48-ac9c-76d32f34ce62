package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.order.model.req.MpOrderPageReq;
import cn.ysatnaf.domain.order.model.req.OrderAssignReq;
import cn.ysatnaf.domain.order.model.res.MpOrderPageRes;
import cn.ysatnaf.domain.order.service.OrderService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * OrderController
 * 订单控制器
 * <AUTHOR> Hang
 * @date 2024/2/7 16:05
 */
@Tag(name = "订单相关")
@RequestMapping("/web/order")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;

    @PostMapping("/assignOrder")
    @Operation(summary = "分配订单")
    public CommonResult<Boolean> assignOrder(@RequestBody @Validated OrderAssignReq req) {
        orderService.assignOrder(req.getOrderId(), req.getCourierId());
        return CommonResult.success(true);
    }

    @PostMapping("/pageMpOrder")
    @Operation(summary = "查询公众号订单列表")
    public CommonResult<PageResult<MpOrderPageRes>> pageMpOrder(@RequestBody @Validated MpOrderPageReq req) {
        return CommonResult.success(orderService.pageMpOrder(req));
    }

    @GetMapping("/countMpOrder/{status}")
    @Operation(summary = "获取公众号待取件订单数量")
    public CommonResult<Integer> countMpOrder(@PathVariable("status") Integer status) {
        return CommonResult.success(orderService.countMpOrder(status));
    }
}
