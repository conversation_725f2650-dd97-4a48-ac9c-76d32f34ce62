package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestSearchDTO;
import cn.ysatnaf.infrastructure.persistent.po.ManifestPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ManifestConverter {
    ManifestConverter INSTANCE = Mappers.getMapper(ManifestConverter.class);

    Manifest toEntity(ManifestPO po);

    List<Manifest> toEntityList(List<ManifestPO> poList);

    ManifestPO toPO(Manifest entity);

    List<ManifestPO> toPOList(List<Manifest> entityList);

}
