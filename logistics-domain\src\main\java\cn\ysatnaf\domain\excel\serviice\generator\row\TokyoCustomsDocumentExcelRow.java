package cn.ysatnaf.domain.excel.serviice.generator.row;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
@Data
public class TokyoCustomsDocumentExcelRow {

    /**
     * 运单id
     */
    @ExcelProperty("NO.")
    private Integer no;

    /**
     * 小包运单号码
     * HAWB
     */
    @ExcelProperty("HAWB")
    private String hawb;

    /**
     * P'KG
     * 小包个数
     * 电商小包只限1个
     */
    @ExcelProperty("P' KG")
    private Integer quantity;

    /**
     * WT(KG)
     * 小包重量
     * 只留小数点后一位，后面舍去（向下取整）
     */
    @ExcelProperty("WT (KG)")
    private BigDecimal weight;

    /**
     * DESCRIPTION
     * 品名
     * 只能使用半角符号
     */
    @ExcelProperty("DESCRIPTION")
    private String description;

    /**
     * VALUE
     * 金额
     * 4000-15000随机整十的数
     */
    @ExcelProperty("VALUE")
    private Integer value;

    /**
     * CNEE COMPANY'S NAME
     * 收件人
     * 卖家人名只能用英文写法（日文名需要转换成罗马拼音）
     */
    @ExcelProperty("CNEE COMPANY'S NAME")
    private String cneeNameEn;

    /**
     * CNEE ADDRESS
     * 收件地址 如：福岡県 久留米市 山川神代1-1-3
     */
    @ExcelProperty("CNEE ADDRESS")
    private String cneeAddressEn;

    /**
     * PHONE NO.
     * 收件人电话号码
     * "注：日本手机号码只有4中开头，080、090、070、050，一共11位。
     * 没有手机只有座机的客人，座机号是0开头的10位号码。
     * "
     */
    @ExcelProperty("PHONE NO.")
    private String phoneNo;

    /**
     * SHIPPER'S NAME
     * 发件代理名称
     */
    @ExcelProperty("SHIPPER'S NAME")
    private String shipperName;

    /**
     * SHIPPER'S ADDRESS
     * 发件代理地址
     */
    @ExcelProperty("SHIPPER'S ADDRESS")
    private String shipperAddress;

    /**
     * CC
     * 一律填写PP
     */
    @ExcelProperty("CC")
    private String cc;

    /**
     * BillNO2
     * 佐川号码
     */
    @ExcelProperty("BillNO2")
    private String billNo2;

    /**
     * CC Money
     * 一律为0
     */
    @ExcelProperty("CC Money")
    private Integer ccMoney;

    /**
     * HF NO
     * 与BILLNO2号码一致
     */
    @ExcelProperty("HF  NO")
    private String hfNo;

    /**
     * M3/KG
     * 体积重量
     */
    @ExcelProperty("M3/KG")
    private BigDecimal m3Kg;

    /**
     * Cnee Zip
     * 收件人邮编
     * 一律为7位数字
     */
    @ExcelProperty("Cnee Zip")
    private String cneeZip;

    /**
     * Cnee Company
     * 收件人名（汉字）
     */
    @ExcelProperty("Cnee Company")
    private String cneeCompany;

    /**
     * Cnee Address
     */
    @ExcelProperty("Cnee Address")
    private String cneeAddress;

    /**
     * Cnee Name
     * 收件人名
     */
    @ExcelProperty("Cnee Name")
    private String cneeName;

    /**
     * Cnee Tel
     * 收件人联系电话
     */
    @ExcelProperty("Cnee Tel")
    private String cneeTel;

    /**
     * HAWB
     * 佐川号码
     */
    @ExcelProperty("HAWB")
    private String hawb2;

    /**
     * LWH
     * 长宽高
     */
    @ExcelProperty("LWH")
    private String lwh;

    /**
     * PackageNO
     * 保税库要求的区分装袋的号码
     */
    @ExcelProperty("PackageNO")
    private String packageNo;

}
