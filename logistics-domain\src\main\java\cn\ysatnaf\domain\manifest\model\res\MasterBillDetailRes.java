package cn.ysatnaf.domain.manifest.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 主提单详情响应DTO
 * <AUTHOR>
 */
@Data
public class MasterBillDetailRes {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 提单号/航班号
     */
    private String masterBillNumber;

    /**
     * 起飞日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime departureDate;

    /**
     * 到达日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime arrivalDate;

    /**
     * 始发地
     */
    private String origin;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 承运商代码
     */
    private String carrierCode;

    /**
     * 提单状态
     */
    private Integer status;

    /**
     * 提单状态描述
     */
    private String statusDesc;

    /**
     * 总重量
     */
    private BigDecimal totalWeight;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;

    /**
     * 运单数量
     */
    private Integer waybillCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 