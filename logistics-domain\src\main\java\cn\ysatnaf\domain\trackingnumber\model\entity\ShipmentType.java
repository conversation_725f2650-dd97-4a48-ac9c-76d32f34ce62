package cn.ysatnaf.domain.trackingnumber.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 货物类型领域实体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShipmentType {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 类型代码 (例如: NORMAL, BATTERY, POSTAL)
     */
    private String code;

    /**
     * 类型名称 (例如: 普通货物, 带电货物, 投函)
     */
    private String name;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 