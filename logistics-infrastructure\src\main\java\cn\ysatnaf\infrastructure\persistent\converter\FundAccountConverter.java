package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.fund.model.entity.FundAccountEntity;
import cn.ysatnaf.infrastructure.persistent.po.FundAccountPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 */
@Mapper
public interface FundAccountConverter {
    FundAccountConverter INSTANCE = Mappers.getMapper(FundAccountConverter.class);

    FundAccountPO toPO(FundAccountEntity entity);

    FundAccountEntity toEntity(FundAccountPO po);

    List<FundAccountEntity> toEntityList(List<FundAccountPO> selectList);
}
