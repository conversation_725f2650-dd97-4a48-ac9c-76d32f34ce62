package cn.ysatnaf.domain.trackingnumber.service;

import cn.ysatnaf.domain.trackingnumber.model.entity.Location;

import java.util.List;

/**
 * 地点/区域基础数据服务接口
 * <AUTHOR>
 */
public interface LocationService {

    /**
     * 获取所有启用的地点
     * @return 地点列表
     */
    List<Location> listActiveLocations();

    /**
     * 根据ID获取地点
     * @param id 地点ID
     * @return 地点，不存在则抛出异常或返回null
     */
    Location getLocationById(Long id);
    
    /**
     * 根据Code获取地点
     * @param code 地点Code
     * @return 地点，不存在则抛出异常或返回null
     */
    Location getLocationByCode(String code);
    
    // 可能还需要添加、修改、删除等管理功能

} 