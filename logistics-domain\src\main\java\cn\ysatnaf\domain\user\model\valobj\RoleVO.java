package cn.ysatnaf.domain.user.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Hang
 */
@Getter
@AllArgsConstructor
public enum RoleVO {

    /**
     * 0-超级管理员
     * 1-管理员
     * 2-本地用户
     * 3-网络用户
     * 4-公众号用户
     * 5-快递员
     */
    SUPER_ADMIN(0L, "超级管理员"),
    ADMIN(1L, "管理员"),
    LOCAL_USER(2L, "本地用户"),
    NETWORK_USER(3L, "网络用户"),
    MP_USER(4L, "公众号用户"),
    COURIER(5L, "快递员"),
    ;

    private final Long code;
    private final String desc;

    public static RoleVO getRoleVOByCode(Long code) {
        for (RoleVO roleVO : RoleVO.values()) {
            if (roleVO.getCode().equals(code)) {
                return roleVO;
            }
        }
        return null;
    }
}
