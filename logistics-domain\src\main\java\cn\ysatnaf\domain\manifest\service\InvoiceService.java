package cn.ysatnaf.domain.manifest.service;

import cn.ysatnaf.domain.manifest.model.req.InvoiceGenerationTaskSubmitReq;

/**
 * <AUTHOR>
 */
public interface InvoiceService {

    /**
     * 提交生成发票任务
     * @param req 生成发票入参请求
     * @return 生成发票任务ID
     */
    Long submitGenerationTask(InvoiceGenerationTaskSubmitReq req);

    /**
     * 生成发票
     * @param taskId 任务ID
     */
    void generate(Long taskId);

    void masterBillGenerate(Long taskId);
}
