package cn.ysatnaf.domain.manifest.model.req;

import cn.ysatnaf.types.common.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
public class ManifestPreReportPreviewReq extends PageParam {

    @NotBlank(message = "批次号不能为空")
    private String batchId;

    @NotNull(message = "预览类型不能为空")
    private Integer previewType;
}
