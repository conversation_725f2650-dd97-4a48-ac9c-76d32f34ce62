package cn.ysatnaf.infrastructure.persistent.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
@Data
@TableName("tb_balance_detail")
public class BalanceDetailPO extends BasePO {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long accountId;

    private BigDecimal changeAmount;

    private BigDecimal originalBalance;

    private BigDecimal newBalance;

    private String remark;

    private Long manifestId;

    private Long operatorId;

    private String operator;
}
