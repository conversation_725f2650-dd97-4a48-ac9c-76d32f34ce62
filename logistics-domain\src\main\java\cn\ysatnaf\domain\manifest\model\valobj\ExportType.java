package cn.ysatnaf.domain.manifest.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> Hang
 */
@Getter
@AllArgsConstructor
public enum ExportType {
    /**
     * 0-全部
     * 1-指定
     * 2-查询条件
     */
    ALL(0, "全部"),
    SPECIFIED(1, "指定"),
    QUERY_CONDITION(2, "查询条件");

    private final Integer code;
    private final String desc;

    public static ExportType getExportType(Integer code) {
        for (ExportType exportType : ExportType.values()) {
            if (exportType.getCode().equals(code)) {
                return exportType;
            }
        }
        return null;
    }
}
