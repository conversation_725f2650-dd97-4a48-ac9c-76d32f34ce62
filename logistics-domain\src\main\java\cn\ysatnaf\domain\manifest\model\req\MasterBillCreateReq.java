package cn.ysatnaf.domain.manifest.model.req;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 主提单创建请求DTO
 * <AUTHOR>
 */
@Data
public class MasterBillCreateReq {

    /**
     * 提单号/航班号(必填)
     */
    @NotBlank(message = "提单号不能为空")
    private String masterBillNumber;

    /**
     * 起飞日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime departureDate;

    /**
     * 到达日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime arrivalDate;

    /**
     * 始发地
     */
    private String origin;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 承运商代码
     */
    private String carrierCode;

    /**
     * 提单状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
} 