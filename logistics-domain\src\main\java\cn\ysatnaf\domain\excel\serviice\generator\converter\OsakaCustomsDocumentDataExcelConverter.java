package cn.ysatnaf.domain.excel.serviice.generator.converter;

import cn.ysatnaf.domain.excel.serviice.generator.DocumentDataContext;
import cn.ysatnaf.domain.excel.serviice.generator.row.OsakaCustomsDocumentExcelRow;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@RequiredArgsConstructor
public class OsakaCustomsDocumentDataExcelConverter {

    /**
     * 将manifest转换为excel数据
     */
    public List<OsakaCustomsDocumentExcelRow> convert(DocumentDataContext context) {
        List<Manifest> manifests = context.getManifests();
        // 转换成对应的excel对象
        List<OsakaCustomsDocumentExcelRow> manifestExcelData = new ArrayList<>();
        Set<String> expressNumbers = new HashSet<>();
        for (int i = 1; i < manifests.size(); i++) {
            Manifest manifest = manifests.get(i);
            String trackingNumber = manifest.getTrackingNumber();
            if (expressNumbers.contains(trackingNumber)) {
                continue;
            }
            expressNumbers.add(trackingNumber);
            OsakaCustomsDocumentExcelRow row = new OsakaCustomsDocumentExcelRow();
            row.setNo(i);
            row.setMaterBL(context.getBatchInfo().getLadingBill());
            row.setHouseBL(trackingNumber);
            row.setDeliveryComp(DeliveryCompany.getByShippingTemplateType(manifest.getShippingFeeTemplateType()).getCode());
            if (manifest.getShippingFeeTemplateType().equals(ShippingFeeTemplateTypeEnum.GENERAL_OSAKA.getCode())) {
                row.setTrackingNo(trackingNumber);
            }
            row.setImyZipCode(manifest.getReceiverZipCode());
            row.setImnJp(manifest.getReceiverName());
            row.setImn(manifest.getReceiverEnName());
            row.setIadJp(manifest.getReceiverAddress());
            row.setIadEn(manifest.getReceiverEnAddress());
            row.setImtPhone(manifest.getReceiverPhone());
            row.setGrossWeight(manifest.getWeight());
            manifestExcelData.add(row);
        }
        return manifestExcelData;
    }

    @Getter
    @AllArgsConstructor
    private enum DeliveryCompany {
        SAGAWA_S8("S8", ShippingFeeTemplateTypeEnum.GENERAL_OSAKA.getCode()),
        POST_S9("S9", ShippingFeeTemplateTypeEnum.SMALL_PARCEL_OSAKA.getCode());

        private final String code;
        private final Integer shippingTemplateType;

        public static DeliveryCompany getByShippingTemplateType(Integer shippingTemplateType) {
            for (DeliveryCompany value : values()) {
                if (value.getShippingTemplateType().equals(shippingTemplateType)) {
                    return value;
                }
            }
            throw new ServiceException("运费模板错误: " + shippingTemplateType + ", 请联系管理员");
        }
    }
}
