package cn.ysatnaf.types.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hang
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TranslationUtil {

    private final StringRedisTemplate stringRedisTemplate;

    public String translateFromChineseToEnglish(String chinese) {
        String english = stringRedisTemplate.opsForValue().get("translate:zh_en:" + chinese);
        if (StrUtil.isNotBlank(english)) {
            return english;
        }
        log.info("中翻英-翻译内容:{}", chinese);
        english = translate(chinese, "zh", "en", 1);
        log.info("中翻英-翻译结果:{}", english);
        if (StrUtil.isNotBlank(english)) {
            stringRedisTemplate.opsForValue().set("translate:zh_en:" + chinese, english);
        }
        return english;
    }

    public String translateFromJapaneseToEnglish(String japanese) {
        return translateFromJapaneseToEnglish(japanese, 1);
    }

    public String translateFromJapaneseToEnglish(String japanese, int requestTimes) {
        String english = stringRedisTemplate.opsForValue().get("translate:jp_en:" + japanese);
        if (StrUtil.isNotBlank(english)) {
            return english;
        }
        log.info("日翻英-翻译内容:{}", japanese);
        english = translate(japanese, "jp", "en", requestTimes);
        log.info("日翻英-翻译结果:{}", english);
        if (StrUtil.isNotBlank(english)) {
            stringRedisTemplate.opsForValue().set("translate:jp_en:" + japanese, english);
        }
        return english;
    }

    public static String translate(String text, String from, String to, Integer requestTimes) {
        if (StrUtil.isBlank(text)) {
            return text;
        }
        if (requestTimes > 5) {
            log.error("translateFromJapaneseToEnglish retry too much.");
            throw new ServiceException("翻译失败，重试次数过多，请联系管理员");
        }
        String appid = "20231230001926157";
        String secretKey = "zpFUw2si84zlYWaVNajT";
        String salt = RandomUtil.randomNumbers(10);
        String sign = SecureUtil.md5(appid + text + salt + secretKey);
        String getUrl = "http://api.fanyi.baidu.com/api/trans/vip/translate?q=%s&from=%s&to=%s&appid=%s&salt=%s&sign=%s";
        String english;
        try {
            String url = String.format(getUrl, URLEncoder.encode(text, StandardCharsets.UTF_8.name()), from, to, appid, salt, sign);
            String res = HttpUtil.get(url);
            TranslationResult translationResult = JSON.parseObject(res, TranslationResult.class);
            if (StrUtil.equals(translationResult.getError_code(), "20003")) {
                text = text.replaceAll(" ", "");
                sign = SecureUtil.md5(appid + text + salt + secretKey);
                url = String.format(getUrl, URLEncoder.encode(text, StandardCharsets.UTF_8.name()), from, to, appid, salt, sign);
                res = HttpUtil.get(url);
                translationResult = JSON.parseObject(res, TranslationResult.class);
            }
            // 请求成功
            if (StrUtil.isBlank(translationResult.getError_code())) {
                english = translationResult.getTrans_result().get(0).getDst();
            } else if (StrUtil.equalsAny(translationResult.getError_code(), "52001", "52002", "54003")) {
                log.error("translateFromJapaneseToEnglish error: error_code-{}, error_msg-{}", translationResult.getError_code(), translationResult.getError_msg());
                // 重试
                return translate(text, from, to, requestTimes + 1);
            } else {
                log.error("translateFromJapaneseToEnglish error: error_code-{}, error_msg-{}", translationResult.getError_code(), translationResult.getError_msg());
                throw new ServiceException("翻译失败：" + translationResult.getError_msg());
            }
        } catch (UnsupportedEncodingException e) {
            log.error("translateFromJapaneseToEnglish error: ", e);
            throw new RuntimeException(e);
        }
        return english;
    }

    public String translateAddressFromJapaneseToEnglish(String address) {
        // 定义正则表达式，匹配包含汉字、片假名和平假名的日文部分

        List<SortText> sortTexts = match(address);
        return sortTexts.stream().sorted(Comparator.comparing(SortText::getIndex)).map(SortText::getText).collect(Collectors.joining());
    }

    @NotNull
    private List<SortText> match(String address) {
        String regexKanji = "[一-龯]+";
        Pattern patternKanji = Pattern.compile(regexKanji);
        Matcher matcherKanji = patternKanji.matcher(address);
        while (matcherKanji.find()) {
            String kanji = matcherKanji.group();
            address = address.replaceAll(kanji, translateFromJapaneseToEnglish(kanji) + " ");
        }
        String regex = "[\\u3040-\\u309F\\u30A0-\\u30FF]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(address);
        int startIndex = 0;
        List<SortText> sortTexts = new ArrayList<>();
        // 查找并输出日文部分
        while (matcher.find()) {
            String japaneseText = matcher.group();

            // 获取日文部分的开始索引和结束索引
            int japaneseStartIndex = matcher.start();
            int japaneseEndIndex = matcher.end();
            if (japaneseStartIndex != startIndex) {
                sortTexts.add(SortText.builder().text(address.substring(startIndex, japaneseStartIndex)).index(startIndex).build());
            }

            sortTexts.add(SortText.builder().text(translateFromJapaneseToEnglish(japaneseText) + " ").index(japaneseStartIndex).build());

            startIndex = japaneseEndIndex;
        }
        if (startIndex < address.length() - 1) {
            sortTexts.add(SortText.builder().text(address.substring(startIndex)).index(startIndex).build());
        }
        return sortTexts;
    }
}
