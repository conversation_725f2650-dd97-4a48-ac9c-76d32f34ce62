package cn.ysatnaf.domain.parcelsorting.model.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * BoxingDetailPO
 * 装箱明细
 * <AUTHOR>
 * @date 2024/3/29 18:58
 */
@TableName("tb_parcel_sorting_package")
@EqualsAndHashCode(callSuper = true)
@Data
public class ParcelSortingPackagePO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long manifestId;

    private String orderNo;

    private String expressNumber;

    private String receiverName;

    private Long boxId;
}
