# 揽件接口文档

## 接口基本信息

| 项目     | 内容                                                               |
| -------- | ------------------------------------------------------------------ |
| 接口名称 | 揽件                                                               |
| 接口地址 | `/web/manifest/pickup`                                             |
| 请求方式 | POST                                                               |
| 请求类型 | application/json                                                   |
| 功能描述 | 对运单进行揽件操作，支持首次揽件和重新揽件（重新揽件需要密码确认） |

## 请求参数

### 请求头

| 参数名        | 类型   | 必填 | 说明             |
| ------------- | ------ | ---- | ---------------- |
| Content-Type  | String | 是   | application/json |
| Authorization | String | 是   | Bearer {token}   |

### 请求体

| 参数名                    | 类型                             | 必填     | 校验规则               | 说明                                          |
| ------------------------- | -------------------------------- | -------- | ---------------------- | --------------------------------------------- |
| id                        | Long                             | 否       | -                      | 运单 ID                                       |
| expressNumber             | String                           | 是       | @NotBlank              | 快递单号                                      |
| sawagaNumber              | String                           | 是       | @NotBlank              | 佐川单号                                      |
| transferredTrackingNumber | String                           | 否       | -                      | 转单号                                        |
| orderNumber               | String                           | 是       | @NotBlank              | 商店订单号（客户自定）                        |
| receiverZipCode           | String                           | 是       | @NotBlank              | 邮编                                          |
| receiverName              | String                           | 是       | @NotBlank              | 收件人                                        |
| receiverAddress           | String                           | 是       | @NotBlank              | 收件地址                                      |
| receiverPhone             | String                           | 是       | @NotBlank              | 收件人电话                                    |
| length                    | BigDecimal                       | 否       | -                      | 长（厘米）                                    |
| width                     | BigDecimal                       | 否       | -                      | 宽（厘米）                                    |
| height                    | BigDecimal                       | 否       | -                      | 高（厘米）                                    |
| weight                    | BigDecimal                       | 是       | @NotNull               | 重量（kg）                                    |
| dimensionalWeight         | BigDecimal                       | 否       | -                      | 体积重量（kg）                                |
| cost                      | BigDecimal                       | 是       | @NotNull               | 所需费用                                      |
| shippingFeeTemplateType   | Integer                          | 是       | @NotNull               | 运费模板类型:1-普通模板;2-带电模板;3-投函模板 |
| overLengthSurcharge       | BigDecimal                       | 否       | -                      | 超长费                                        |
| remoteAreaSurcharge       | BigDecimal                       | 否       | -                      | 偏远费                                        |
| otherCostName             | String                           | 否       | -                      | 其他费用名称                                  |
| otherCost                 | BigDecimal                       | 否       | -                      | 其他费用                                      |
| manifestItems             | List&lt;ManifestItemOrderDTO&gt; | 是       | @NotEmpty @Valid       | 物品列表                                      |
| masterBillId              | Long                             | 否       | -                      | 提单 ID                                       |
| masterBillNumber          | String                           | 否       | -                      | 提单号                                        |
| **confirmPassword**       | **String**                       | **条件** | **当运单已揽件时必填** | **确认密码（当前用户登录密码）**              |

#### ManifestItemOrderDTO 结构

| 参数名   | 类型       | 必填 | 说明       |
| -------- | ---------- | ---- | ---------- |
| name     | String     | 是   | 物品名称   |
| weight   | BigDecimal | 是   | 重量（kg） |
| quantity | Integer    | 是   | 数量       |
| price    | BigDecimal | 是   | 价格       |

### 请求示例

#### 首次揽件

```json
{
  "id": 1001,
  "expressNumber": "BM2024001",
  "sawagaNumber": "SG2024001",
  "orderNumber": "ORDER001",
  "receiverZipCode": "1000001",
  "receiverName": "山田太郎",
  "receiverAddress": "東京都千代田区千代田1-1",
  "receiverPhone": "09012345678",
  "weight": 1.5,
  "cost": 1000,
  "shippingFeeTemplateType": 1,
  "manifestItems": [
    {
      "name": "测试商品",
      "weight": 1.5,
      "quantity": 1,
      "price": 1000
    }
  ]
}
```

#### 重新揽件（需要密码确认）

```json
{
  "id": 1001,
  "expressNumber": "BM2024001",
  "sawagaNumber": "SG2024001",
  "orderNumber": "ORDER001",
  "receiverZipCode": "1000001",
  "receiverName": "山田太郎",
  "receiverAddress": "東京都千代田区千代田1-1",
  "receiverPhone": "09012345678",
  "weight": 1.5,
  "cost": 1000,
  "shippingFeeTemplateType": 1,
  "manifestItems": [
    {
      "name": "测试商品",
      "weight": 1.5,
      "quantity": 1,
      "price": 1000
    }
  ],
  "confirmPassword": "your_login_password"
}
```

## 响应参数

### 响应体结构

| 参数名 | 类型    | 说明                         |
| ------ | ------- | ---------------------------- |
| code   | Integer | 响应状态码，0 表示成功       |
| data   | Boolean | 业务数据，揽件操作结果       |
| msg    | String  | 响应消息，错误时包含错误描述 |

### 成功响应示例

```json
{
  "code": 0,
  "data": true,
  "msg": ""
}
```

### 错误响应示例

```json
{
  "code": 400,
  "data": null,
  "msg": "该运单已揽件，重新揽件需要输入确认密码"
}
```

## 业务逻辑说明

### 1. 运单状态判断

- **待揽件（status = 1）**：正常揽件流程，无需密码确认
- **已揽件（status = 2）**：重新揽件流程，需要密码确认
- **已发货（status = 3）**：禁止重新揽件

### 2. 密码验证逻辑

- 仅当运单状态为已揽件（status = 2）且未发货时要求密码确认
- 使用当前登录用户的密码进行验证
- 密码验证使用 MD5 加密对比

### 3. 权限控制

- 仅管理员用户可以执行揽件操作
- 需要有效的登录 token

### 4. 数据校验

- 电话号码格式验证（日本手机号格式）
- 邮编格式验证（日本邮编格式）
- 物品名称违禁词校验

## 错误码说明

| 错误码 | 错误描述                            | 解决方案                 |
| ------ | ----------------------------------- | ------------------------ |
| 0      | 成功                                | 无需处理                 |
| 400    | 请求参数不正确                      | 检查请求参数格式和必填项 |
| 401    | 账号未登录                          | 重新登录获取有效 token   |
| 403    | 没有该操作权限                      | 联系管理员分配权限       |
| 500    | 系统异常                            | 联系技术支持             |
| 9999   | 业务异常（具体错误信息见 msg 字段） | 根据具体错误信息处理     |

### 具体业务异常信息

| 异常信息                               | 原因                     | 解决方案                   |
| -------------------------------------- | ------------------------ | -------------------------- |
| 该运单已揽件，重新揽件需要输入确认密码 | 重新揽件时未提供密码     | 提供当前用户的登录密码     |
| 确认密码错误，无法重新揽件             | 提供的密码不正确         | 输入正确的当前用户登录密码 |
| 该运单已发货，无法再次被揽件           | 尝试对已发货运单重新揽件 | 已发货运单无法重新揽件     |
| 该订单已被废弃，无法揽件               | 运单已被标记为删除       | 恢复运单后再执行揽件操作   |
| 录入失败：收件人电话号码格式错误       | 电话号码格式不符合要求   | 使用正确的日本手机号格式   |
| 邮编错误                               | 邮编格式不符合要求       | 使用正确的日本邮编格式     |

## 安全注意事项

1. **密码安全**：

   - 密码通过 HTTPS 加密传输
   - 服务端不记录密码明文到日志
   - 使用 MD5 加密进行密码对比

2. **权限控制**：

   - 仅管理员可以执行揽件操作
   - 验证当前登录用户身份

3. **操作审计**：
   - 记录重新揽件操作的详细日志
   - 包含操作人、操作时间、运单信息等

## 相关接口

- [运单查询接口](./运单查询接口文档.md) - 查询运单详细信息
- [运单列表接口](./运单列表接口文档.md) - 获取运单列表
- [发货接口](./发货接口文档.md) - 运单发货操作

## 更新日志

| 版本 | 更新时间   | 更新内容                                 | 更新人    |
| ---- | ---------- | ---------------------------------------- | --------- |
| v1.0 | 2024-03-29 | 初始版本，支持基础揽件功能               | Chen Hang |
| v1.1 | 2024-12-19 | 新增重新揽件密码确认功能，提升操作安全性 | Assistant |
