package cn.ysatnaf.domain.manifest.service;

import cn.ysatnaf.domain.manifest.model.entity.SawagaNumberEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SawagaNumberService {
    void insertBatch(List<SawagaNumberEntity> numbers);

    SawagaNumberEntity useOne();

    List<SawagaNumberEntity> useBatch(Integer count);

    List<SawagaNumberEntity> getBatchSawagaNumber(int size);

    void updateBatchById(List<SawagaNumberEntity> batchSawagaNumber);

    Integer countLeftSawagaNumber();

    void revertSawagaNumber(String expressNo);
}
