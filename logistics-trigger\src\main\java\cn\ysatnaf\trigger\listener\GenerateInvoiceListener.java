package cn.ysatnaf.trigger.listener;

import cn.ysatnaf.domain.manifest.service.InvoiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class GenerateInvoiceListener {

    private final InvoiceService invoiceService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = "generate.invoice.queue", durable = "true"),
            exchange = @Exchange(name = "generate.exchange"),
            key = {"generate.invoice"}
    ))
    public void listen(Long taskId) {
        invoiceService.generate(taskId);
    }
}
