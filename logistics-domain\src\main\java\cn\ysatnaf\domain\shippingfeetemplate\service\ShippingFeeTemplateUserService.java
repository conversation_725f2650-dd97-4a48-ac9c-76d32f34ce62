package cn.ysatnaf.domain.shippingfeetemplate.service;

import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplatePO;
import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplateUserPO;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;

import java.util.List;

public interface ShippingFeeTemplateUserService {

    /**
     * 根据用户ID和类型获取模板-用户关系
     *
     * @param userId 用户ID
     * @param type   模板类型 {@link ShippingFeeTemplateTypeEnum#getCode()}
     * @return 模板-用户关系
     */
    ShippingFeeTemplateUserPO getByUserIdAndType(Long userId, Integer type);

    /**
     * 给用户绑定模板
     *
     * @param templates 模板列表
     * @param userId    用户ID
     */
    void bind(List<ShippingFeeTemplatePO> templates, Long userId);

    /**
     * 根据用户ID列表查询模板-用户绑定关系
     * @param userIds 用户IDs
     * @return 模板-用户绑定关系
     */
    List<ShippingFeeTemplateUserPO> getByUserIds(List<Long> userIds);
}
