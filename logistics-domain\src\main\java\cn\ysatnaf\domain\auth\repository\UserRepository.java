package cn.ysatnaf.domain.auth.repository;

import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.types.common.PageResult;

import java.util.List;
import java.util.Set;

/**
 * UserRepository
 *
 * <AUTHOR>
 * @date 2023/12/21 19:18
 */
public interface UserRepository {
    UserEntity findByOpenId(String openid);

    UserEntity insert(UserEntity newUser);

    UserEntity getByUsername(String username);

    void updateById(UserEntity userEntity);

    PageResult<UserEntity> page(Integer pageNo, Integer pageSize);

    UserEntity getById(Long id);

    void deleteById(Long id);

    List<UserEntity> listByIds(Set<Long> creatorIds);

    UserEntity getByOpenid(String openid);

    UserEntity getByMobileNumber(String mobileNumber);
}
