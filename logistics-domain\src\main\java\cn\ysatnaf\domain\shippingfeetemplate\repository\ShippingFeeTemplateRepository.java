package cn.ysatnaf.domain.shippingfeetemplate.repository;

import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplatePO;

import java.util.List;

public interface ShippingFeeTemplateRepository {

    void insert(ShippingFeeTemplatePO shippingFeeTemplatePO);

    void updateById(ShippingFeeTemplatePO shippingFeeTemplatePO);

    void deleteById(Long id);

    List<ShippingFeeTemplatePO> list(Integer type);

    ShippingFeeTemplatePO getById(Long templateId);

    List<ShippingFeeTemplatePO> getByIds(List<Long> templateIds);
}
