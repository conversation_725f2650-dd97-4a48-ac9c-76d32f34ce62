package cn.ysatnaf.domain.address.service.impl;

import cn.ysatnaf.domain.address.model.entity.AreaEntity;
import cn.ysatnaf.domain.address.repository.AreaRepository;
import cn.ysatnaf.domain.address.service.AreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AreaServiceImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 11:30
 */
@Service
@RequiredArgsConstructor
public class AreaServiceImpl implements AreaService {

    private final AreaRepository areaRepository;

    @Override
    public List<AreaEntity> listByPid(Long pid) {
        if (pid == null) {
            // 如果为空，设置为0（中国）
            pid = 0L;
        }
        return areaRepository.listByPid(pid);
    }
}
