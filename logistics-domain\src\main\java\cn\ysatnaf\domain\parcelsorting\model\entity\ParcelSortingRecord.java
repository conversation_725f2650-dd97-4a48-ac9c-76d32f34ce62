package cn.ysatnaf.domain.parcelsorting.model.entity; // Assuming this path

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime; // Assuming BaseEntity fields might be needed

/**
 * 装箱记录 领域实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParcelSortingRecord {

    private Long id;
    private String recordName;
    private Long masterBillId;
    private Boolean isDelete;

    /**
     * 预期装箱总件数 (可选, 快照)
     */
    private Integer totalItemsExpected;

    // Potentially add fields from BaseEntity if needed (creatorId, createTime etc.)
    // private Long creatorId;
    // private LocalDateTime createTime;
    // private Long updaterId;
    // private LocalDateTime updateTime;

} 