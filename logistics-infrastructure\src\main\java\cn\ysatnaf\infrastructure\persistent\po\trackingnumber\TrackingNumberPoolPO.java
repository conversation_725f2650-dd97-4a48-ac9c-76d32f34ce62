package cn.ysatnaf.infrastructure.persistent.po.trackingnumber;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 预报单号池持久化对象
 * <AUTHOR>
 */
@Data
@TableName("tracking_number_pool")
public class TrackingNumberPoolPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 单号字符串
     */
    private String trackingNumber;

    /**
     * 所属渠道ID (FK -> tracking_number_channels.id)
     */
    private Long channelId;

    /**
     * 单号状态 (0: 可用/Available, 1: 已分配/Allocated)
     */
    private Integer status;

    /**
     * 导入批次ID (FK -> tracking_number_import_batch)
     */
    private Long importBatchId;

    /**
     * 分配批次ID (FK -> tracking_number_allocation_batch)
     */
    private Long allocationBatchId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 