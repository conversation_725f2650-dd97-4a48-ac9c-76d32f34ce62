package cn.ysatnaf.infrastructure.persistent.repository.trackingnumber;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.trackingnumber.model.entity.Carrier;
import cn.ysatnaf.domain.trackingnumber.repository.CarrierRepository;
import cn.ysatnaf.infrastructure.persistent.converter.trackingnumber.CarrierConverter;
import cn.ysatnaf.infrastructure.persistent.dao.trackingnumber.CarrierDao;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.CarrierPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 承运商仓库实现
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class CarrierRepositoryImpl implements CarrierRepository {

    private final CarrierDao carrierDao;
    private final CarrierConverter converter = CarrierConverter.INSTANCE;

    @Override
    public Carrier findById(Long id) {
        CarrierPO carrierPO = carrierDao.selectById(id);
        return converter.toEntity(carrierPO);
    }

    @Override
    public Carrier findByCode(String code) {
        LambdaQueryWrapper<CarrierPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarrierPO::getCode, code);
        CarrierPO carrierPO = carrierDao.selectOne(queryWrapper);
        return converter.toEntity(carrierPO);
    }

    @Override
    public List<Carrier> findAllActive() {
        LambdaQueryWrapper<CarrierPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CarrierPO::getIsActive, true);
        List<CarrierPO> carrierPOs = carrierDao.selectList(queryWrapper);
        return converter.toEntityList(carrierPOs);
    }

    @Override
    public Carrier save(Carrier carrier) {
        CarrierPO carrierPO = converter.toPO(carrier);
        if (carrierPO.getId() == null) {
            carrierDao.insert(carrierPO);
        } else {
            carrierDao.updateById(carrierPO);
        }
        // 返回包含ID的实体
        return converter.toEntity(carrierPO);
    }

    @Override
    public boolean deleteById(Long id) {
        // 实际项目中可能是逻辑删除，这里使用物理删除示例
        return carrierDao.deleteById(id) > 0;
    }

    @Override
    public List<Carrier> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<CarrierPO> carrierPOs = carrierDao.selectBatchIds(ids);
        return converter.toEntityList(carrierPOs);
    }
} 