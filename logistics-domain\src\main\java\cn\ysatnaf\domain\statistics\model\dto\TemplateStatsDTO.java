package cn.ysatnaf.domain.statistics.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class TemplateStatsDTO {
    private Integer templateType; // 原始模板类型（1-5）
    private Long quantity;
    private BigDecimal amount;

    public TemplateStatsDTO add(Long addQty, BigDecimal addAmt) {
        this.quantity = (quantity == null ? 0L : quantity) + addQty;
        this.amount = (amount == null ? BigDecimal.ZERO : amount).add(addAmt);
        return this;
    }
}