package cn.ysatnaf.domain.cost.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CostRuleEntity {
    private Long id;

    private Integer platformType;

    private BigDecimal floor;

    private BigDecimal ceil;

    private BigDecimal cost;

    private BigDecimal throwRatio;
}
