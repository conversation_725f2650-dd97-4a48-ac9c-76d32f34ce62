package cn.ysatnaf.domain.manifest.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.ysatnaf.domain.manifest.model.dto.WayBillImageGenDTO;
import cn.ysatnaf.domain.manifest.model.entity.SawagaSiteCodeEntity;
import cn.ysatnaf.domain.sawaga.service.SawagaSiteService;
import cn.ysatnaf.types.exception.ServiceException;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Component
public class JapaneseShippingLabelGenerator {

    private static final int LABEL_WIDTH = 1000;
    private static final int LABEL_HEIGHT = 1500;
    private static final Color BACKGROUND_COLOR = Color.WHITE;
    private static final Color TEXT_COLOR = Color.BLACK;
    private static final Color BOX_COLOR = Color.BLACK;
    private static final Color HIGHLIGHT_COLOR = Color.RED;
    private static final Font NORMAL_FONT = new Font("MS Gothic", Font.PLAIN, 25);
    private static final Font LARGE_FONT = new Font("MS Gothic", Font.PLAIN, 60);
    private static final Font VERY_LARGE_FONT = new Font("MS Gothic", Font.PLAIN, 100);
    private static final Font MEDIUM_FONT = new Font("MS Gothic", Font.PLAIN, 40);
    private static final Font SMALL_FONT = new Font("MS Gothic", Font.PLAIN, 20);

    private BufferedImage image;
    private Graphics2D g2d;

    private final SawagaSiteService sawagaSiteService;

    public JapaneseShippingLabelGenerator(SawagaSiteService sawagaSiteService) {
        this.sawagaSiteService = sawagaSiteService;
        // Create a new blank image with white background
        image = new BufferedImage(LABEL_WIDTH, LABEL_HEIGHT, BufferedImage.TYPE_INT_RGB);
        g2d = image.createGraphics();

        // Set high quality rendering
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);

        // Fill background
        g2d.setColor(BACKGROUND_COLOR);
        g2d.fillRect(0, 0, LABEL_WIDTH, LABEL_HEIGHT);

    }

    public void generateLabel(WayBillImageGenDTO info) {
        drawHeader(info);
        drawRecipientSection(info);
        drawTrackingNumberSection(info);
        drawSenderSection();
        drawQRAndBarCodeSection(info);
        drawWeightBarcodes();
        drawItemDetailsSection(info);
        drawBottomBarcode(info);
//        // Draw border
//        g2d.setColor(BOX_COLOR);
//        g2d.setStroke(new BasicStroke(2));
//        g2d.drawRect(5, 5, LABEL_WIDTH - 10, LABEL_HEIGHT - 10);
    }

    private void drawHeader(WayBillImageGenDTO info) {
        List<SawagaSiteCodeEntity> sawagaSiteCode = sawagaSiteService.getByZipCode(info.getReceiverZipCode());
        if (CollUtil.isEmpty(sawagaSiteCode)) {
            throw new ServiceException("未查到邮编【" + info.getReceiverZipCode() + "】对应的店铺番号");
        }
        SawagaSiteCodeEntity sawagaSiteCodeEntity = sawagaSiteCode.get(0);
        // Draw barcode for division code
        drawBarcode(810, 120, sawagaSiteCodeEntity.acquireBarcodeCode(), BarcodeFormat.CODABAR, 400, 100, false);

        // Draw shipping date and quantity section
        g2d.setColor(BOX_COLOR);
        g2d.drawRect(15, 30, 960, 160);

        g2d.setFont(NORMAL_FONT);
        // 格式化日期
        g2d.drawString("発送日：" + DateUtil.format(LocalDateTime.now(), "yy年MM月dd日"), 40, 60);
        g2d.setFont(MEDIUM_FONT);
        g2d.drawString("個数：" + 1, 40, 110);
        g2d.setFont(NORMAL_FONT);
        g2d.drawString("便種：" + "陸便", 40, 150);

        // Draw large division code
//        g2d.setFont(VERY_LARGE_FONT);
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 100));
        g2d.drawString(sawagaSiteCodeEntity.getCodeOne(), 320, 160);
//        g2d.setFont(LARGE_FONT);
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 60));
        g2d.drawString(sawagaSiteCodeEntity.getCodeTwo(), 520, 165);
    }

    private void drawRecipientSection(WayBillImageGenDTO info) {
        // Draw recipient labels
        g2d.setColor(BOX_COLOR);
        g2d.drawRect(15, 190, 960, 250);
        g2d.drawRect(15, 190, 40, 250);
        g2d.setFont(NORMAL_FONT);
        g2d.drawString("お", 25, 285);
        g2d.drawString("届", 25, 315);
        g2d.drawString("先", 25, 345);

        // Draw recipient address box
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 35));
        g2d.drawString("〒 " + info.getFormattedZipCode() + " TEL: " + info.getReceiverPhone(), 60, 225);

        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 30));
        g2d.drawString(info.getReceiverAddress(), 60, 280);

        // Draw recipient name box
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 38));
        g2d.drawString(info.getReceiverName() + " 様", 60, 400);
    }

    private void drawTrackingNumberSection(WayBillImageGenDTO info) {
        // Draw tracking number barcode
        drawBarcode(740, 550, "d" + info.getSawagaNumber() + "d", BarcodeFormat.CODABAR, 600, 100, false);
        // Draw tracking number section
        g2d.setColor(BOX_COLOR);
        g2d.drawRect(15, 440, 960, 230);
        g2d.drawRect(15, 440, 40, 230);

        g2d.setFont(NORMAL_FONT);
        g2d.drawString("お", 25, 480);
        g2d.drawString("問", 25, 510);
        g2d.drawString("合", 25, 540);
        g2d.drawString("番", 25, 570);
        g2d.drawString("号", 25, 600);


        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 35));
        // trackingNumber每4个数字中间加一个-
        StringBuilder trackingNumberSb = new StringBuilder();
        for (int i = 0; i < info.getSawagaNumber().length(); i++) {
            trackingNumberSb.append(info.getSawagaNumber().charAt(i));
            if (i % 4 == 3 && i != info.getSawagaNumber().length() - 1) {
                trackingNumberSb.append("-");
            }
        }
        g2d.drawString(trackingNumberSb.toString(), 620, 640);
    }

    private void drawSenderSection() {
        // Draw sender section
        g2d.setColor(BOX_COLOR);
        g2d.drawRect(15, 670, 960, 180);
        g2d.drawRect(15, 670, 40, 180);
        g2d.setFont(NORMAL_FONT);
        g2d.drawString("ご", 25, 700);
        g2d.drawString("依", 25, 730);
        g2d.drawString("頼", 25, 760);
        g2d.drawString("主", 25, 790);

        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 30));
        g2d.drawString("大阪府泉南市新家2049合同会", 60, 700);
        g2d.drawString("社E-Trade", 60, 735);

        g2d.drawRect(515, 670, 460, 60);
        g2d.drawRect(515, 670, 225, 60);
        g2d.drawRect(515, 730, 460, 120);
        g2d.drawString("佐川急便(株)", 550, 715);
        g2d.drawString("りんくう営業所", 760, 715);
        g2d.drawString("お問合せTEL " + "0570-01-0604", 560, 780);
        g2d.drawString("顧客コード " + "154274360247", 560, 820);
    }

    private void drawQRAndBarCodeSection(WayBillImageGenDTO info) {
//        OrderIdGenerator generator = new OrderIdGenerator(1);
//        String orderId = generator.generate();
        String orderId = info.getSawagaNumber();
        drawQRCode(125, 960, orderId);
        drawBarcode(680, 920, orderId, BarcodeFormat.CODE_128, 600, 120, false);

        // Draw QR code section
        g2d.drawRect(15, 850, 960, 180);
        g2d.drawRect(15, 850, 40, 180);
        g2d.drawRect(15, 850, 400, 180);

        g2d.setFont(NORMAL_FONT);
        g2d.drawString("記", 25, 900);
        g2d.drawString("事", 25, 930);
        g2d.drawString("欄", 25, 960);
        // Draw JS tracking barcode section
        g2d.drawString(orderId, 60, 880);
        g2d.drawString(orderId, 600, 1010);
    }

    private void drawItemDetailsSection(WayBillImageGenDTO info) {
        // Draw item details section
        g2d.setColor(BOX_COLOR);
        g2d.drawRect(15, 1030, 960, 350);
        g2d.drawRect(15, 1030, 300, 350);
        g2d.drawRect(15, 1030, 660, 350);

        // Draw weight barcodes section
        g2d.setFont(NORMAL_FONT);
        g2d.drawString("品名：", 350, 1080);
        g2d.drawString(info.getItemEnNames(), 360, 1110);
    }

    private void drawWeightBarcodes() {
        // Draw weight barcodes on the left and right sides
        g2d.setColor(BOX_COLOR);

        // Left side weight barcodes
        g2d.setFont(SMALL_FONT);
        drawBarcode(150, 1090, "D002B", BarcodeFormat.CODABAR, 200, 60, false);
        g2d.drawString("2kg(サイズ 60)", 70, 1145);

        drawBarcode(150, 1190, "D005B", BarcodeFormat.CODABAR, 200, 60, false);
        g2d.drawString("5kg(サイズ 80)", 70, 1245);

        drawBarcode(150, 1290, "D010B", BarcodeFormat.CODABAR, 200, 60, false);
        g2d.drawString("10kg(サイズ 100)", 70, 1345);

        // Right side weight barcodes
        drawBarcode(830, 1150, "D020B", BarcodeFormat.CODABAR, 200, 60, false);
        g2d.drawString("20kg(サイズ 140)", 750, 1205);

        drawBarcode(830, 1250, "D030B", BarcodeFormat.CODABAR, 200, 60, false);
        g2d.drawString("30kg(サイズ 160)", 750, 1305);
    }

    private void drawBottomBarcode(WayBillImageGenDTO info) {
        // Draw bottom barcode
        drawBarcode(500, 1430, info.getOrderNo(), BarcodeFormat.CODE_128, 900, 40, false);
    }

    // Simulated barcode drawing method
    private void drawBarcode(int x, int y, String data, BarcodeFormat barcodeFormat, int width, int height, boolean isVertical) {
        try {
            MultiFormatWriter writer = new MultiFormatWriter();
            BitMatrix bitMatrix;

            if (isVertical) {
                bitMatrix = writer.encode(data, barcodeFormat, height, width);
            } else {
                bitMatrix = writer.encode(data, barcodeFormat, width, height);
            }

            BufferedImage barcodeImage = MatrixToImageWriter.toBufferedImage(bitMatrix);

            if (isVertical) {
                // Rotate image for vertical barcodes
                barcodeImage = rotateImage(barcodeImage, 90);
            }

            g2d.drawImage(barcodeImage, x - barcodeImage.getWidth() / 2, y - barcodeImage.getHeight() / 2, null);
        } catch (Exception e) {
            System.err.println("Error generating barcode: " + e.getMessage());
        }
    }

    // Simulated QR code drawing method
    private void drawQRCode(int x, int y, String data) {
        try {
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(data, BarcodeFormat.QR_CODE, 160, 160);
            BufferedImage qrImage = MatrixToImageWriter.toBufferedImage(bitMatrix);
            g2d.drawImage(qrImage, x - qrImage.getWidth() / 2, y - qrImage.getHeight() / 2, null);
        } catch (Exception e) {
            System.err.println("Error generating QR code: " + e.getMessage());
        }
    }

    private BufferedImage rotateImage(BufferedImage image, double degrees) {
        double radians = Math.toRadians(degrees);
        double sin = Math.abs(Math.sin(radians));
        double cos = Math.abs(Math.cos(radians));

        int newWidth = (int) Math.floor(image.getWidth() * cos + image.getHeight() * sin);
        int newHeight = (int) Math.floor(image.getHeight() * cos + image.getWidth() * sin);

        BufferedImage rotated = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = rotated.createGraphics();

        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.translate((newWidth - image.getWidth()) / 2, (newHeight - image.getHeight()) / 2);
        g2d.rotate(radians, image.getWidth() / 2, image.getHeight() / 2);
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();

        return rotated;
    }
}
