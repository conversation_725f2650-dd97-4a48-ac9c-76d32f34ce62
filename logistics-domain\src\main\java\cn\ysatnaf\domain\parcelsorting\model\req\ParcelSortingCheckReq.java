package cn.ysatnaf.domain.parcelsorting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Schema(description = "装箱检测入参")
@Data
public class ParcelSortingCheckReq {

    @Schema(description = "物流单号")
    @NotBlank
    private String trackingNumber;

    @Schema(description = "提单ID")
    private Long masterBillId;
}
