package cn.ysatnaf.domain.item.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 物品名称映射 领域实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ItemNameMapping {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 原始物品名称 (包含违禁词, 作为查找键)
     */
    private String originalName;

    /**
     * 映射后的合规物品名称
     */
    private String mappedName;

    /**
     * 此映射是否启用 (TRUE: 启用, FALSE: 禁用)
     */
    private Boolean isActive;

    /**
     * 此映射被应用的次数
     */
    private Integer usageCount;

    /**
     * 最后应用时间
     */
    private LocalDateTime lastUsedTime;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 最后更新者ID
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

} 