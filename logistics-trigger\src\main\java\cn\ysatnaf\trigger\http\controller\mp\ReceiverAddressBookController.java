package cn.ysatnaf.trigger.http.controller.mp;

import cn.ysatnaf.domain.address.model.entity.ReceiverAddressBookEntity;
import cn.ysatnaf.domain.address.service.ReceiverAddressBookService;
import cn.ysatnaf.trigger.http.req.address.ReceiverAddressBookAddReq;
import cn.ysatnaf.trigger.http.req.address.ReceiverAddressBookDeleteReq;
import cn.ysatnaf.trigger.http.req.address.ReceiverAddressBookPageReq;
import cn.ysatnaf.trigger.http.req.address.ReceiverAddressBookUpdateReq;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;

/**
 * AddressBookController
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 10:17
 */
@Tag(name = "收件人地址簿相关")
@RequestMapping("/mp/receiverAddressBook")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class ReceiverAddressBookController {

    private final ReceiverAddressBookService receiverAddressBookService;

    @PostMapping("/add")
    @PermitAll
    @Operation(summary = "添加地址")
    public CommonResult<Boolean> add(@RequestBody @Validated ReceiverAddressBookAddReq req) {
        return CommonResult.success(receiverAddressBookService.add(req.getOpenid(), req.getReceiverAreaId(), req.getAddressDetail(), req.getName(), req.getPhone(), req.getIsDefault()));
    }

    @PostMapping("/delete")
    @PermitAll
    @Operation(summary = "删除地址")
    public CommonResult<Boolean> delete(@RequestBody @Validated ReceiverAddressBookDeleteReq req) {
        return CommonResult.success(receiverAddressBookService.delete(req.getId()));
    }

    @PostMapping("/update")
    @PermitAll
    @Operation(summary = "更新地址")
    public CommonResult<Boolean> update(@RequestBody @Validated ReceiverAddressBookUpdateReq req) {
        return CommonResult.success(receiverAddressBookService.update(req.getId(),
                req.getReceiverAreaId(),
                req.getOpenid(),
                req.getAddressDetail(),
                req.getName(),
                req.getPhone(),
                req.getIsDefault()));
    }

    @PostMapping("/getDefault/{openid}")
    @PermitAll
    @Operation(summary = "获取默认收件人地址")
    public CommonResult<ReceiverAddressBookEntity> getDefault(@PathVariable("openid") String openid) {
        return CommonResult.success(receiverAddressBookService.getDefault(openid));
    }

    @PostMapping("/page")
    @PermitAll
    @Operation(summary = "分页查询地址簿")
    public CommonResult<PageResult<ReceiverAddressBookEntity>> page(@RequestBody @Validated ReceiverAddressBookPageReq req) {
        return CommonResult.success(receiverAddressBookService.page(req.getOpenid(), req.getPageNo(), req.getPageSize()));
    }
}
