<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ysatnaf.infrastructure.persistent.dao.trackingnumber.TrackingNumberPoolDao">

    <!-- 批量更新单号状态和分配批次ID -->
    <update id="updateStatusAndAllocationBatchByIds">
        UPDATE tracking_number_pool
        SET
          status = #{status},
          allocation_batch_id = #{allocationBatchId},
          update_time = NOW()
        WHERE id IN
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <!-- 基础 ResultMap -->
    <resultMap id="BaseResultMap" type="cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberPoolPO">
        <id column="id" property="id"/>
        <result column="tracking_number" property="trackingNumber"/>
        <result column="channel_id" property="channelId"/>
        <result column="status" property="status"/>
        <result column="import_batch_id" property="importBatchId"/>
        <result column="allocation_batch_id" property="allocationBatchId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 基础列列表 -->
    <sql id="Base_Column_List">
        id, tracking_number, channel_id, status, import_batch_id, allocation_batch_id, 
        create_time, update_time
    </sql>

    <!-- 批量插入指定列 -->
    <insert id="insertBatchSomeColumn" parameterType="java.util.List">
        INSERT INTO tracking_number_pool (tracking_number, channel_id, status, import_batch_id, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.trackingNumber,jdbcType=VARCHAR},
            #{item.channelId,jdbcType=BIGINT},
            #{item.status,jdbcType=INTEGER},
            #{item.importBatchId,jdbcType=BIGINT},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <!-- 根据渠道ID和单号列表查询存在的单号 -->
    <select id="selectExistingNumbers" resultType="java.lang.String">
        SELECT tracking_number
        FROM tracking_number_pool
        WHERE channel_id = #{channelId,jdbcType=BIGINT}
          AND tracking_number IN
            <foreach collection="trackingNumbers" item="tn" open="(" separator="," close=")">
                #{tn,jdbcType=VARCHAR}
            </foreach>
    </select>

    <!-- 新增：批量查询各渠道可用数量 -->
    <select id="countAvailableByChannelIds" resultType="java.util.Map">
        SELECT
            channel_id as channelId,
            COUNT(*) as availableCount
        FROM
            tracking_number_pool
        WHERE
            status = #{availableStatus}
          AND channel_id IN
            <foreach item="item" collection="channelIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
            channel_id
    </select>

</mapper> 