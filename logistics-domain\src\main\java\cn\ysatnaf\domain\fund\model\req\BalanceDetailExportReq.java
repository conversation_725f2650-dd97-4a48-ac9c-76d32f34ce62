package cn.ysatnaf.domain.fund.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> Hang
 */

@Schema(description = "查看资金明细 入参")
@Data
public class BalanceDetailExportReq {

    @Schema(description = "管理员指定用户ID，不传为自己，非管理员只能查看自己")
    private Long userId;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
