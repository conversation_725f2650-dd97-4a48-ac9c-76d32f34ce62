package cn.ysatnaf.domain.fund.repository;

import cn.ysatnaf.domain.fund.model.entity.BalanceDetailEntity;
import cn.ysatnaf.types.common.PageResult;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
public interface BalanceDetailRepository {
    PageResult<BalanceDetailEntity> pageByAccountId(Long accountId, Integer pageNo, Integer pageSize);

    void insert(BalanceDetailEntity balanceDetailEntity);

    List<BalanceDetailEntity> listByAccountId(Long accountId, Date startTime, Date endTime);
}
