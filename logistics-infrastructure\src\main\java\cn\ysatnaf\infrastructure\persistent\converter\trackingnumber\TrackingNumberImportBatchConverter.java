package cn.ysatnaf.infrastructure.persistent.converter.trackingnumber;

import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberImportBatch;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberImportBatchPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 单号导入批次记录对象转换器
 * <AUTHOR>
 */
@Mapper
public interface TrackingNumberImportBatchConverter {

    TrackingNumberImportBatchConverter INSTANCE = Mappers.getMapper(TrackingNumberImportBatchConverter.class);

    TrackingNumberImportBatchPO toPO(TrackingNumberImportBatch batch);

    TrackingNumberImportBatch toEntity(TrackingNumberImportBatchPO batchPO);
    
    List<TrackingNumberImportBatch> toEntityList(List<TrackingNumberImportBatchPO> batchPOs);
    
    List<TrackingNumberImportBatchPO> toPOList(List<TrackingNumberImportBatch> batches);

} 