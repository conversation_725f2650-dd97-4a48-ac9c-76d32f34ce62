package cn.ysatnaf.domain.trackingnumber.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 渠道可用单号数量 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "渠道可用单号数量信息")
public class ChannelAvailableCountDTO {

    @Schema(description = "渠道ID")
    private Long channelId;

    @Schema(description = "渠道代码")
    private String channelCode;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "承运商ID")
    private Long carrierId;

    @Schema(description = "承运商名称")
    private String carrierName;

    @Schema(description = "地点ID")
    private Long locationId;

    @Schema(description = "地点名称")
    private String locationName;

    @Schema(description = "货物类型ID")
    private Long shipmentTypeId;

    @Schema(description = "货物类型名称")
    private String shipmentTypeName;

    @Schema(description = "当前可用单号数量")
    private Long availableCount;
} 