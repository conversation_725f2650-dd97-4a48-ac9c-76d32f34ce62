package cn.ysatnaf.domain.item.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "违禁关键词 DTO")
public class ProhibitedItemKeywordDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "违禁关键词或短语")
    private String keyword;

    @Schema(description = "是否启用", defaultValue = "true")
    private Boolean isActive;

    @Schema(description = "备注信息")
    private String remarks;

    @Schema(description = "创建者ID")
    private Long creatorId;

    @Schema(description = "最后更新者ID")
    private Long updaterId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 