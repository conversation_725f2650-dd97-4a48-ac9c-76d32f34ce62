package cn.ysatnaf.domain.item.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Schema(description = "更新违禁关键词请求")
public class ProhibitedItemKeywordUpdateReq {

    @NotNull(message = "ID不能为空")
    @Schema(description = "主键ID", required = true)
    private Long id;

    @Size(max = 100, message = "关键词长度不能超过100个字符")
    @Schema(description = "违禁关键词或短语 (可选，不传则不修改)", example = "Lithium Battery")
    private String keyword;

    @Schema(description = "是否启用 (可选，不传则不修改)", example = "false")
    private Boolean isActive;

    @Schema(description = "备注信息 (可选，不传则不修改)", example = "更新航空规定")
    private String remarks;
} 