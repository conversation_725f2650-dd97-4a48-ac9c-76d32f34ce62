package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.order.model.entity.OrderEntity;
import cn.ysatnaf.domain.order.model.res.OrderSearchRes;
import cn.ysatnaf.infrastructure.persistent.po.OrderPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrderConverter {

    OrderConverter INSTANCE = Mappers.getMapper(OrderConverter.class);

    OrderPO entity2po(OrderEntity orderEntity);

    OrderEntity po2entity(OrderPO orderPO);

    List<OrderEntity> entityList2poList(List<OrderPO> orderPOList);

    List<OrderEntity> po2entityList(List<OrderPO> records);
}
