package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingBoxPO;
import cn.ysatnaf.domain.parcelsorting.repository.ParcelSortingDetailRepository;
import cn.ysatnaf.infrastructure.persistent.dao.ParcelSortingDetailDao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Hang
 */
@RequiredArgsConstructor
@Repository
public class ParcelSortingDetailRepositoryImpl implements ParcelSortingDetailRepository {

    private final ParcelSortingDetailDao parcelSortingDetailDao;

    @Override
    public List<ParcelSortingBoxPO> listByRecordId(Long recordId) {
        LambdaQueryWrapper<ParcelSortingBoxPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingBoxPO::getRecordId, recordId);
        return parcelSortingDetailDao.selectList(wrapper);
    }

    @Override
    public void create(ParcelSortingBoxPO parcelSortingBoxPO) {
        parcelSortingDetailDao.insert(parcelSortingBoxPO);
    }

    @Override
    public ParcelSortingBoxPO getById(Long id) {
        return parcelSortingDetailDao.selectById(id);
    }

    @Override
    public void updateById(ParcelSortingBoxPO detail) {
        parcelSortingDetailDao.updateById(detail);
    }

    @Override
    public void delete(Long id) {
        parcelSortingDetailDao.deleteById(id);
    }

    @Override
    public Integer countById(Long boxId) {

        LambdaQueryWrapper<ParcelSortingBoxPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingBoxPO::getId, boxId);
        return Math.toIntExact(parcelSortingDetailDao.selectCount(wrapper));
    }

    @Override
    public List<ParcelSortingBoxPO> listByIds(Set<Long> boxIds) {
        return parcelSortingDetailDao.selectBatchIds(boxIds);
    }
}
