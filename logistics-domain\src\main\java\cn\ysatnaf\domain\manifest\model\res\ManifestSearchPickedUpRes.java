package cn.ysatnaf.domain.manifest.model.res;

import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.MasterBill;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 运单高级查询响应DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "运单高级查询响应")
public class ManifestSearchPickedUpRes {

    @Schema(description = "运单ID")
    private Long id;

    @Schema(description = "预报单号")
    private String expressNumber;

    @Schema(description = "国际物流单号")
    private String sawagaNumber;

    @Schema(description = "提单号")
    private String masterBillNumber;

    @Schema(description = "货物类型")
    private Integer cargoType;

    @Schema(description = "目的地")
    private Integer destination;

    @Schema(description = "重量(kg)")
    private BigDecimal weight;

    @Schema(description = "长(cm)")
    private BigDecimal length;

    @Schema(description = "宽(cm)")
    private BigDecimal width;

    @Schema(description = "高(cm)")
    private BigDecimal height;

    @Schema(description = "揽件时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pickUpTime;

    @Schema(description = "揽件人")
    private String pickUpBy;


    /**
     * 将Manifest实体转换为ManifestAdvancedSearchRes对象
     */
    public static ManifestSearchPickedUpRes from(Manifest manifest, Map<Long, UserEntity> userIdMap, Map<Long, MasterBill> masterBillIdMap) {
        int cargoType;
        int destination;
        if (manifest.getShippingFeeTemplateType() <= 3 || manifest.getShippingFeeTemplateType() == 6) {
            cargoType = manifest.getShippingFeeTemplateType();
            destination = 1;
        } else {
            if (manifest.getShippingFeeTemplateType() == 4) {
                cargoType = 1;
            } else {
                cargoType = 3;
            }
            destination = 2;
        }

        return ManifestSearchPickedUpRes.builder()
                .id(manifest.getId())
                .expressNumber(manifest.getExpressNumber())
                .sawagaNumber(manifest.getSawagaNumber())
                .masterBillNumber(masterBillIdMap.getOrDefault(manifest.getMasterBillId(), new MasterBill()).getMasterBillNumber())
                .cargoType(cargoType)
                .destination(destination)
                .weight(manifest.getWeight())
                .length(manifest.getLength())
                .width(manifest.getWidth())
                .height(manifest.getHeight())
                .pickUpTime(manifest.getPickUpTime())
                .pickUpBy(userIdMap.getOrDefault(manifest.getPickUpBy(), new UserEntity()).getNickname())
                .build();
    }
} 