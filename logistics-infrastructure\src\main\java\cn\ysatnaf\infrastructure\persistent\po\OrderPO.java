package cn.ysatnaf.infrastructure.persistent.po;

import cn.ysatnaf.domain.order.model.valobj.OrderStatusVO;
import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Hang
 */
@Data
@TableName("tb_order")
public class OrderPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String openid;

    /**
     * 快递单号（佐川单号）
     */
    private String expressNo;

    /**
     * 系统内部订单号
     */
    private String orderNo;

    /**
     * 快递员ID
     */
    private Long courierId;

    /**
     * 物品名称
     */
    private String itemName;

    /**
     * 物品数量
     */
    private Integer itemQuantity;

    /**
     * 寄件人名字
     */
    private String senderName;

    /**
     * 寄件人性别
     */
    private Integer senderSex;

    /**
     * 寄件人手机号
     */
    private String senderPhone;

    /**
     * 寄件人省份编码
     */
    private Long senderProvinceCode;
    /**
     * 寄件人省份名称
     */
    private String senderProvinceName;

    /**
     * 寄件人城市编码
     */
    private Long senderCityCode;

    /**
     * 寄件人城市名称
     */
    private String senderCityName;

    /**
     * 寄件人区县编码
     */
    private Long senderDistrictCode;

    /**
     * 寄件人区县名称
     */
    private String senderDistrictName;

    /**
     * 寄件人街道编码
     */
    private Long senderStreetCode;

    /**
     * 寄件人街道名称
     */
    private String senderStreetName;

    /**
     * 寄件人居委/社区编码
     */
    private Long committeeCode;

    /**
     * 寄件人居委/社区名称
     */
    private String committeeName;

    /**
     * 寄件人详细地址
     */
    private String senderAddressDetail;

    /**
     * 收件人地区编码（邮编）
     */
    private Long receiverAreaId;

    /**
     * 收件人
     * 日本都道府县级名称
     */
    private String receiverPrefectureName;
    /**
     * 收件人
     * 日本市区町村级名称
     */
    private String receiverMunicipalName;
    /**
     * 收件人
     * 日本丁目级名称
     */
    private String receiverLocalitiesName;

    /**
     * 收件人
     * 日本都道府县级名称(英文)
     */
    private String receiverPrefectureEnName;
    /**
     * 收件人
     * 日本市区町村级名称(英文)
     */
    private String receiverMunicipalEnName;
    /**
     * 收件人
     * 日本丁目级名称(英文)
     */
    private String receiverLocalitiesEnName;

    /**
     * 收件人地址详情
     */
    private String receiverAddressDetail;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人手机号
     */
    private String receiverPhone;

    /**
     * 订单状态 {@link OrderStatusVO#getCode()}
     */
    private Integer status;

    private LocalDateTime paymentTime;
}
