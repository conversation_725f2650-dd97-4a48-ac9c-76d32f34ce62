package cn.ysatnaf.domain.fund.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Hang
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BalanceDetailExcel {

    @ExcelProperty("操作")
    private String operation;

    @ExcelProperty("金额变动")
    private BigDecimal changeAmount;

    @ExcelProperty("余额")
    private BigDecimal balance;

    @ExcelProperty("快递单号")
    private String expressNumber;

    @ExcelProperty("佐川号码")
    private String sawagaNumber;

    @ExcelProperty("转单号")
    private String transferredTrackingNumber;

    @ExcelProperty("商店订单号")
    private String orderNumber;

    @ExcelProperty("品名")
    private String itemName;

    @ExcelProperty("邮编")
    private String receiverZipCode;

    @ExcelProperty("收件人")
    private String receiverName;

    @ExcelProperty("收件地址")
    private String receiverAddress;

    @ExcelProperty("收件人电话")
    private String receiverPhone;

    @ExcelProperty("重量")
    private BigDecimal weight;

    @ExcelProperty("长")
    private BigDecimal length;

    @ExcelProperty("宽")
    private BigDecimal width;

    @ExcelProperty("高")
    private BigDecimal height;

    @ExcelProperty("体积重量")
    private BigDecimal dimensionWeight;

    @ExcelProperty("偏远费")
    private BigDecimal remoteAreaSurcharge;

    @ExcelProperty("超长费")
    private BigDecimal overLengthSurcharge;

    @ExcelProperty("其他费用名称")
    private String otherCostName;

    @ExcelProperty("其他费用")
    private BigDecimal otherCost;

    @ExcelProperty("运费类型")
    private String templateType;

    @ExcelProperty("基本费用")
    private BigDecimal cost;

    @ExcelProperty("总计")
    private BigDecimal totalCost;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("预报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    @ExcelProperty("发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shipmentTime;
}
