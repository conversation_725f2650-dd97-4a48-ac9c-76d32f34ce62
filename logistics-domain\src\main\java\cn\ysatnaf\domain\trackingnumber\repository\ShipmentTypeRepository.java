package cn.ysatnaf.domain.trackingnumber.repository;

import cn.ysatnaf.domain.trackingnumber.model.entity.ShipmentType;

import java.util.Collection;
import java.util.List;

/**
 * 货物类型仓库接口
 * <AUTHOR>
 */
public interface ShipmentTypeRepository {

    /**
     * 根据ID查询货物类型
     * @param id 货物类型ID
     * @return 货物类型实体，如果不存在则返回null
     */
    ShipmentType findById(Long id);
    
    /**
     * 根据代码查询货物类型
     * @param code 货物类型代码
     * @return 货物类型实体，如果不存在则返回null
     */
    ShipmentType findByCode(String code);

    /**
     * 查询所有启用的货物类型
     * @return 启用的货物类型列表
     */
    List<ShipmentType> findAllActive();

    /**
     * 保存货物类型（新增或更新）
     * @param shipmentType 货物类型实体
     * @return 保存后的货物类型实体（可能包含生成的ID）
     */
    ShipmentType save(ShipmentType shipmentType);

    /**
     * 根据ID删除货物类型（通常是逻辑删除）
     * @param id 货物类型ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID列表批量查询货物类型
     * @param ids ID 集合
     * @return 货物类型列表
     */
    List<ShipmentType> findByIds(Collection<Long> ids);

} 