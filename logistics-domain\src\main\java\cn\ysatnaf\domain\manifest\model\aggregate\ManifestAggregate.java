package cn.ysatnaf.domain.manifest.model.aggregate;

import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> Hang
 * 运单聚合对象
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManifestAggregate {

    private String expressNumber;

    private Manifest manifest;

    private List<ManifestItem> manifestItems;
}
