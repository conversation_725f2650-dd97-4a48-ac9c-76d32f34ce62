package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.infrastructure.persistent.po.item.ProhibitedItemKeywordPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 违禁关键词表 DAO 接口
 */
@Mapper // 确保 Spring Boot 能扫描到
public interface ProhibitedItemKeywordDao extends BaseMapper<ProhibitedItemKeywordPO> {
    // BaseMapper 已提供常用 CRUD 方法
    // 如有特殊 SQL 需求，可在此处添加自定义方法及其 @Select/@Insert/@Update/@Delete 注解或对应的 XML 实现
} 