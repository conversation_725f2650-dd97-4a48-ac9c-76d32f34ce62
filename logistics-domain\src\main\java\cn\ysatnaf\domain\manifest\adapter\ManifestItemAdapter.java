package cn.ysatnaf.domain.manifest.adapter;

import cn.ysatnaf.domain.manifest.model.dto.ManifestItemOrderDTO;
import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ManifestItemAdapter {
    ManifestItemAdapter INSTANCE = Mappers.getMapper(ManifestItemAdapter.class);

    ManifestItemOrderDTO entity2OrderDTO(ManifestItem manifestItem);
}
