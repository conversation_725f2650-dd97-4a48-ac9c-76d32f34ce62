package cn.ysatnaf.domain.manifest.service.impl;

import java.awt.*;
import java.awt.image.BufferedImage;

import cn.ysatnaf.domain.manifest.model.dto.WayBillImageGenDTO;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;

public class ShippingLabelGenerator {

    private final Graphics2D g2d;
    private BufferedImage image;
    private final SortCodeService sortCodeService;
    private final DivisionCodeService divisionCodeService;

    public ShippingLabelGenerator(SortCodeService sortCodeService, DivisionCodeService divisionCodeService) {
        // Create the image
        int multiplier = 350;
        int width = 3 * multiplier;
        int height = 2 * multiplier;
        this.image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();

        // Set background to white
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width, height);

        // Draw grid lines for guidance
        g2d.setColor(new Color(230, 230, 230));
        for (int i = 0; i < width; i += 100) {
            g2d.drawLine(i, 0, i, height);
        }
        for (int i = 0; i < height; i += 100) {
            g2d.drawLine(0, i, width, i);
        }

        // Setup for drawing
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        this.g2d = g2d;
        this.sortCodeService = sortCodeService;
        this.divisionCodeService = divisionCodeService;
    }

    public BufferedImage generate(WayBillImageGenDTO dto) {
        drawLeftTopBox(dto);
        drawCenterTopBox(dto);
        drawRightTopBox(dto);
        drawOrderNumberBox(dto);
        drawLargeBarcode(dto);
        drawSenderInfoBox(dto);
        drawReturnAddressBox();
        drawCustomCodeBox();
        // Dispose the graphics context
        g2d.dispose();
        this.image = rotateImage(this.image, 90);
        return this.image;
    }

    private void drawLeftTopBox(WayBillImageGenDTO dto) {
        
        // Draw inner box for "料金後納"
        g2d.setColor(Color.BLACK);
        g2d.drawRect(45, 70, 220, 60);
        g2d.fillRect(45, 130, 220, 60);

        // Draw text for "料金後納"
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 32));
        g2d.drawString("料金後納", 85, 110);

        // Draw text for "ゆうパケット"
        g2d.setColor(Color.WHITE);
        g2d.drawString("ゆうパケット", 60, 170);

        // 查询分拣码
        String sortCode = sortCodeService.getSortCode(dto.getReceiverZipCode());
        if (sortCode == null) {
            throw new RuntimeException("邮编【" +dto.getReceiverZipCode() + "】分拣码不存在");
        }
        // Draw "44-50-42"
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 48));
        // 分拣码按照每两个数字中间用一个 - 分隔
        StringBuilder sortCodeSb = new StringBuilder();
        for (int i = 0; i < sortCode.length(); i++) {
            sortCodeSb.append(sortCode.charAt(i));
            if (i % 2 == 1 && i != sortCode.length() - 1) {
                sortCodeSb.append("-");
            }
        }
        g2d.drawString(sortCodeSb.toString(), 60, 240);

        // Draw a barcode
        g2d.setColor(Color.BLACK);
        drawBarcode(167, 350, sortCode, BarcodeFormat.CODE_128,270, 120, false);
    }
    
    private void drawCenterTopBox(WayBillImageGenDTO dto) {
        String formattedZipCode = dto.getFormattedZipCode();
        // 邮编每个字符之间用空格隔开
        StringBuilder zipCodeSb = new StringBuilder();
        for (int i = 0; i < formattedZipCode.length(); i++) {
            zipCodeSb.append(formattedZipCode.charAt(i));
            if (i != formattedZipCode.length() - 1) {
                zipCodeSb.append(" ");
            }
        }

        // Draw tracking number
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 52));
        g2d.drawString("〒 " + zipCodeSb, 450, 130);
        
        // Draw address
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 30));
        g2d.drawString(dto.getReceiverAddress(), 390, 200);
        
        // Draw recipient
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 30));
        g2d.drawString(dto.getReceiverName(), 390, 290);

        // Draw "様"
        g2d.setColor(Color.BLACK);
        g2d.drawString("様", 900, 290);
    }
    
    private void drawRightTopBox(WayBillImageGenDTO dto) {
        // Draw "D"
        // 获取邮编前两位
        String prefix = dto.getReceiverZipCode().substring(0, 2);
        String divisionCode = divisionCodeService.getDivisionCode(prefix);
        if (divisionCode == null) {
            throw new RuntimeException("邮编【" +dto.getReceiverZipCode() + "】区分号不存在");
        }
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 90));
        // 查询区分号
        g2d.drawString(divisionCode, 970, 140);
    }
    
    private void drawOrderNumberBox(WayBillImageGenDTO dto) {
        // Draw order number
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 22));
        g2d.drawString(dto.getOrderNo(), 360, 350);
    }
    
    private void drawLargeBarcode(WayBillImageGenDTO dto) {
        // Draw barcode
        g2d.setColor(Color.BLACK);
        drawBarcode( 250, 520, "A" + dto.getSawagaNumber() + "A", BarcodeFormat.CODABAR,520, 120, false);
        
        // Draw barcode number
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 30));
        g2d.drawString("A" + dto.getSawagaNumber() + "A", 140, 620);
    }
    
    private void drawSenderInfoBox(WayBillImageGenDTO dto) {

        // Draw sender info
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 24));
        g2d.drawString("【差出人】        〒598-0024", 580, 460);
        
        // Draw sender address
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 24));
        g2d.drawString("大阪府泉佐野市上之郷4315-1", 590, 500);
    }
    
    private void drawReturnAddressBox() {
        // Draw text
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 24));
        g2d.drawString("【返還先】", 580, 540);
    }
    
    private void drawCustomCodeBox() {
        // Draw code
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("MS Gothic", Font.PLAIN, 24));
        g2d.drawString("FJBM", 590, 625);
    }

    private void drawBarcode(int x, int y, String data, BarcodeFormat barcodeFormat, int width, int height, boolean isVertical) {
        try {
            MultiFormatWriter writer = new MultiFormatWriter();
            BitMatrix bitMatrix;

            if (isVertical) {
                bitMatrix = writer.encode(data, barcodeFormat, height, width);
            } else {
                bitMatrix = writer.encode(data, barcodeFormat, width, height);
            }

            BufferedImage barcodeImage = MatrixToImageWriter.toBufferedImage(bitMatrix);

            if (isVertical) {
                // Rotate image for vertical barcodes
                barcodeImage = rotateImage(barcodeImage, 90);
            }

            g2d.drawImage(barcodeImage, x - barcodeImage.getWidth() / 2, y - barcodeImage.getHeight() / 2, null);
        } catch (Exception e) {
            System.err.println("Error generating barcode: " + e.getMessage());
        }
    }

    private BufferedImage rotateImage(BufferedImage image, double degrees) {
        double radians = Math.toRadians(degrees);
        double sin = Math.abs(Math.sin(radians));
        double cos = Math.abs(Math.cos(radians));

        int newWidth = (int) Math.floor(image.getWidth() * cos + image.getHeight() * sin);
        int newHeight = (int) Math.floor(image.getHeight() * cos + image.getWidth() * sin);

        BufferedImage rotated = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = rotated.createGraphics();

        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.translate((newWidth - image.getWidth()) / 2, (newHeight - image.getHeight()) / 2);
        g2d.rotate(radians, image.getWidth() / 2, image.getHeight() / 2);
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();

        return rotated;
    }
}