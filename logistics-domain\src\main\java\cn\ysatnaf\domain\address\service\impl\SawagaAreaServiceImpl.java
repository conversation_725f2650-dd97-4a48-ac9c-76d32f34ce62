package cn.ysatnaf.domain.address.service.impl;

import cn.ysatnaf.domain.address.model.entity.SawagaAreaEntity;
import cn.ysatnaf.domain.address.repository.SawagaAreaRepository;
import cn.ysatnaf.domain.address.service.SawagaAreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class SawagaAreaServiceImpl implements SawagaAreaService {

    private final SawagaAreaRepository sawagaAreaRepository;

    @Override
    public SawagaAreaEntity getByPrefectureName(String prefectureName) {
        return sawagaAreaRepository.getByPrefectureName(prefectureName);
    }
}
