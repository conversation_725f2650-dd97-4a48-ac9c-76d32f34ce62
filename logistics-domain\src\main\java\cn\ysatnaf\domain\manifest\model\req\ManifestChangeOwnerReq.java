package cn.ysatnaf.domain.manifest.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "归属划转入参")
@Data
public class ManifestChangeOwnerReq {

    @NotEmpty
    @Schema(description = "要划转的订单ID")
    private List<Long> manifestIds;

    @Schema(description = "要划转到的用户ID")
    private Long toUserId;
}
