package cn.ysatnaf.types.util;

import cn.ysatnaf.types.exception.ServiceException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class OSSUtil {

    private static final String BUCKET_NAME = "zebra-logistics-files";
    
    // 分片上传的阈值：100MB
    private static final long MULTIPART_UPLOAD_THRESHOLD = 100 * 1024 * 1024L;
    
    // 分片大小：10MB
    private static final long PART_SIZE = 10 * 1024 * 1024L;

    private final OSS oss;

    public PutObjectResult upload(String objectName, ByteArrayInputStream byteArrayInputStream) {
        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET_NAME, objectName, byteArrayInputStream);
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 上传文件。
            return oss.putObject(putObjectRequest);
        } catch (OSSException oe) {
            log.error("Error Message:{}", oe.getErrorMessage());
            log.error("Error Code:{}", oe.getErrorCode());
            log.error("Request ID:{}", oe.getRequestId());
            log.error("Host ID:{}", oe.getHostId());
            throw new ServiceException("系统异常，请联系管理员");
        }
    }

    /**
     * 上传文件到OSS（支持大文件，避免内存溢出）
     * @param objectName OSS对象名称
     * @param file 要上传的文件
     * @return 上传结果
     */
    public PutObjectResult upload(String objectName, File file) {
        try {
            if (!file.exists()) {
                throw new ServiceException("文件不存在: " + file.getPath());
            }
            
            long fileSize = file.length();
            log.info("开始上传文件到OSS - 文件名: {}, 本地路径: {}, 大小: {} bytes ({:.2f} MB)", 
                    objectName, file.getPath(), fileSize, fileSize / 1024.0 / 1024.0);
            
            // 记录上传开始时间
            long startTime = System.currentTimeMillis();
            
            PutObjectResult result;
            
            // 如果文件大于阈值，使用分片上传
            if (fileSize > MULTIPART_UPLOAD_THRESHOLD) {
                log.info("文件大小超过{}MB，使用分片上传", MULTIPART_UPLOAD_THRESHOLD / 1024 / 1024);
                result = multipartUpload(objectName, file);
            } else {
                log.info("使用普通上传方式");
                result = simpleUpload(objectName, file);
            }
            
            long uploadTime = System.currentTimeMillis() - startTime;
            double speedMBps = fileSize / 1024.0 / 1024.0 / (uploadTime / 1000.0);
            
            log.info("OSS上传成功 - 文件名: {}, 用时: {}ms, 上传速度: {:.2f} MB/s, ETag: {}", 
                    objectName, uploadTime, speedMBps, result.getETag());
            
            return result;
        } catch (OSSException oe) {
            log.error("OSS上传失败 - 文件名: {}, Error Message: {}, Error Code: {}, Request ID: {}, Host ID: {}", 
                    objectName, oe.getErrorMessage(), oe.getErrorCode(), oe.getRequestId(), oe.getHostId());
            throw new ServiceException("[OSS错误] " + oe.getErrorCode() + ": " + oe.getErrorMessage());
        } catch (Exception e) {
            log.error("OSS上传失败 - 文件名: {}, 异常信息: {}", objectName, e.getMessage(), e);
            throw new ServiceException("OSS上传异常: " + e.getMessage());
        }
    }
    
    /**
     * 普通上传
     */
    private PutObjectResult simpleUpload(String objectName, File file) {
        PutObjectRequest putObjectRequest = new PutObjectRequest(BUCKET_NAME, objectName, file);
        return oss.putObject(putObjectRequest);
    }
    
    /**
     * 分片上传
     */
    private PutObjectResult multipartUpload(String objectName, File file) {
        try {
            // 初始化分片上传
            InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(BUCKET_NAME, objectName);
            InitiateMultipartUploadResult result = oss.initiateMultipartUpload(request);
            String uploadId = result.getUploadId();
            
            log.info("开始分片上传 - 上传ID: {}, 分片大小: {}MB", uploadId, PART_SIZE / 1024 / 1024);
            
            // 计算分片数量
            long fileSize = file.length();
            int partCount = (int) ((fileSize - 1) / PART_SIZE + 1);
            
            List<PartETag> partETags = Collections.synchronizedList(new ArrayList<>());
            
            // 上传分片
            for (int i = 0; i < partCount; i++) {
                long startPos = i * PART_SIZE;
                long curPartSize = Math.min(PART_SIZE, fileSize - startPos);
                int partNumber = i + 1;
                
                log.info("上传分片 {}/{}, 起始位置: {}, 大小: {} bytes", partNumber, partCount, startPos, curPartSize);
                
                // 使用FileInputStream读取指定位置的数据
                try (FileInputStream fileInputStream = new FileInputStream(file)) {
                    // 跳过前面的数据到指定位置
                    fileInputStream.skip(startPos);
                    
                    UploadPartRequest uploadPartRequest = new UploadPartRequest();
                    uploadPartRequest.setBucketName(BUCKET_NAME);
                    uploadPartRequest.setKey(objectName);
                    uploadPartRequest.setUploadId(uploadId);
                    uploadPartRequest.setPartNumber(partNumber);
                    uploadPartRequest.setInputStream(fileInputStream);
                    uploadPartRequest.setPartSize(curPartSize);
                    
                    UploadPartResult uploadPartResult = oss.uploadPart(uploadPartRequest);
                    partETags.add(uploadPartResult.getPartETag());
                    
                    // 记录进度
                    double progress = (double) partNumber / partCount * 100;
                    log.info("分片上传进度: {:.1f}% ({}/{})", progress, partNumber, partCount);
                }
            }
            
            // 完成分片上传
            CompleteMultipartUploadRequest completeRequest = new CompleteMultipartUploadRequest(
                    BUCKET_NAME, objectName, uploadId, partETags);
            CompleteMultipartUploadResult completeResult = oss.completeMultipartUpload(completeRequest);
            
            log.info("分片上传完成 - ETag: {}", completeResult.getETag());
            
            // 构造返回结果
            PutObjectResult putObjectResult = new PutObjectResult();
            putObjectResult.setETag(completeResult.getETag());
            return putObjectResult;
            
        } catch (Exception e) {
            log.error("分片上传失败", e);
            throw new ServiceException("分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 测试OSS连接
     * @return 连接是否正常
     */
    public boolean testConnection() {
        try {
            log.info("开始测试OSS连接...");
            long startTime = System.currentTimeMillis();
            
            // 尝试列举bucket信息来测试连接
            oss.getBucketInfo(BUCKET_NAME);
            
            long testTime = System.currentTimeMillis() - startTime;
            log.info("OSS连接测试成功，用时: {}ms", testTime);
            return true;
        } catch (Exception e) {
            log.error("OSS连接测试失败", e);
            return false;
        }
    }
}
