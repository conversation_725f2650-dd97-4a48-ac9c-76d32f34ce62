package cn.ysatnaf.domain.manifest.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 主提单导出海关文件请求DTO
 * <AUTHOR>
 */
@Data
public class MasterBillExportReq {

    /**
     * 主提单ID(必填)
     */
    @NotNull(message = "主提单ID不能为空")
    private Long masterBillId;

    /**
     * 目的地 1-东京 2-大阪(必填)
     */
    @NotNull(message = "目的地不能为空")
    private Integer destination;

    /**
     * 运费模板类型列表(必填)
     * 1-普通 2-带电 3-投函
     */
    @NotNull(message = "运费模板类型不能为空")
    private List<Integer> shippingFeeTemplateTypes;

    /**
     * 航班日期(必填)
     */
    @NotNull(message = "航班日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime flightDate;

    /**
     * 航班号(必填)
     */
    @NotNull(message = "航班号不能为空")
    private String flightNumber;

    /**
     * 提单号
     */
    private String ladingBill;
} 