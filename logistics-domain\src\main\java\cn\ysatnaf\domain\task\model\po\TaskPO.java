package cn.ysatnaf.domain.task.model.po;

import cn.ysatnaf.domain.po.BasePO;
import cn.ysatnaf.domain.task.model.vo.TaskStatus;
import cn.ysatnaf.domain.task.model.vo.TaskType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_task")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TaskPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 提交任务的用户ID
     */
    private Long userId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态，如："PENDING", "IN_PROGRESS", "COMPLETED", "FAILED"
     * {@link TaskStatus#name()}
     */
    private String status;

    /**
     * 任务类型 {@link TaskType#name()}
     */
    private String taskType;

    /**
     * 任务参数
     */
    private String taskParam;

    /**
     * 任务进度，0到100
     */
    private Double progress;

    /**
     * 任务状态信息，如错误信息或进度描述
     */
    private String message;

    /**
     * 文件下载链接
     */
    private String downloadUrl;
}
