package cn.ysatnaf.infrastructure.persistent.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
@TableName("tb_manifest_item")
@Data
public class ManifestItemPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属运单ID
     */
    private Long manifestId;

    /**
     * 物品名称
     */
    private String name;

    /**
     * 物品英文名称
     */
    private String nameEn;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 随机生成
     */
    private BigDecimal value;
}
