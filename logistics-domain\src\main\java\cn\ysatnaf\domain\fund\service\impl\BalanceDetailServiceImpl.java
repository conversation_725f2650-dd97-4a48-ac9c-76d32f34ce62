package cn.ysatnaf.domain.fund.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.domain.fund.adapter.BalanceDetailAdapter;
import cn.ysatnaf.domain.fund.model.entity.BalanceDetailEntity;
import cn.ysatnaf.domain.fund.model.excel.BalanceDetailExcel;
import cn.ysatnaf.domain.fund.model.req.BalanceDetailExportReq;
import cn.ysatnaf.domain.fund.model.req.BalanceDetailPageReq;
import cn.ysatnaf.domain.fund.model.res.BalanceDetailPageRes;
import cn.ysatnaf.domain.fund.model.res.FundAccountGetRes;
import cn.ysatnaf.domain.fund.repository.BalanceDetailRepository;
import cn.ysatnaf.domain.fund.service.BalanceDetailService;
import cn.ysatnaf.domain.fund.service.FundAccountService;
import cn.ysatnaf.domain.manifest.adapter.ManifestAdapter;
import cn.ysatnaf.domain.manifest.model.dto.ManifestOrderDTO;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import cn.ysatnaf.domain.manifest.service.ManifestItemService;
import cn.ysatnaf.domain.manifest.service.ManifestService;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hang
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BalanceDetailServiceImpl implements BalanceDetailService {

    private final BalanceDetailRepository balanceDetailRepository;

    private final ManifestService manifestService;

    private final UserService userService;

    private final ManifestItemService manifestItemService;

    @Override
    public PageResult<BalanceDetailPageRes> pageBalanceDetails(BalanceDetailPageReq req) {
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        Long userId;
        if (loginUser.ifSuperAdmin() && ObjUtil.isNotNull(req.getUserId())) {
            userId = req.getUserId();
        } else {
            userId = loginUser.getId();
        }
        // 通过userId获取accountId
        FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
        FundAccountGetRes fundAccountGetRes = fundAccountService.getByUserId(userId);
        PageResult<BalanceDetailEntity> pageResult = balanceDetailRepository.pageByAccountId(fundAccountGetRes.getId(), req.getPageNo(), req.getPageSize());
        List<BalanceDetailEntity> balanceDetails = pageResult.getList();
        if (CollUtil.isEmpty(balanceDetails)) {
            return PageResult.empty();
        }
        List<Long> manifestIds = balanceDetails.stream().map(BalanceDetailEntity::getManifestId).collect(Collectors.toList());
        List<Manifest> manifests = manifestService.listByIds(manifestIds);
        List<ManifestOrderDTO> manifestOrderDTOS = ManifestAdapter.INSTANCE.entity2OrderDTOList(manifests);
        Map<Long, ManifestOrderDTO> manifestIdMap = manifestOrderDTOS.stream().collect(Collectors.toMap(ManifestOrderDTO::getId, manifest -> manifest));
        List<BalanceDetailPageRes> balanceDetailPageRes = BalanceDetailAdapter.INSTANCE.toBalanceDetailPageResList(balanceDetails);
        balanceDetailPageRes.forEach(res -> res.setManifest(manifestIdMap.get(res.getManifestId())));
        return new PageResult<>(balanceDetailPageRes, pageResult.getTotal());
    }

    @Override
    public void record(Long accountId, BigDecimal originalBalance, BigDecimal newBalance, String remark) {
        BigDecimal changeAmount = newBalance.subtract(originalBalance);
        BalanceDetailEntity balanceDetailEntity = new BalanceDetailEntity();
        balanceDetailEntity.setAccountId(accountId);
        balanceDetailEntity.setChangeAmount(changeAmount);
        balanceDetailEntity.setOriginalBalance(originalBalance);
        balanceDetailEntity.setNewBalance(newBalance);
        balanceDetailEntity.setRemark(remark);
        balanceDetailEntity.setOperatorId(LoginUserHolder.getLoginUser().getId());
        balanceDetailEntity.setOperator(LoginUserHolder.getLoginUser().getUsername());
        balanceDetailRepository.insert(balanceDetailEntity);
    }

    @Override
    public void record(Long accountId, BigDecimal originalBalance, BigDecimal newBalance, String remark, Long manifestId) {
        BigDecimal changeAmount = newBalance.subtract(originalBalance);
        BalanceDetailEntity balanceDetailEntity = new BalanceDetailEntity();
        balanceDetailEntity.setAccountId(accountId);
        balanceDetailEntity.setChangeAmount(changeAmount);
        balanceDetailEntity.setOriginalBalance(originalBalance);
        balanceDetailEntity.setNewBalance(newBalance);
        balanceDetailEntity.setRemark(remark);
        balanceDetailEntity.setManifestId(manifestId);
        balanceDetailEntity.setOperatorId(LoginUserHolder.getLoginUser().getId());
        balanceDetailEntity.setOperator(LoginUserHolder.getLoginUser().getUsername());
        balanceDetailRepository.insert(balanceDetailEntity);
    }

    @Override
    public void exportBalanceDetails(BalanceDetailExportReq req, HttpServletResponse response) {
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        Long userId;
        if (loginUser.ifSuperAdmin() && ObjUtil.isNotNull(req.getUserId())) {
            userId = req.getUserId();
        } else {
            userId = loginUser.getId();
        }
        // 通过userId获取accountId
        FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
        FundAccountGetRes fundAccountGetRes = fundAccountService.getByUserId(userId);
        Long accountId = fundAccountGetRes.getId();
        List<BalanceDetailEntity> balanceDetailEntities = balanceDetailRepository.listByAccountId(accountId, req.getStartTime(), req.getEndTime());
        if (CollUtil.isEmpty(balanceDetailEntities)) {
            throw new ServiceException("当前账户没有账户明细");
        }
        List<Long> manifestIds = balanceDetailEntities.stream()
                .map(BalanceDetailEntity::getManifestId)
                .filter(ObjUtil::isNotNull)
                .collect(Collectors.toList());

        List<Manifest> manifests = new ArrayList<>();
        if (CollUtil.isNotEmpty(manifestIds)) {
            manifests = manifestService.listByIds(manifestIds);
        }
        List<ManifestItem> manifestItems = manifestItemService.listBatchByManifestIds(manifestIds);
        Map<Long, List<ManifestItem>> manifestIdItemMap = manifestItems.stream().collect(Collectors.groupingBy(ManifestItem::getManifestId));
        Map<Long, Manifest> manifestIdMap = manifests.stream().collect(Collectors.toMap(Manifest::getId, manifest -> manifest));

        List<BalanceDetailExcel> excels = balanceDetailEntities.stream().map(entity -> {
            Manifest manifest = manifestIdMap.getOrDefault(entity.getManifestId(), new Manifest());
            List<ManifestItem> manifestItems1 = manifestIdItemMap.getOrDefault(entity.getManifestId(), new ArrayList<>());
            boolean negative = entity.getChangeAmount().compareTo(BigDecimal.ZERO) < 0;
            ShippingFeeTemplateTypeEnum shippingFeeTemplateTypeEnum = ShippingFeeTemplateTypeEnum.getByCode(manifest.getShippingFeeTemplateType());
            return BalanceDetailExcel.builder()
                    .operation(entity.getRemark())
                    .changeAmount(entity.getChangeAmount())
                    .balance(entity.getNewBalance())
                    .expressNumber(manifest.getExpressNumber())
                    .sawagaNumber(manifest.getSawagaNumber())
                    .transferredTrackingNumber(manifest.getTransferredTrackingNumber())
                    .orderNumber(manifest.getOrderNumber())
                    .itemName(manifestItems1.stream().map(ManifestItem::getName).collect(Collectors.joining(",")))
                    .receiverZipCode(manifest.getReceiverZipCode())
                    .receiverName(manifest.getReceiverName())
                    .receiverAddress(manifest.getReceiverAddress())
                    .receiverPhone(manifest.getReceiverPhone())
                    .weight(manifest.getWeight())
                    .length(manifest.getLength())
                    .width(manifest.getWidth())
                    .height(manifest.getHeight())
                    .templateType(shippingFeeTemplateTypeEnum.getShortName())
                    .dimensionWeight(manifest.getDimensionalWeight())
                    .remoteAreaSurcharge(negative && manifest.getRemoteAreaSurcharge() != null ? manifest.getRemoteAreaSurcharge().negate() : manifest.getRemoteAreaSurcharge())
                    .overLengthSurcharge(negative && manifest.getOverLengthSurcharge() != null ? manifest.getOverLengthSurcharge().negate() : manifest.getOverLengthSurcharge())
                    .otherCostName(StrUtil.isNotBlank(manifest.getOtherCostName()) ? manifest.getOtherCostName() + ":" : manifest.getOtherCostName())
                    .otherCost(negative && manifest.getOtherCost() != null ? manifest.getOtherCost().negate() : manifest.getOtherCost())
                    .cost(negative && manifest.getCost() != null ? manifest.getCost().negate() : manifest.getCost())
                    .totalCost(negative &&  manifest.getTotalCost() != null ? manifest.getTotalCost().negate() : manifest.getTotalCost())
                    .remark(manifest.getRemark())
                    .time(manifest.getCreateTime())
                    .shipmentTime(manifest.getShipmentTime())
                    .build();
        }).collect(Collectors.toList());
        try (InputStream inputStream = ResourceUtil.getStream("templates/BalanceDetailTemplate.xlsx")) {
            UserEntity user = userService.getById(userId);
            Map<String, Object> map = new HashMap<>();
            map.put("nickname", user.getNickname());
            map.put("exportTime", DateUtil.formatDateTime(new Date()));

            // 处理数据 (此处是业务需要，可以自行更改)
            //获取模板 (我是将模板放到 resource文件下面了)
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = user.getNickname() + "对账单明细 ";
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncodeUtil.encode(fileName) + ".xlsx");

            // 生成sheet
            WriteSheet sheet1 = EasyExcel.writerSheet(0, "明细").build();
            ExcelWriter write = EasyExcel.write(response.getOutputStream(), BalanceDetailExcel.class)
                    .withTemplate(inputStream)
                    .build();
            // 填充数据
            write.fill(excels, sheet1);
            write.fill(map, sheet1);
            write.close();
        } catch (Exception e) {
            log.error("导出明细失败：", e);
            throw new ServiceException("导出明细失败，请联系管理员");
        }
    }
}
