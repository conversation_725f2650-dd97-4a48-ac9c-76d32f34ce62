package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.infrastructure.persistent.po.item.ItemNameMappingPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;

/**
 * 物品名称映射表 DAO 接口
 */
@Mapper
public interface ItemNameMappingDao extends BaseMapper<ItemNameMappingPO> {

    /**
     * 增加使用次数并更新最后使用时间
     * (使用 @Update 注解示例，也可以在 XML 中实现)
     * @param id 映射ID
     * @param lastUsedTime 最后使用时间
     * @return 更新的行数
     */
    @Update("UPDATE item_name_mappings SET usage_count = usage_count + 1, last_used_time = #{lastUsedTime} WHERE id = #{id}")
    int incrementUsageCountAndUpdateTime(@Param("id") Long id, @Param("lastUsedTime") LocalDateTime lastUsedTime);

} 