package cn.ysatnaf.domain.statistics.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MonthlyQuantityDTO {
    private Integer total;
    private Double monthOnMonthChange;
    private Integer dailyAvg;
    private Double monthOnMonthAvgChange; // 新增平均日运单环比变化
    private PeakDayDTO peakDay;
    private List<MonthTrendDTO> monthlyTrend;
    private TemplateDistributionDTO templateDistribution;
    private RegionalDistributionDTO regionalDistribution;
}