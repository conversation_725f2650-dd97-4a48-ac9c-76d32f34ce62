package cn.ysatnaf.domain.manifest.service;

import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.Tracking;
import cn.ysatnaf.domain.tracking.model.dto.TrackingDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Hang
 */
public interface TrackingService {
    List<String> querySawagaTracking(List<String> sawagaNumbers);

    void insertBatch(List<Tracking> trackingToSave);

    void save(Tracking tracking);

    List<Tracking> getByExpressNumber(String manifestId);

    List<Tracking> getByManifestId(Long manifestId);

    void queryJpCustomsTracking(List<Manifest> list);

    /**
     * 查询日本邮便物流轨迹
     * @param expressNumbers 快递单号
     * @return 物流轨迹信息
     */
    List<TrackingDTO> queryJapanPostTracking(List<String> expressNumbers);

    /**
     * 根据运单IDs查询轨迹
     * @param manifestIds 运单ID列表
     * @return 轨迹列表
     */
    List<Tracking> getByManifestIds(List<Long> manifestIds);

    /**
     * 查询黑猫物流轨迹
     *
     * @param expressNumbers 物流单号集合
     * @return 物流轨迹
     */
    Map<String, List<TrackingDTO>> queryNekoposuLogisticsJourney(List<String> expressNumbers);
}
