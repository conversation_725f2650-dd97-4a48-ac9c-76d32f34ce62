package cn.ysatnaf.domain.address.repository;

import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;

import java.util.List;

/**
 * ReceiverAreaRepository
 *
 * <AUTHOR>
 * @date 2023/12/22 14:04
 */
public interface ReceiverAreaRepository {
    List<ReceiverAreaEntity> getByZipCode(String zipCode);

    List<ReceiverAreaEntity> findAll();

    ReceiverAreaEntity getById(Long receiverAreaId);
}
