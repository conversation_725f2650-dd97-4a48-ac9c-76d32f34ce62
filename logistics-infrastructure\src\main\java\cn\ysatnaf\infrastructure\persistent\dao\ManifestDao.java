package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.domain.statistics.model.dto.*;
import cn.ysatnaf.domain.statistics.model.vo.SummaryVO;
import cn.ysatnaf.domain.statistics.model.vo.TodayVO;
import cn.ysatnaf.infrastructure.persistent.po.ManifestPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Hang
 */
@Mapper
public interface ManifestDao extends BaseMapper<ManifestPO> {

    void insertBatchSomeColumn(@Param("list") List<ManifestPO> manifestPOList);

    void updateBatchById(@Param("list") List<ManifestPO> toPOList);

    List<ManifestPO> selectContainsItemName(@Param("status") Integer status,
                                            @Param("userId") Long userId,
                                            @Param("itemName") String itemName,
                                            @Param("isDelete") Boolean isDelete,
                                            @Param("expressNumber") String expressNumber,
                                            @Param("sawagaNumber") String sawagaNumber,
                                            @Param("orderNumber") String orderNumber,
                                            @Param("orderNo") String orderNo,
                                            @Param("receiverName") String receiverName,
                                            @Param("createTimeStart") LocalDateTime createTimeStart,
                                            @Param("createTimeEnd") LocalDateTime createTimeEnd,
                                            @Param("pickUpTimeStart") LocalDateTime pickUpTimeStart,
                                            @Param("pickUpTimeEnd") LocalDateTime pickUpTimeEnd,
                                            @Param("shipmentTimeStart") LocalDateTime shipmentTimeStart,
                                            @Param("shipmentTimeEnd") LocalDateTime shipmentTimeEnd,
                                            @Param("offset") Integer offset,
                                            @Param("limit") Integer limit);

    Integer countContainsItemName(@Param("status") Integer status,
                                  @Param("userId") Long userId,
                                  @Param("itemName") String itemName,
                                  @Param("isDelete") Boolean isDelete,
                                  @Param("expressNumber") String expressNumber,
                                  @Param("sawagaNumber") String sawagaNumber,
                                  @Param("orderNumber") String orderNumber,
                                  @Param("orderNo") String orderNo,
                                  @Param("receiverName") String receiverName,
                                  @Param("createTimeStart") LocalDateTime createTimeStart,
                                  @Param("createTimeEnd") LocalDateTime createTimeEnd,
                                  @Param("pickUpTimeStart") LocalDateTime pickUpTimeStart,
                                  @Param("pickUpTimeEnd") LocalDateTime pickUpTimeEnd,
                                  @Param("shipmentTimeStart") LocalDateTime shipmentTimeStart,
                                  @Param("shipmentTimeEnd") LocalDateTime shipmentTimeEnd);

    // 总统计（不限制时间）
    @Select("SELECT COUNT(*) as quantity, " +
            "COALESCE(SUM(" +
            "   COALESCE(cost, 0) + " +
            "   COALESCE(over_length_surcharge, 0) + " +
            "   COALESCE(remote_area_surcharge, 0) + " +
            "   COALESCE(other_cost, 0)" +
            "), 0) as amount " +
            "FROM tb_manifest WHERE status >= 2 AND is_delete = 0")
    @Results({
            @Result(column = "quantity", property = "quantity"),
            @Result(column = "amount", property = "amount")
    })
    SummaryVO.TotalData selectTotalSummary();

    // 时间段统计
    @Select("<script>" +
            "SELECT COUNT(*) as quantity, " +
            "COALESCE(SUM(" +
            "   COALESCE(cost, 0) + " +
            "   COALESCE(over_length_surcharge, 0) + " +
            "   COALESCE(remote_area_surcharge, 0) + " +
            "   COALESCE(other_cost, 0)" +
            "), 0) as amount " +
            "FROM tb_manifest " +
            "WHERE status >= 2 AND is_delete = 0 " +
            "AND pick_up_time BETWEEN #{start} AND #{end} " +
            "</script>")
    @Results({
            @Result(column = "quantity", property = "quantity"),
            @Result(column = "amount", property = "amount")
    })
    SummaryVO.CurrentMonthData selectPeriodSummary(
            @Param("start") LocalDateTime start,
            @Param("end") LocalDateTime end);

    @Select("SELECT " +
            "COUNT(*) as quantity, " +
            "COALESCE(SUM(" +
            "   COALESCE(cost, 0) + " +
            "   COALESCE(over_length_surcharge, 0) + " +
            "   COALESCE(remote_area_surcharge, 0) + " +
            "   COALESCE(other_cost, 0)" +
            "), 0) as amount " +
            "FROM tb_manifest " +
            "WHERE status >= 2 AND is_delete = 0 " +
            "AND pick_up_time BETWEEN #{start} AND #{end}")
    @Results({
            @Result(column = "quantity", property = "quantity"),
            @Result(column = "amount", property = "amount", jdbcType = JdbcType.DECIMAL)
    })
    TodayVO selectTodaySummary(
            @Param("start") LocalDateTime start,
            @Param("end") LocalDateTime end
    );

    @Select("<script>" +
            "SELECT shipping_fee_template_type as templateType, " +
            "COUNT(*) as quantity, " +
            "COALESCE(SUM(" +
            "   COALESCE(cost, 0) + " +
            "   COALESCE(over_length_surcharge, 0) + " +
            "   COALESCE(remote_area_surcharge, 0) + " +
            "   COALESCE(other_cost, 0)" +
            "), 0) as amount " +
            "FROM tb_manifest " +
            "WHERE status >= 2 AND is_delete = 0 " +
            "<if test='start != null'> AND pick_up_time >= #{start} </if>" +
            "<if test='end != null'> AND pick_up_time &lt;= #{end} </if>" +
            "GROUP BY shipping_fee_template_type" +
            "</script>")
    @Results({
            @Result(property = "templateType", column = "templateType"),
            @Result(property = "quantity", column = "quantity"),
            @Result(property = "amount", column = "amount")
    })
    List<TemplateStatsDTO> selectTemplateStats(
            @Param("start") LocalDateTime start,
            @Param("end") LocalDateTime end
    );

    List<CompanyRankingDTO> selectCompanyRanking(@Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate,
                                                 @Param("limit") Integer limit);

    List<AmountDistributionDTO> selectAmountDistribution();

    List<TrendDTO> selectTrendData(
            @Param("startHour") Integer startHour,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );


    // 基础统计
    @Select("<script>" +
            "SELECT COUNT(id) AS total, " +
            "  FLOOR(COUNT(id)/TIMESTAMPDIFF(DAY, #{start}, #{end})) AS dailyAvg " +
            "FROM tb_manifest " +
            "WHERE is_delete = 0 AND status >= 2 " +
            "AND pick_up_time BETWEEN #{start} AND #{end} " +
            "</script>")
    Map<String, Object> selectBaseStats(@Param("start") LocalDateTime start,
                                        @Param("end") LocalDateTime end);

    // 峰值日查询
    @Select("<script>" +
            "SELECT DATE(pick_up_time - INTERVAL #{startHour} HOUR) AS peakDate, " +
            "  COUNT(id) AS quantity " +
            "FROM tb_manifest " +
            "WHERE is_delete = 0 AND status >= 2 " +
            "AND pick_up_time BETWEEN #{start} AND #{end} " +
            "GROUP BY DATE(pick_up_time - INTERVAL #{startHour} HOUR) " +
            "ORDER BY quantity DESC LIMIT 1" +
            "</script>")
    Map<String, Object> selectPeakDay(@Param("start") LocalDateTime start,
                                      @Param("end") LocalDateTime end,
                                      @Param("startHour") Integer startHour);

    // 模板类型分布
    @Select("<script>" +
            "SELECT " +
            "  CASE " +
            "    WHEN shipping_fee_template_type IN (1,4) THEN '普通模板' " +
            "    WHEN shipping_fee_template_type = 2 THEN '带电模板' " +
            "    WHEN shipping_fee_template_type IN (3,5) THEN '投函模板' " +
            "  END AS type, " +
            "  COUNT(id) AS quantity, " +
            "  ROUND(COUNT(id)*100/(SELECT COUNT(id) FROM tb_manifest " +
            "    WHERE is_delete = 0 AND status >= 2 " +
            "    AND pick_up_time BETWEEN #{start} AND #{end}), 1) AS ratio " +
            "FROM tb_manifest " +
            "WHERE is_delete = 0 AND status >= 2 " +
            "AND pick_up_time BETWEEN #{start} AND #{end} " +
            "GROUP BY type" +
            "</script>")
    List<TemplateCategoryDTO> selectTemplateDistribution(@Param("start") LocalDateTime start,
                                                         @Param("end") LocalDateTime end);

    // 区域分布
    @Select("<script>" +
            "SELECT " +
            "  CASE " +
            "    WHEN shipping_fee_template_type IN (1,2,3) THEN '东京' " +
            "    WHEN shipping_fee_template_type IN (4,5) THEN '大阪' " +
            "  END AS area, " +
            "  CASE " +
            "    WHEN shipping_fee_template_type IN (1,4) THEN '普通模板' " +
            "    WHEN shipping_fee_template_type = 2 THEN '带电模板' " +
            "    WHEN shipping_fee_template_type IN (3,5) THEN '投函模板' " +
            "  END AS type, " +
            "  COUNT(id) AS quantity " +
            "FROM tb_manifest " +
            "WHERE is_delete = 0 AND status >= 2 " +
            "AND pick_up_time BETWEEN #{start} AND #{end} " +
            "GROUP BY area, type" +
            "</script>")
    List<Map<String, Object>> selectRegionalDistribution(@Param("start") LocalDateTime start,
                                                         @Param("end") LocalDateTime end);

    // 趋势数据（近12个月）
    @Select("<script>" +
            "SELECT " +
            "  DATE_FORMAT(DATE(pick_up_time - INTERVAL #{startHour} HOUR), '%Y-%m') AS month, " +
            "  COUNT(id) AS quantity " +
            "FROM tb_manifest " +
            "WHERE is_delete = 0 AND status >= 2 " +
            "AND pick_up_time BETWEEN #{start} AND #{end} " +
            "GROUP BY DATE_FORMAT(DATE(pick_up_time - INTERVAL #{startHour} HOUR), '%Y-%m') " +
            "ORDER BY month LIMIT 12" +
            "</script>")
    List<MonthTrendDTO> selectTwelveTrend(@Param("start") LocalDateTime start,
                                          @Param("end") LocalDateTime end,
                                          @Param("startHour") Integer startHour);


    // 基础金额统计
    @Select("SELECT " +
            "  SUM(total_amount) AS total, " +
            "  SUM(total_amount)/TIMESTAMPDIFF(DAY, #{start}, #{end}) AS dailyAvg " +
            "FROM (" +
            "  SELECT " +
            "    COALESCE(cost,0) + COALESCE(over_length_surcharge,0) + " +
            "    COALESCE(remote_area_surcharge,0) + COALESCE(other_cost,0) AS total_amount " +
            "  FROM tb_manifest " +
            "  WHERE is_delete = 0 AND status >= 2 " +
            "  AND pick_up_time BETWEEN #{start} AND #{end}" +
            ") AS t")
    Map<String, Object> selectAmountStats(@Param("start") LocalDateTime start,
                                          @Param("end") LocalDateTime end);

    // 峰值日查询
    @Select("SELECT DATE(pick_up_time - INTERVAL #{startHour} HOUR) AS peak_date, " +
            "  SUM(total_amount) AS amount " +
            "FROM (" +
            "  SELECT pick_up_time, " +
            "    COALESCE(cost,0) + COALESCE(over_length_surcharge,0) + " +
            "    COALESCE(remote_area_surcharge,0) + COALESCE(other_cost,0) AS total_amount " +
            "  FROM tb_manifest " +
            "  WHERE is_delete = 0 AND status >= 2 " +
            "  AND pick_up_time BETWEEN #{start} AND #{end}" +
            ") AS t " +
            "GROUP BY peak_date " +
            "ORDER BY amount DESC LIMIT 1")
    Map<String, Object> selectPeakAmountDay(@Param("start") LocalDateTime start,
                                            @Param("end") LocalDateTime end,
                                            @Param("startHour") Integer startHour);

    // 模板类型金额分布
    @Select("SELECT " +
            "  CASE WHEN shipping_fee_template_type IN (1,4) THEN '普通模板' " +
            "       WHEN shipping_fee_template_type = 2 THEN '带电模板' " +
            "       WHEN shipping_fee_template_type IN (3,5) THEN '投函模板' END AS type, " +
            "  SUM(COALESCE(cost,0) + COALESCE(over_length_surcharge,0) + COALESCE(remote_area_surcharge,0) + COALESCE(other_cost,0)) AS amount " +
            "  FROM tb_manifest " +
            "  WHERE is_delete = 0 AND status >= 2 " +
            "  AND pick_up_time BETWEEN #{start} AND #{end} " +
            "GROUP BY type")
    List<TemplateAmountDTO> selectTemplateAmountDistribution(@Param("start") LocalDateTime start,
                                                             @Param("end") LocalDateTime end);

    // 区域金额分布
    @Select("SELECT " +
            "  CASE WHEN shipping_fee_template_type IN (1,2,3) THEN '东京' " +
            "       WHEN shipping_fee_template_type IN (4,5) THEN '大阪' END AS area, " +
            "  CASE WHEN shipping_fee_template_type IN (1,4) THEN '普通模板' " +
            "       WHEN shipping_fee_template_type = 2 THEN '带电模板' " +
            "       WHEN shipping_fee_template_type IN (3,5) THEN '投函模板' END AS type, " +
            "  SUM(total_amount) AS amount " +
            "FROM (" +
            "  SELECT shipping_fee_template_type, " +
            "    COALESCE(cost,0) + COALESCE(over_length_surcharge,0) + " +
            "    COALESCE(remote_area_surcharge,0) + COALESCE(other_cost,0) AS total_amount " +
            "  FROM tb_manifest " +
            "  WHERE is_delete = 0 AND status >= 2 " +
            "  AND pick_up_time BETWEEN #{start} AND #{end}" +
            ") AS t " +
            "GROUP BY area, type")
    List<Map<String, Object>> selectRegionalAmountDistribution(@Param("start") LocalDateTime start,
                                                               @Param("end") LocalDateTime end);

    // 趋势数据（近6个月）
    @Select("SELECT " +
            "  DATE_FORMAT(DATE (pick_up_time - INTERVAL #{startHour} HOUR), '%Y-%m') AS month, " +
            "  SUM(total_amount) AS amount " +
            "FROM (" +
            "  SELECT pick_up_time, " +
            "    COALESCE(cost,0) + COALESCE(over_length_surcharge,0) + " +
            "    COALESCE(remote_area_surcharge,0) + COALESCE(other_cost,0) AS total_amount " +
            "  FROM tb_manifest " +
            "  WHERE is_delete = 0 AND status >= 2 " +
            "  AND pick_up_time BETWEEN #{start} AND #{end}" +
            ") AS t " +
            "GROUP BY month " +
            "ORDER BY month LIMIT 12")
    List<MonthAmountTrendDTO> selectAmountTrend(@Param("start") LocalDateTime start,
                                                @Param("end") LocalDateTime end,
                                                @Param("startHour") Integer startHour);

    /**
     * 获取今日运单总数和昨日运单总数
     */
    Map<String, Object> getTodayYesterdayQuantitySummary(
            @Param("todayStart") LocalDateTime todayStart,
            @Param("todayEnd") LocalDateTime todayEnd,
            @Param("yesterdayStart") LocalDateTime yesterdayStart,
            @Param("yesterdayEnd") LocalDateTime yesterdayEnd,
            @Param("beforeYesterdayStart") LocalDateTime beforeYesterdayStart,
            @Param("beforeYesterdayEnd") LocalDateTime beforeYesterdayEnd);

    /**
     * 获取今日各小时运单数量
     */
    List<Map<String, Object>> getTodayHourlyStats(
            @Param("todayStart") LocalDateTime todayStart,
            @Param("todayEnd") LocalDateTime todayEnd,
            @Param("startHour") int startHour);

    /**
     * 获取模板类型分布
     */
    List<Map<String, Object>> getTemplateQuantityDistribution(
            @Param("start") LocalDateTime start,
            @Param("end") LocalDateTime end);

    /**
     * 获取今日、昨日和前日运单金额总数
     */
    Map<String, Object> getTodayYesterdayBeforeAmountSummary(
            @Param("todayStart") LocalDateTime todayStart,
            @Param("todayEnd") LocalDateTime todayEnd,
            @Param("yesterdayStart") LocalDateTime yesterdayStart,
            @Param("yesterdayEnd") LocalDateTime yesterdayEnd,
            @Param("beforeYesterdayStart") LocalDateTime beforeYesterdayStart,
            @Param("beforeYesterdayEnd") LocalDateTime beforeYesterdayEnd);

    /**
     * 获取今日各小时运单金额
     */
    List<Map<String, Object>> getTodayHourlyAmountStats(
            @Param("todayStart") LocalDateTime todayStart,
            @Param("todayEnd") LocalDateTime todayEnd,
            @Param("startHour") int startHour);

    /**
     * 获取今日模板类型金额分布
     */
    List<Map<String, Object>> getTemplateAmountDistribution(
            @Param("start") LocalDateTime start,
            @Param("end") LocalDateTime end);

    /**
     * 获取特定时期的总发货数量和金额
     */
    Map<String, Object> getPeriodSummary(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 获取模板类型分布数据
     */
    List<Map<String, Object>> getTemplateDistribution(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 获取指定时间单位的趋势数据
     */
    List<Map<String, Object>> getTrendData(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("unit") String unit,
            @Param("groupFormat") String groupFormat);

    /**
     * 获取热力图数据
     */
    List<Map<String, Object>> getHeatmapData(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 更新运单的主提单关联
     * @param id 运单ID
     * @param masterBillId 主提单ID
     * @param masterBillNumber 主提单号
     * @return 更新的记录数
     */
    int updateMasterBill(@Param("id") Long id, 
                         @Param("masterBillId") Long masterBillId, 
                         @Param("masterBillNumber") String masterBillNumber);
}
