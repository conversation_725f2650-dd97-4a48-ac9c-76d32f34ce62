package cn.ysatnaf.types.util;


import cn.hutool.core.util.StrUtil;
import codes.quine.labo.lite.romaji.Kana;
import com.atilika.kuromoji.ipadic.Token;
import com.atilika.kuromoji.ipadic.Tokenizer;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 */
public class JapaneseUtil {
    public static String nameToRomaji(String japaneseName) {
        Tokenizer tokenizer = new Tokenizer();
        // 分词
        List<Token> tokenList = tokenizer.tokenize(japaneseName);
        StringBuilder name = new StringBuilder();
        for (Token token : tokenList) {
            String surface = token.getSurface();
            System.out.println(surface);
        }
        return name.toString().replaceAll("\\*", " ").trim();
    }

    public static void main(String[] args) {
        nameToRomaji("葉");
    }
}
