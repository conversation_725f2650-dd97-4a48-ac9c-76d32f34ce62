package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.manifest.model.entity.SawagaSiteCodeEntity;
import cn.ysatnaf.domain.manifest.repository.SawagaSiteCodeRepository;
import cn.ysatnaf.infrastructure.persistent.converter.SawagaSiteCodeConverter;
import cn.ysatnaf.infrastructure.persistent.dao.SawagaSiteCodeDao;
import cn.ysatnaf.infrastructure.persistent.po.SawagaSiteCodePO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class SawagaSiteCodeRepositoryImpl implements SawagaSiteCodeRepository {

    private final SawagaSiteCodeDao sawagaSiteCodeDao;

    @Override
    public List<SawagaSiteCodeEntity> getByZipCode(String zipCode) {
        LambdaQueryWrapper<SawagaSiteCodePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SawagaSiteCodePO::getZipCode, zipCode);
        return SawagaSiteCodeConverter.INSTANCE.toEntityList(sawagaSiteCodeDao.selectList(queryWrapper));
    }
}
