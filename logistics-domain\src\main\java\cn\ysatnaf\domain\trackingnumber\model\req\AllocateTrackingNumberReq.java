package cn.ysatnaf.domain.trackingnumber.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 分配单号请求
 * <AUTHOR>
 */
@Data
public class AllocateTrackingNumberReq {

    /**
     * 目的地ID
     */
    @Schema(description = "目的地ID", required = true)
    @NotNull(message = "目的地ID不能为空")
    private Long locationId;

    /**
     * 货物类型ID
     */
    @Schema(description = "货物类型ID", required = true)
    @NotNull(message = "货物类型ID不能为空")
    private Long shipmentTypeId;

    /**
     * 分配目标客户账户ID
     */
    @Schema(description = "分配目标客户账户ID", required = true)
    @NotNull(message = "客户账户ID不能为空")
    private Long customerAccountId;

    /**
     * 请求分配的数量
     */
    @Schema(description = "请求分配的数量", required = true, example = "10")
    @NotNull(message = "请求数量不能为空")
    @Min(value = 1, message = "请求数量必须大于0")
    private Integer quantity;

    // allocatorId 可以从登录信息中获取，无需传递
}