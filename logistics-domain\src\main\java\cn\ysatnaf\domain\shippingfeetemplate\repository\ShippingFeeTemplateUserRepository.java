package cn.ysatnaf.domain.shippingfeetemplate.repository;

import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplateUserPO;

import java.util.List;

public interface ShippingFeeTemplateUserRepository {

    ShippingFeeTemplateUserPO getByUserIdAndType(Long userId, Integer type);

    void insert(ShippingFeeTemplateUserPO templateUserPO);

    void updateById(ShippingFeeTemplateUserPO templateUserPO);

    List<ShippingFeeTemplateUserPO> getByUserIds(List<Long> userIds);
}
