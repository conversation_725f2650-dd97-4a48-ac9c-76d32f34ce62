package cn.ysatnaf.domain.address.service;

import cn.ysatnaf.domain.address.model.entity.AddressBookEntity;
import cn.ysatnaf.types.common.PageResult;

/**
 * AddressBookService
 *
 * <AUTHOR>
 * @date 2023/12/22 10:41
 */
public interface AddressBookService {
    Boolean add(AddressBookEntity addressBookEntity);

    Boolean delete(Long id);

    Boolean update(AddressBookEntity addressBookEntity);

    PageResult<AddressBookEntity> page(String openid, Integer pageNo, Integer pageSize);

    AddressBookEntity getById(Long sendAddressBookId);

    AddressBookEntity getDefault(String openid);
}
