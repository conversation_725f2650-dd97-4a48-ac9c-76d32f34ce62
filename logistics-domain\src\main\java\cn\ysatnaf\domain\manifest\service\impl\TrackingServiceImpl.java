package cn.ysatnaf.domain.manifest.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.Tracking;
import cn.ysatnaf.domain.manifest.model.message.LogisticsTrackingUpdateMessage;
import cn.ysatnaf.domain.manifest.model.valobj.TrackingStatus;
import cn.ysatnaf.domain.manifest.repository.TrackingRepository;
import cn.ysatnaf.domain.manifest.service.ManifestService;
import cn.ysatnaf.domain.manifest.service.TrackingService;
import cn.ysatnaf.domain.tracking.model.dto.TrackingDTO;
import cn.ysatnaf.domain.tracking.model.valobj.NekoposuLogisticsJourney;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.service.UserService;
import com.google.common.collect.Lists;
import com.itextpdf.styledxmlparser.jsoup.Jsoup;
import com.itextpdf.styledxmlparser.jsoup.nodes.Document;
import com.itextpdf.styledxmlparser.jsoup.nodes.Element;
import com.itextpdf.styledxmlparser.jsoup.select.Elements;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
//import org.springframework.web.reactive.function.client.WebClient;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hang
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TrackingServiceImpl implements TrackingService {

    private final TrackingRepository trackingRepository;

    private final UserService userService;

    private final RabbitTemplate rabbitTemplate;

//    private final WebClient webClient;

    @Override
    public List<String> querySawagaTracking(List<String> expressNumbers) {

        Map<String, Object> map = new HashMap<>();
        map.put("jsf_tree_64", "H4sIAAAAAAAAAK2WT08TQRTAJ0ChoIlRD15NJCEa2Zb91zacoAlSApIImCiHZtwd2iX7z91ZKRdv3v0AfgLDJ/ATePPql/Dq2dmdYdu8eQYSvWy6v0zf+73582avfpFGnpEXSTYyaEq9MTOiyzPqsVy8pmHgUR4ksXGcMXbEs8LjRcYOaExHLFudwn4SpUnMYr7z5tHPL3u/f8yRhVOyPPTGQehngpO9032RoiVTtFSK1kyK1m1TbO6Te0Pv+q0f0jzn5MH+Of1AWyGNRy0xPohHYtjd6bCB/558JHOnZGlYZubiLw9PZ/5z+O6ceXxzkhaZUv0fs7H57Wrt+/1Pn7/OETJJCSFrealBivLZEO/z8j3l5GnpMjFkrlrbGPMoNHbF47DgacGP2YRzsjAM/I1U/bV8rkhmIswSsbfOUxHSyOmIXtAhm6TGmVe8Y4YXDrkXGPllzlk0k7QIpr9PBruM+jJWOwX6TT2fgzh0ENYDrFHWBIuqoIVBmKaCME8Fe2IC1vDJPRm8pJHYKv0k5jSIWcbJ4lhUyzIkkFnLcWIi+wOsmVioat0OWC7mneUqCFyiCmIVmjYGsbJNF4PYXJhdDGIrYbUxiC2PhVVkYRVZWEUWVpGFVWRhFVlYRRZWkY1VZGMV2VhFtgX3fh8ZZE+3yPMbzrPoERGN/e2Cc7H7SJMnwRGnGZ/N/VaFdaZhn90QdhCLLrEb+H7Zc+94SZaxsOpZWE3YNNvYNNvdqYF1+71fyciWNR8n2lz7lDMIl3JOeZFDvJizAEQYisHjdS2GwMvjdSTKuJLQ1rYMAKGSgFhKmLgExLWEqUtop6MMAKGSgFhKWLgExLWEpUtop7EMAKGSgFhK2LgExLWErUtop78MAKGSgFhKOLgExLWEo0tox6AMAKGSgFhKuLgExLWEq0tox64MAKGSgFhKdHAJiGuJji6hddMyAIRKAmIp0cUlIK4lurqE1r3LABAqCYilRA+XgLiW6AGJBdFq4HWxWHUaSJuq0UC+JPtVG2RsqoYF+Urdsdqiwz65ocfvJFkkJCPxyYL1auz2c7Dbz8FuPwe7/RzsPnew+9zB7nMHu2gc7KJxsPvcua6ovnsb+iAXK8bFvF1M0cVs3F71tipW/yxJ+PSzkJPtf/qk3grFt6bM0dkQ0R7/7fv0dcAuXonUafoHR4rALqgNAAA=");
        map.put("jsf_state_64", "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");
        map.put("jsf_viewid", "/web/okurijosearcheng.jsp");
        map.put("main:toiStart", "Track it");
        map.put("main:correlation", 1);
        for (int i = 0; i < expressNumbers.size(); i++) {
            map.put("main:no" + (i + 1), expressNumbers.get(i));
        }
        map.put("main_SUBMIT", 1);
//        String response = webClient
//                .post()
//                .uri("https://k2k.sagawa-exp.co.jp/p/sagawa/web/okurijosearcheng.jsp")
//                .bodyValue(map)
//                .retrieve()
//                .bodyToMono(String.class)
//                .block();
        String response = HttpUtil.post("https://k2k.sagawa-exp.co.jp/p/sagawa/web/okurijosearcheng.jsp", map, 30000);
        Document document = Jsoup.parse(response);
        Elements tdElements = document.select("td.ichiran-fg.ichiran-fg-esrc5");
        List<String> result = new ArrayList<>();
        for (int i = 0; i < expressNumbers.size(); i++) {
            result.add(tdElements.get(i * 2 + 1).text());
        }
        return result;
    }

    @Override
    public void insertBatch(List<Tracking> trackingToSave) {
        trackingRepository.insertBatch(trackingToSave);
        // 发送消息
        for (Tracking tracking : trackingToSave) {
            try {

                LogisticsTrackingUpdateMessage message = LogisticsTrackingUpdateMessage.builder()
                        .trackingNumber(tracking.getTrackingNumber())
                        .time(tracking.getTime() == null ? DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(tracking.getCreateTime())
                                : DateUtil.formatDateTime(tracking.getTime()))
                        .info(tracking.getTrack())
                        .build();
                if (StrUtil.isNotBlank(tracking.getTrack()) && tracking.getTrack().contains("配達完了")) {
                    message.setStatus(7);
                }
                rabbitTemplate.convertAndSend("logistics.tracking.info", "update", message);
            } catch (Exception e) {
                log.error("发送消息失败。物流单号:{}，物流信息：{}, 错误原因:", tracking.getTrackingNumber(), tracking.getTrack(), e);
            }
        }
    }

    @Override
    public void save(Tracking tracking) {
        trackingRepository.save(tracking);
    }

    @Override
    public List<Tracking> getByExpressNumber(String sawagaNumber) {
        ManifestService manifestService = SpringUtil.getBean(ManifestService.class);
        Manifest manifest = manifestService.fuzzyGetBySawagaNumber(sawagaNumber);
        if (ObjUtil.isNull(manifest)) {
            return Collections.emptyList();
        }
        return getTrackingList(manifest);
    }

    @Override
    public List<Tracking> getByManifestId(Long manifestId) {
        ManifestService manifestService = SpringUtil.getBean(ManifestService.class);
        Manifest manifest = manifestService.getManifestById(manifestId);
        return getTrackingList(manifest);
    }

    @Override
    public void queryJpCustomsTracking(List<Manifest> list) {
        List<Tracking> trackingList = new ArrayList<>();
        list.forEach(manifest -> {
            String jpCustomsStatus = getJpCustomsStatus(manifest.getOrderNo());
            if (StrUtil.isNotBlank(jpCustomsStatus)
                    && !StrUtil.contains(jpCustomsStatus, "NewDataSet")
                    && manifest.getTrackingStatus() < TrackingStatus.PICKUP.getValue()) {
                manifest.setTrackingStatus(TrackingStatus.CUSTOMS_CLEARANCE.getValue());
                manifest.setTrackingUpdateTime(LocalDateTime.now());
                trackingList.add(Tracking.builder()
                        .manifestId(manifest.getId())
                        .operatorId(0L)
                        .status(TrackingStatus.CUSTOMS_CLEARANCE.getValue())
                        .track(jpCustomsStatus)
                        .createTime(LocalDateTime.now())
                        .build());
            }
        });
        if (CollUtil.isNotEmpty(trackingList)) {
            trackingRepository.insertBatch(trackingList);
        }
    }

    @Override
    public List<TrackingDTO> queryJapanPostTracking(List<String> expressNumbers) {
        // 判断查询的数量，一个和多个是不同的
        if (CollUtil.isEmpty(expressNumbers)) {
            return Collections.emptyList();
        }
        // 查询一个
        if (expressNumbers.size() == 1) {
            String expressNumber = expressNumbers.get(0);
            String url = "https://trackings.post.japanpost.jp/services/srv/search?requestNo1=" + expressNumber + "&requestNo2=&requestNo3=&requestNo4=&requestNo5=&requestNo6=&requestNo7=&requestNo8=&requestNo9=&requestNo10=&search.x=98&search.y=9&startingUrlPatten=&locale=ja";
            HttpResponse response = HttpUtil.createGet(url).timeout(30000).execute();
            Document document = Jsoup.parse(response.body());
            Elements tr = document.select("#content table tr");
            Element elements = tr.get(tr.size() - 7);
            String time = elements.children().get(0).text();
            String tracking = elements.children().get(1).text();
            String place = elements.children().get(3).text();
            return Lists.newArrayList(TrackingDTO.builder()
                    .expressNumber(expressNumber)
                    .tracking(tracking)
                    .place(place)
                    .time(DateUtil.parse(time, "yyyy/MM/dd HH:mm"))
                    .build());
        }

        // 查询多个
        StringBuilder url = new StringBuilder("https://trackings.post.japanpost.jp/services/srv/search?");
        for (int i = 0; i < expressNumbers.size(); i++) {
            url.append("requestNo").append(i + 1).append("=").append(expressNumbers.get(i)).append("&");
        }
        url.append("search.x=130&search.y=15&startingUrlPatten=&locale=ja");
        HttpResponse response = HttpUtil
                .createGet(url.toString())
                .timeout(30000)
                .execute();
        Document document = Jsoup.parse(response.body());
        Elements eles = document.select("#content table tr");
        List<TrackingDTO> trackingDTOList = new ArrayList<>();
        int index = 0;
        for (int i = 2; i < eles.size(); ) {
            try {
                Element element = eles.get(i);
                String timeStr = element.children().get(2).text();
                String tracking = element.children().get(3).text();
                String place = element.children().get(4).text();
                Date time;
                try {
                    time = DateUtil.parse(timeStr, "yyyy/MM/dd HH:mm");
                } catch (Exception e) {
                    time = DateUtil.parse(timeStr, "yyyy/MM/dd");
                }
                TrackingDTO trackingDTO = TrackingDTO.builder()
                        .expressNumber(expressNumbers.get(index))
                        .tracking(tracking)
                        .place(place)
                        .time(time)
                        .build();
                trackingDTOList.add(trackingDTO);
                i += 2;
            } catch (Exception e) {
                i++;
                log.error("查询日邮失败, 单号:{}", expressNumbers.get(index));
            } finally {
                index++;
            }
        }
        return trackingDTOList;
    }

    @Override
    public List<Tracking> getByManifestIds(List<Long> manifestIds) {
        return trackingRepository.getByManifestIds(manifestIds);
    }

    @Override
    public Map<String, List<TrackingDTO>> queryNekoposuLogisticsJourney(List<String> expressNumbers) {
        Map<String, List<TrackingDTO>> result = new HashMap<>(expressNumbers.size());
        Map<String, Object> map = new HashMap<>();
        map.put("backrequest", "get");
        for (int i = 0; i < expressNumbers.size(); i++) {
            if (i + 1 < 10) {
                map.put("number0" + (i + 1), expressNumbers.get(i));
            } else {
                map.put("number" + (i + 1), expressNumbers.get(i));
            }
        }
//        String response = webClient
//                .post()
//                .uri("https://toi.kuronekoyamato.co.jp/cgi-bin/tneko")
//                .bodyValue(map)
//                .retrieve()
//                .bodyToMono(String.class)
//                .block();
        String response = HttpUtil.post("https://toi.kuronekoyamato.co.jp/cgi-bin/tneko", map, 30000);
        Document document = Jsoup.parse(response);
        Elements divs = document.select(".parts-tracking-invoice-block");
        for (Element div : divs) {
            String title = div.select(".tracking-invoice-block-title").text();
            String expressNumber = title.substring(title.indexOf("：") + 1).replace("-", "");
            Elements details = div.select(".tracking-invoice-block-detail ol li");
            List<TrackingDTO> trackingDTOList = new ArrayList<>();
            for (Element detail : details) {
                try {
                    String tracking = detail.select(".item").text();
                    Date time;
                    String date = DateUtil.year(new Date()) + "年" + detail.select(".date").text();
                    try {
                        time = DateUtil.parse(date, "yyyy年MM月dd日 HH:mm");
                    } catch (Exception e) {
                        time = DateUtil.parse(date, "yyyy年MM月dd日");
                    }
                    String place = detail.select(".name").text();
                    if (StrUtil.isBlank(place)) {
                        place = detail.select(".name a").text();
                    }
                    TrackingDTO trackingDTO = TrackingDTO.builder()
                            .expressNumber(expressNumber)
                            .tracking(tracking)
                            .place(place)
                            .time(time)
                            .build();
                    trackingDTOList.add(trackingDTO);
                } catch (Exception e) {
                    log.error("抓取黑猫物流-[{}]-错误", expressNumber);
                }
            }
            result.put(expressNumber, trackingDTOList);
        }
        return result;
    }

    private String getJpCustomsStatus(String cleanOrderNumber) {
        // SOAP请求的URL
        String soapUrl = "http://webservice.sinoair.co.jp/trace.asmx";

        // SOAP请求的内容，根据具体的SOAP请求格式设置
        String soapRequest = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                "  <soap:Body>\n" +
                "    <QueryBillNO xmlns=\"http://webservice.sinoair.co.jp/\">\n" +
                "      <cBillNO>" + cleanOrderNumber + "</cBillNO>\n" +
                "    </QueryBillNO>\n" +
                "  </soap:Body>\n" +
                "</soap:Envelope>";

        // 发起SOAP请求
        HttpResponse response = HttpRequest.post(soapUrl)
                .header("Content-Type", "text/xml; charset=utf-8")
                .body(soapRequest)
                .execute();

        // 处理SOAP响应
        if (response.isOk()) {

            String soapResponse = response.body();
            Document document = Jsoup.parse(soapResponse);
            Elements tdElements = document.select("QueryBillNOResponse");
            String text = tdElements.get(0).text().replaceAll(" ", "");
            return text.substring(2, text.length() - 2);
        } else {
            log.error("请求日本海关接口失败");
            return null;
        }
    }

    @NotNull
    private List<Tracking> getTrackingList(Manifest manifest) {
        Tracking tracking = Tracking.builder()
                .manifestId(manifest.getId())
                .operatorId(manifest.getCreatorId())
                .status(TrackingStatus.FORECAST.getValue())
                .track(TrackingStatus.FORECAST.getDescription())
                .createTime(manifest.getCreateTime()).build();
        List<Tracking> trackingList = trackingRepository.getByManifestId(manifest.getId());
        if (CollUtil.isEmpty(trackingList)) {
            trackingList = Lists.newArrayList(tracking);
        } else {
            trackingList.add(tracking);
        }
        Set<Long> userIds = trackingList.stream().map(Tracking::getOperatorId).filter(ObjUtil::isNotNull).collect(Collectors.toSet());
        List<UserEntity> userEntities = userService.listBatchByIds(userIds);
        if (CollUtil.isNotEmpty(userEntities)) {
            Map<Long, String> userIdNicknameMap = userEntities.stream().collect(Collectors.toMap(UserEntity::getId, UserEntity::getNickname));
            trackingList.forEach(track -> {
                if (track.getTime() != null) {
                    track.setCreateTime(track.getTime().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDateTime());
                }
                if (ObjUtil.isNull(track.getOperatorId()) || track.getOperatorId().equals(0L)) {
                    track.setOperatorName("系统");
                } else {
                    track.setOperatorName(userIdNicknameMap.get(track.getOperatorId()));
                }
                if (StrUtil.isNotBlank(track.getPlace())) {
                    track.setTrack(track.getTrack() + " [日本] " + track.getPlace());
                }
            });
        }
        return trackingList;
    }
}
