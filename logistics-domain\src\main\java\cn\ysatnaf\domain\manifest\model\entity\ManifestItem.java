package cn.ysatnaf.domain.manifest.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManifestItem {

    private Long id;

    private String expressNumber;

    /**
     * 所属运单ID
     */
    private Long manifestId;

    /**
     * 物品名称
     */
    private String name;

    /**
     * 物品英文名称
     */
    private String nameEn;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 随机生成
     */
    private BigDecimal value;
}
