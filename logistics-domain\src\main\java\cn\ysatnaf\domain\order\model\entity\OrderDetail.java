package cn.ysatnaf.domain.order.model.entity;

/**
 * OrderDetail
 *
 * <AUTHOR>
 * @date 2024/2/7 11:55
 */

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * OrderDetail
 * 订单明细
 * <AUTHOR>
 * @date 2024/2/7 11:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderDetail {

    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 长度
     */
    private BigDecimal length;

    /**
     * 宽度
     */
    private BigDecimal width;

    /**
     * 高度
     */
    private BigDecimal height;

    /**
     * 体积重量
     */
    private BigDecimal dimensionalWeight;

    /**
     * 基本费用
     */
    private BigDecimal basicFee;
}
