package cn.ysatnaf.domain.manifest.service;

import cn.ysatnaf.domain.manifest.model.aggregate.ManifestAggregate;
import cn.ysatnaf.domain.manifest.model.dto.ManifestOrderDTO;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestSearchDTO;
import cn.ysatnaf.domain.manifest.model.req.*;
import cn.ysatnaf.domain.manifest.model.res.ManifestSearchPickedUpRes;
import cn.ysatnaf.domain.manifest.model.res.ManifestBatchSearchByExpressNumberRes;
import cn.ysatnaf.domain.manifest.model.res.ManifestStatisticsRes;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.types.common.PageResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * ManifestService
 *
 * <AUTHOR> <PERSON>
 * @date 2023/12/22 16:05
 */
public interface ManifestService {

    PageResult<ManifestSearchDTO> searchManifest(ManifestSearchReq req);

    /**
     * 高级查询运单
     * @param req 高级查询请求
     * @return 分页结果
     */
    PageResult<ManifestSearchPickedUpRes> searchPickedUp(ManifestSearchPickedUpReq req);

    void exportManifest(ManifestExportReq req, HttpServletResponse httpServletResponse);

    void ship(List<Manifest> manifests);

    void exportApplicationForm(ApplicationFormExportReq req, HttpServletResponse httpServletResponse);

    List<ManifestAggregate> searchManifestAggregate(List<Long> ids, Date pickUpTimeFrom, Date pickUpTimeTo, List<Integer> shippingFeeTemplateTypes);

    List<ManifestAggregate> getManifestAggregateByExpressNumbers(List<String> expressNumbers);

    void generateWayBillImage(ManifestGenWayBillImageReq manifestId, HttpServletResponse httpServletResponse);

    ManifestOrderDTO getByExpressNumber(String expressNumber);

    List<String> fuzzySearchExpressNumber(ManifestFuzzySearchReq req);

    PageResult<ManifestOrderDTO> searchOrder(ManifestOrderSearchReq req);

    Boolean add(ManifestAddReq req);

    /**
     * 外部系统录入运单 - 自动分配单号
     * @param req 请求参数，包含目的地和货物类型用于自动分配单号
     * @return 录入结果
     */
    Boolean addWithAllocatedExpress(ManifestAddWithAllocatedExpressReq req);

    Boolean pickup(ManifestPickupReq req);

    ManifestOrderDTO getOrderById(Long id);

    ManifestSearchDTO getById(Long id);

    void genWayBillImageById(ManifestGenWayBillImageByIdReq req, HttpServletResponse httpServletResponse);

    Boolean updateById(ManifestUpdateByIdReq req);

    void downloadImportTemplate(HttpServletResponse httpServletResponse);

    List<Manifest> listByIds(Collection<Long> manifestIds);

    Boolean abandon(List<Long> manifestIds);

    Boolean restore(List<Long> manifestIds);

    Boolean remark(ManifestRemarkReq req);

    Boolean updateFreightCost(ManifestUpdateFreightCostReq req);

    List<Manifest> findShippedManifests();

    void updateBatchByIds(List<Manifest> manifestList);

    Manifest getManifestById(Long manifestId);

    void exportOrder(ManifestOrderExportReq req, HttpServletResponse response);

    ManifestStatisticsRes statistics(ManifestStatisticsReq req);

    Manifest fuzzyGetBySawagaNumber(String expressNumber);

    void deleteByUserId(Long userId);

    Boolean changeOwner(ManifestChangeOwnerReq req);

    /**
     * 打印简易面单
     *
     * @param manifestIds         运单单号
     * @param httpServletResponse 响应
     */
    void printSimpleWaybill(List<Long> manifestIds, HttpServletResponse httpServletResponse);

    List<ManifestBatchSearchByExpressNumberRes> batchSearchByExpressNumbers(List<String> expressNumbers);

    /**
     * 根据系统订单查询运单
     *
     * @param orderNo 系统运单
     * @return
     */
    Manifest getManifestByOrderNoOrExpressNumber(String orderNo);

    /**
     * 根据运费模板类型查询已发货运单
     *
     * @param templateTypes 运费模板类型 {@link ShippingFeeTemplateTypeEnum#getCode()}
     * @return 运单列表
     */
    List<Manifest> findShippedManifestsByShippingFeeTemplateTypes(List<Integer> templateTypes);

    /**
     * 生成条形码PDF
     *
     * @param req
     * @param httpServletResponse
     */
    void generateBarcodePdf(ManifestGenerateBarcodePdfReq req, HttpServletResponse httpServletResponse);

    /**
     * 导出预报信息
     *
     * @param req      请求参数
     * @param response 响应对象
     */
    void exportPreReportInfo(ManifestExportPreReportInfoReq req, HttpServletResponse response);

    /**
     * 转单
     * @param req 请求参数
     * @return 请求结果
     */
    Boolean transfer(ManifestTransferReq req);

    /**
     * 批量转单号
     * @param file 上传文件
     * @return
     */
    Boolean batchTransfer(MultipartFile file);

    List<ManifestAggregate> listByMasterBillId(Long masterBillId);
}
