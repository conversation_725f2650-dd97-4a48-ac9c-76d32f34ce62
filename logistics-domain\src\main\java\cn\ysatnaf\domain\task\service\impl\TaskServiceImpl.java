package cn.ysatnaf.domain.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.manifest.model.req.InvoiceGenerationTaskSubmitReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillInvoiceExportReq;
import cn.ysatnaf.domain.manifest.producer.MasterBillGenerateInvoiceProducer;
import cn.ysatnaf.domain.manifest.repository.MasterBillRepository;
import cn.ysatnaf.domain.manifest.model.entity.MasterBill;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.domain.task.model.po.TaskPO;
import cn.ysatnaf.domain.task.model.req.TaskListReq;
import cn.ysatnaf.domain.task.model.vo.TaskStatus;
import cn.ysatnaf.domain.task.model.vo.TaskType;
import cn.ysatnaf.domain.task.repository.TaskRepository;
import cn.ysatnaf.domain.task.service.TaskService;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@RequiredArgsConstructor
public class TaskServiceImpl implements TaskService {

    private final TaskRepository taskRepository;
    private final MasterBillRepository masterBillRepository;
    private final MasterBillGenerateInvoiceProducer masterBillGenerateInvoiceProducer;

    @Override
    public Long createGenerateInvoiceTask(InvoiceGenerationTaskSubmitReq req) {
        String taskName;
        if (CollUtil.isNotEmpty(req.getIds())) {
            taskName = "生成发票：指定" + req.getIds().size() + "条";
        } else {
            taskName = "生成发票：时间范围-[";
            if (req.getPickUpTimeFrom() != null && req.getPickUpTimeTo() != null) {
                taskName += DateUtil.formatDateTime(req.getPickUpTimeFrom()) + " -" + DateUtil.formatDateTime(req.getPickUpTimeTo());
            } else if (req.getPickUpTimeFrom() != null) {
                taskName += DateUtil.formatDateTime(req.getPickUpTimeFrom()) + "往后";
            } else if (req.getPickUpTimeTo() != null) {
                taskName += DateUtil.formatDateTime(req.getPickUpTimeTo()) + "之前";
            } else {
                taskName += "全部";
            }
            taskName += "];";
            if (CollUtil.isNotEmpty(req.getShippingFeeTemplateTypes())) {
                taskName += "模板类型:[";
                if (req.getShippingFeeTemplateTypes().contains(ShippingFeeTemplateTypeEnum.GENERAL.getCode())) {
                    taskName += "普通;";
                }
                if (req.getShippingFeeTemplateTypes().contains(ShippingFeeTemplateTypeEnum.ELECTRONICS.getCode())) {
                    taskName += "带电;";
                }
                if (req.getShippingFeeTemplateTypes().contains(ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode())) {
                    taskName += "投函;";
                }
            }
            taskName = taskName.substring(0, taskName.length() - 1);
            taskName += "]";
        }
        TaskPO taskPO = initTask(TaskType.INVOICE_GENERATION, taskName, req);
        return taskRepository.insert(taskPO);
    }

    @Override
    public Long createMasterBillInvoiceExportTask(MasterBillInvoiceExportReq req) {
        // 检查主提单是否存在
        MasterBill masterBill = masterBillRepository.getById(req.getMasterBillId());
        if (masterBill == null) {
            throw new ServiceException("提单不存在");
        }
        
        // 创建任务名称
        StringBuilder taskName = new StringBuilder();
        taskName.append("导出提单发票：");
        taskName.append(masterBill.getMasterBillNumber());
        
        // 添加目的地信息
        taskName.append("；目的地：");
        if (req.getDestination() == 1) {
            taskName.append("东京");
        } else if (req.getDestination() == 2) {
            taskName.append("大阪");
        } else {
            taskName.append("其他");
        }
        
        // 添加运费模板类型信息
        if (CollUtil.isNotEmpty(req.getShippingFeeTemplateTypes())) {
            taskName.append("；模板类型：[");
            if (req.getShippingFeeTemplateTypes().contains(ShippingFeeTemplateTypeEnum.GENERAL.getCode())) {
                taskName.append("普通;");
            }
            if (req.getShippingFeeTemplateTypes().contains(ShippingFeeTemplateTypeEnum.ELECTRONICS.getCode())) {
                taskName.append("带电;");
            }
            if (req.getShippingFeeTemplateTypes().contains(ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode())) {
                taskName.append("投函;");
            }
            
            // 移除最后一个分号
            if (taskName.charAt(taskName.length() - 1) == ';') {
                taskName.deleteCharAt(taskName.length() - 1);
            }
            taskName.append("]");
        }
        
        // 初始化任务并提交
        TaskPO taskPO = initTask(TaskType.MASTER_BILL_INVOICE_EXPORT, taskName.toString(), req);
        Long taskId = taskRepository.insert(taskPO);
        masterBillGenerateInvoiceProducer.produce(taskId);
        return taskId;
    }

    @Override
    public TaskPO getById(Long taskId) {
        return taskRepository.getById(taskId);
    }

    @Override
    public void start(TaskPO taskPO) {
        taskPO.setStatus(TaskStatus.IN_PROGRESS.name());
        taskPO.setMessage("正在生成文件");
        taskRepository.updateById(taskPO);
    }

    @Override
    public void updateById(TaskPO taskPO) {
        taskRepository.updateById(taskPO);
    }

    @Override
    public void completed(TaskPO taskPO) {
        taskPO.setProgress(100d);
        taskPO.setMessage("执行完毕");
        taskPO.setStatus(TaskStatus.COMPLETED.name());
        taskRepository.updateById(taskPO);
    }

    @Override
    public void failed(TaskPO taskPO, String message) {
        taskPO.setMessage(message);
        taskPO.setStatus(TaskStatus.FAILED.name());
        taskRepository.updateById(taskPO);
    }

    @Override
    public PageResult<TaskPO> list(TaskListReq req) {
        return taskRepository.list(LoginUserHolder.getLoginUser().getId(), req.getTaskType(), req.getTaskStatus(), req.getCreateTimeStart(), req.getCreateTimeEnd(), req.getPageNo(), req.getPageSize());
    }

    private TaskPO initTask(TaskType taskType, String taskName, Object taskParam) {
        return TaskPO.builder()
                .taskName(taskName)
                .taskType(taskType.name())
                .message("等待执行")
                .progress(0d)
                .taskParam(JSON.toJSONString(taskParam))
                .userId(LoginUserHolder.getLoginUser().getId())
                .status(TaskStatus.PENDING.name())
                .build();
    }
}
