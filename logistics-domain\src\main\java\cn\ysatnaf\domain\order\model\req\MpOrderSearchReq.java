package cn.ysatnaf.domain.order.model.req;

import cn.ysatnaf.types.common.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OrderSearchReq
 *
 * <AUTHOR>
 * @date 2023/12/22 16:00
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询订单 入参")
@Data
public class MpOrderSearchReq extends PageParam {

    @Schema(description = "openid")
    private String openid;

    @Schema(description = "内部系统订单号")
    private String orderNo;

    @Schema(description = "快递单号（佐川号码）")
    private String expressNo;

    @Schema(description = "订单状态: 0-待接单;1;-待取件;2-待支付;3-待揽件;4-待发货;5-运送中;6-已签收;-1-已取消")
    private Integer status;
}
