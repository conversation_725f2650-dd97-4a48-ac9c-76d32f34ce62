package cn.ysatnaf.domain.fund.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ModifyBalanceDTO {

    private Long userId;

    private Long manifestId;

    private BigDecimal changeBalance;

    private String reason;
}
