package cn.ysatnaf.trigger.http.controller.mp;

import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;
import cn.ysatnaf.domain.address.service.ReceiverAreaService;
import cn.ysatnaf.trigger.http.req.address.ReceiverAreaGetReq;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import java.util.List;

/**
 * ReceiverAreaController
 *
 * <AUTHOR>
 * @date 2023/12/22 11:59
 */
@Tag(name = "收件地址相关")
@RequestMapping("/mp/receiverArea")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class ReceiverAreaController {

    private final ReceiverAreaService receiverAreaService;

    @PostMapping("/getByZipCode")
    @PermitAll
    @Operation(summary = "根据邮编获取地址")
    public CommonResult<List<ReceiverAreaEntity>> getByZipCode(@RequestBody @Validated ReceiverAreaGetReq req) {
        return CommonResult.success(receiverAreaService.getByZipCode(req.getZipCode()));
    }
}
