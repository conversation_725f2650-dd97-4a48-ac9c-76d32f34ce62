package cn.ysatnaf.domain.task.service;

import cn.ysatnaf.domain.manifest.model.req.InvoiceGenerationTaskSubmitReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillInvoiceExportReq;
import cn.ysatnaf.domain.task.model.po.TaskPO;
import cn.ysatnaf.domain.task.model.req.TaskListReq;
import cn.ysatnaf.types.common.PageResult;

public interface TaskService {

    /**
     * 创建发票生成任务
     * @param req 请求参数
     * @return 任务ID
     */
    Long createGenerateInvoiceTask(InvoiceGenerationTaskSubmitReq req);
    
    /**
     * 创建主提单发票导出任务
     * @param req 请求参数
     * @return 任务ID
     */
    Long createMasterBillInvoiceExportTask(MasterBillInvoiceExportReq req);

    /**
     * 根据ID获取任务
     * @param taskId 任务ID
     * @return 任务详情
     */
    TaskPO getById(Long taskId);

    /**
     * 开始执行任务
     * @param taskPO 任务对象
     */
    void start(TaskPO taskPO);

    /**
     * 更新任务信息
     * @param taskPO 任务对象
     */
    void updateById(TaskPO taskPO);

    /**
     * 标记任务为完成
     * @param taskPO 任务对象
     */
    void completed(TaskPO taskPO);

    /**
     * 标记任务为失败
     * @param taskPO 任务对象
     * @param message 失败信息
     */
    void failed(TaskPO taskPO, String message);

    /**
     * 查询任务列表
     * @param req 请求参数
     * @return 分页结果
     */
    PageResult<TaskPO> list(TaskListReq req);
}
