package cn.ysatnaf.domain.trackingnumber.service;

import cn.ysatnaf.domain.trackingnumber.model.dto.ChannelAvailableCountDTO;
import cn.ysatnaf.domain.trackingnumber.model.dto.TrackingNumberAllocationResultDTO;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberAllocationBatch;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberImportBatch;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberPool;
import cn.ysatnaf.domain.trackingnumber.model.req.AllocateTrackingNumberReq;
// import cn.ysatnaf.domain.trackingnumber.model.req.ImportTrackingNumberReq; // 暂时不用
import cn.ysatnaf.types.common.PageResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 预报单号核心服务接口
 * <AUTHOR>
 */
public interface TrackingNumberService {

    /**
     * 导入单号到指定渠道
     * @param file 包含单号的Excel文件
     * @param locationId 目的地ID
     * @param shipmentTypeId 货物类型ID
     * @return 导入批次信息
     */
    TrackingNumberImportBatch importTrackingNumbers(MultipartFile file, Long locationId, Long shipmentTypeId);

    /**
     * 从指定渠道分配单号
     * @param req 分配请求参数 (渠道ID, 数量)
     * @return 分配结果
     */
    TrackingNumberAllocationResultDTO allocateTrackingNumbers(AllocateTrackingNumberReq req);
    
    /**
     * 分页查询导入批次记录
     * @param uploaderId 上传者ID (可选)
     * @param channelId 渠道ID (可选)
     * @param startTime 导入开始时间 (可选)
     * @param endTime 导入结束时间 (可选)
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    PageResult<TrackingNumberImportBatch> pageQueryImportBatches(Long uploaderId,
                                                                Long channelId,
                                                                LocalDateTime startTime,
                                                                LocalDateTime endTime,
                                                                int pageNo,
                                                                int pageSize);

    /**
     * 分页查询分配批次记录
     * @param allocatorId 分配者ID (可选)
     * @param requestedLocationId 请求的地点ID (可选)
     * @param requestedShipmentTypeId 请求的货物类型ID (可选)
     * @param startTime 分配开始时间 (可选)
     * @param endTime 分配结束时间 (可选)
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    PageResult<TrackingNumberAllocationBatch> pageQueryAllocationBatches(Long allocatorId,
                                                                       Long requestedLocationId,
                                                                       Long requestedShipmentTypeId,
                                                                       LocalDateTime startTime,
                                                                       LocalDateTime endTime,
                                                                       int pageNo,
                                                                       int pageSize);

    /**
     * 根据分配批次ID查询分配的单号详情
     * @param allocationBatchId 分配批次ID
     * @return 分配的单号列表
     */
    List<TrackingNumberPool> findAllocatedNumbersByBatchId(Long allocationBatchId);
    
    /**
     * 导出指定分配批次的单号到Excel
     * @param allocationBatchId 分配批次ID
     * @param response HttpServletResponse 用于文件下载
     */
    void exportAllocatedNumbers(Long allocationBatchId, HttpServletResponse response);
    
    /**
     * 打印指定分配批次的单号 (具体实现可能需要根据打印需求调整)
     * @param allocationBatchId 分配批次ID
     * @param response HttpServletResponse (如果需要生成文件下载)
     */
    void printAllocatedNumbers(Long allocationBatchId, HttpServletResponse response);

    /**
     * 获取所有活动渠道的可用单号数量
     * @return 包含各渠道及其可用数量的列表
     */
    List<ChannelAvailableCountDTO> getAvailableCounts();
} 