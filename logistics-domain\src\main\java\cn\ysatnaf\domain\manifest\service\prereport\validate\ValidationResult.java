package cn.ysatnaf.domain.manifest.service.prereport.validate;

import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 检测结果
 */
@Data
public class ValidationResult {

    /**
     * 已预报数据
     */
    private List<ManifestPreReportRow> preReportedList = new ArrayList<>();

    /**
     * 未预报数据
     */
    private List<ManifestPreReportRow> notPreReportedList = new ArrayList<>();
}
