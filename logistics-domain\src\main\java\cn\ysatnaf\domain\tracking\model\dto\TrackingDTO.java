package cn.ysatnaf.domain.tracking.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 物流轨迹信息DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TrackingDTO {

    /**
     * 物流单号
     */
    private String expressNumber;

    /**
     * 轨迹
     */
    private String tracking;

    /**
     * 轨迹发生场所
     */
    private String place;

    /**
     * 轨迹发生时间
     */
    private Date time;
}
