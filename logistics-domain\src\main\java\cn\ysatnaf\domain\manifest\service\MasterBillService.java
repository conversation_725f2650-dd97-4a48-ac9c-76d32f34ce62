package cn.ysatnaf.domain.manifest.service;

import cn.ysatnaf.domain.manifest.model.entity.MasterBill;
import cn.ysatnaf.domain.manifest.model.req.MasterBillCreateReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillExportReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillGenerateBarcodePdfReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillPageReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillUpdateReq;
import cn.ysatnaf.domain.manifest.model.res.MasterBillDetailRes;
import cn.ysatnaf.types.common.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 主提单服务接口
 * <AUTHOR>
 */
public interface MasterBillService {

    /**
     * 创建主提单
     * @param req 创建请求
     * @return 创建的主提单ID
     */
    Long create(MasterBillCreateReq req);

    /**
     * 更新主提单
     * @param req 更新请求
     * @return 是否更新成功
     */
    boolean update(MasterBillUpdateReq req);

    /**
     * 删除主提单
     * @param id 主提单ID
     * @return 是否删除成功
     */
    boolean delete(Long id);

    /**
     * 获取主提单详情
     * @param id 主提单ID
     * @return 主提单详情
     */
    MasterBillDetailRes getDetail(Long id);

    /**
     * 分页查询主提单
     * @param req 分页查询请求
     * @return 主提单分页结果
     */
    PageResult<MasterBillDetailRes> page(MasterBillPageReq req);

    /**
     * 为运单分配主提单
     * @param manifestId 运单ID
     * @param masterBillId 主提单ID
     * @return 是否分配成功
     */
    boolean assignManifest(Long manifestId, Long masterBillId);

    /**
     * 更新主提单的统计信息（运单数量、总重量、总体积）
     * @param masterBillId 主提单ID
     * @return 是否更新成功
     */
    boolean updateStatistics(Long masterBillId);
    
    /**
     * 根据主提单导出日本海关文件
     * @param req 导出请求
     * @param response HTTP响应
     */
    void exportCustomsDocument(MasterBillExportReq req, HttpServletResponse response);
    
    /**
     * 根据主提单生成条码PDF
     * @param req 请求参数
     * @param response HTTP响应
     */
    void generateBarcodePdf(MasterBillGenerateBarcodePdfReq req, HttpServletResponse response);

    List<MasterBill> listByIds(Set<Long> masterBillIds);
}