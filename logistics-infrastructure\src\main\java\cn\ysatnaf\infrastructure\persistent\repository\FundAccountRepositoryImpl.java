package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.fund.model.entity.FundAccountEntity;
import cn.ysatnaf.domain.fund.repository.FundAccountRepository;
import cn.ysatnaf.infrastructure.persistent.converter.FundAccountConverter;
import cn.ysatnaf.infrastructure.persistent.dao.FundAccountDao;
import cn.ysatnaf.infrastructure.persistent.po.FundAccountPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class FundAccountRepositoryImpl implements FundAccountRepository {

    private final FundAccountDao dao;

    @Override
    public FundAccountEntity getByUserId(Long userId) {
        LambdaQueryWrapper<FundAccountPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FundAccountPO::getUserId, userId);
        return FundAccountConverter.INSTANCE.toEntity(dao.selectOne(wrapper));
    }

    @Override
    public FundAccountEntity getById(Long id) {
        return FundAccountConverter.INSTANCE.toEntity(dao.selectById(id));
    }

    @Override
    public void updateById(FundAccountEntity fundAccountEntity) {
        dao.updateById(FundAccountConverter.INSTANCE.toPO(fundAccountEntity));
    }

    @Override
    public void insert(FundAccountEntity fundAccountEntity) {
        dao.insert(FundAccountConverter.INSTANCE.toPO(fundAccountEntity));
    }

    @Override
    public void deleteByUserId(Long userId) {
        LambdaQueryWrapper<FundAccountPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FundAccountPO::getUserId, userId);
        dao.delete(wrapper);
    }

    @Override
    public List<FundAccountEntity> listByUserIds(List<Long> userIds) {
            LambdaQueryWrapper<FundAccountPO> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(FundAccountPO::getUserId, userIds);
            return FundAccountConverter.INSTANCE.toEntityList(dao.selectList(wrapper));
    }

}
