# 揽件密码验证功能测试文档

## 功能描述

当运单已经是揽件状态（status = 2）时，重新揽件需要校验传入的密码（当前用户登录密码），如果密码正确，则表示用户确认重新揽件，可以继续执行。

## 修改内容

### 1. ManifestPickupReq 请求参数修改

- 新增字段：`confirmPassword`（确认密码）
- 类型：String
- 说明：当运单已揽件时需要传入当前用户的登录密码

### 2. ManifestServiceImpl.pickup() 方法修改

- 新增密码校验逻辑
- 仅在运单状态为已揽件（status = 2）且未发货时要求密码校验
- 使用 `UserEntity.validatePassword()` 方法验证密码

## 测试场景

### 场景 1：正常揽件（运单状态为待揽件）

**前置条件：** 运单状态为 1（待揽件）
**请求参数：**

```json
{
  "id": 1001,
  "expressNumber": "BM2024001",
  "sawagaNumber": "SG2024001",
  "orderNumber": "ORDER001",
  // ... 其他必需参数
  "confirmPassword": null // 可以为空
}
```

**预期结果：** 揽件成功，无需密码校验

### 场景 2：重新揽件不提供密码

**前置条件：** 运单状态为 2（已揽件）
**请求参数：**

```json
{
  "id": 1001,
  "expressNumber": "BM2024001",
  // ... 其他参数
  "confirmPassword": null // 为空
}
```

**预期结果：** 抛出异常 "该运单已揽件，重新揽件需要输入确认密码"

### 场景 3：重新揽件提供错误密码

**前置条件：** 运单状态为 2（已揽件）
**请求参数：**

```json
{
  "id": 1001,
  "expressNumber": "BM2024001",
  // ... 其他参数
  "confirmPassword": "wrongpassword"
}
```

**预期结果：** 抛出异常 "确认密码错误，无法重新揽件"

### 场景 4：重新揽件提供正确密码

**前置条件：** 运单状态为 2（已揽件）
**请求参数：**

```json
{
  "id": 1001,
  "expressNumber": "BM2024001",
  // ... 其他参数
  "confirmPassword": "正确的用户密码"
}
```

**预期结果：** 揽件成功，运单信息更新

### 场景 5：已发货运单尝试重新揽件

**前置条件：** 运单状态为 3（已发货）
**请求参数：**

```json
{
  "id": 1001,
  "expressNumber": "BM2024001",
  // ... 其他参数
  "confirmPassword": "anypassword"
}
```

**预期结果：** 抛出异常 "该运单已发货，无法再次被揽件"

## API 测试示例

### 使用 Postman 测试

```
POST /web/manifest/pickup
Content-Type: application/json
Authorization: Bearer {your_token}

{
  "id": 1001,
  "expressNumber": "BM2024001",
  "sawagaNumber": "SG2024001",
  "orderNumber": "ORDER001",
  "receiverZipCode": "1000001",
  "receiverName": "山田太郎",
  "receiverAddress": "東京都千代田区千代田1-1",
  "receiverPhone": "09012345678",
  "weight": 1.5,
  "cost": 1000,
  "shippingFeeTemplateType": 1,
  "manifestItems": [
    {
      "name": "测试商品",
      "weight": 1.5,
      "quantity": 1,
      "price": 1000
    }
  ],
  "confirmPassword": "your_actual_password"
}
```

## 验证要点

1. **权限验证**：确保只有管理员可以调用揽件接口
2. **状态判断**：正确识别运单当前状态
3. **密码校验**：使用正确的密码验证逻辑
4. **日志记录**：记录重新揽件的操作日志
5. **异常处理**：提供清晰的错误信息

## 注意事项

1. 密码传输应使用 HTTPS 确保安全
2. 密码不应记录在日志中
3. 重新揽件操作应有相应的业务日志记录
4. 需要考虑并发情况下的数据一致性

## 相关文件修改清单

1. `ManifestPickupReq.java` - 新增 confirmPassword 字段
2. `ManifestServiceImpl.java` - pickup() 方法中新增密码校验逻辑
3. 接口文档需要更新，说明新的参数要求
