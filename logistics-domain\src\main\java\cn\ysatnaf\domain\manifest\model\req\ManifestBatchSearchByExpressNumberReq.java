package cn.ysatnaf.domain.manifest.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * ManifestBatchSearchByIdReq
 *
 * <AUTHOR>
 * @date 2024/3/20 15:41
 */
@Schema(description = "根据运单号批量追踪")
@Data
public class ManifestBatchSearchByExpressNumberReq {

    @Schema(description = "运单号列表")
    @NotEmpty
    private List<String> expressNumbers;
}
