package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingPageReq;
import cn.ysatnaf.domain.item.model.entity.ItemNameMapping;
import cn.ysatnaf.domain.item.repository.ItemNameMappingRepository;
import cn.ysatnaf.infrastructure.persistent.converter.ItemNameMappingConverter;
import cn.ysatnaf.infrastructure.persistent.dao.ItemNameMappingDao;
import cn.ysatnaf.infrastructure.persistent.po.item.ItemNameMappingPO;
import cn.ysatnaf.types.common.PageResult; // 使用确认的路径
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 物品名称映射仓库实现
 */
@Slf4j
@Repository // 添加 @Repository 注解
@RequiredArgsConstructor
public class ItemNameMappingRepositoryImpl implements ItemNameMappingRepository {

    private final ItemNameMappingDao itemNameMappingDao;
    private final ItemNameMappingConverter converter = ItemNameMappingConverter.INSTANCE;

    @Override
    public Long insert(ItemNameMapping mapping) {
        ItemNameMappingPO po = converter.toPO(mapping);
        // 确保初始 usageCount 和 lastUsedTime 被设置 (如果 PO 中没有 FieldFill.INSERT)
        // po.setUsageCount(0); // 如果需要在这里设置
        // po.setLastUsedTime(LocalDateTime.now()); // 如果需要在这里设置
        itemNameMappingDao.insert(po);
        return po.getId();
    }

    @Override
    public boolean updateById(ItemNameMapping mapping) {
        ItemNameMappingPO po = converter.toPO(mapping);
        // 注意：usageCount 和 lastUsedTime 通常不由普通更新修改，
        // 而是由 incrementUsageCount 方法处理。确保这里的更新不会覆盖它们。
        // 如果需要允许更新这些字段，需要调整这里的逻辑。
        return itemNameMappingDao.updateById(po) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return itemNameMappingDao.deleteById(id) > 0;
    }

    @Override
    public Optional<ItemNameMapping> findById(Long id) {
        ItemNameMappingPO po = itemNameMappingDao.selectById(id);
        return Optional.ofNullable(converter.toEntity(po));
    }

    @Override
    public Optional<ItemNameMapping> findActiveByOriginalName(String originalName) {
        LambdaQueryWrapper<ItemNameMappingPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemNameMappingPO::getOriginalName, originalName)
                    .eq(ItemNameMappingPO::getIsActive, true);
        ItemNameMappingPO po = itemNameMappingDao.selectOne(queryWrapper);
        return Optional.ofNullable(converter.toEntity(po));
    }

    @Override
    public Optional<ItemNameMapping> findByOriginalName(String originalName) {
        LambdaQueryWrapper<ItemNameMappingPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemNameMappingPO::getOriginalName, originalName);
        ItemNameMappingPO po = itemNameMappingDao.selectOne(queryWrapper);
        return Optional.ofNullable(converter.toEntity(po));
    }

    @Override
    public PageResult<ItemNameMapping> page(ItemNameMappingPageReq req) {
        Page<ItemNameMappingPO> page = new Page<>(req.getPageNo(), req.getPageSize());
        LambdaQueryWrapper<ItemNameMappingPO> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.like(StrUtil.isNotBlank(req.getOriginalName()), ItemNameMappingPO::getOriginalName, req.getOriginalName());
        queryWrapper.like(StrUtil.isNotBlank(req.getMappedName()), ItemNameMappingPO::getMappedName, req.getMappedName());
        queryWrapper.eq(req.getIsActive() != null, ItemNameMappingPO::getIsActive, req.getIsActive());
        // 可以添加排序，例如按使用次数或最后使用时间
        queryWrapper.orderByDesc(ItemNameMappingPO::getLastUsedTime);

        IPage<ItemNameMappingPO> poPage = itemNameMappingDao.selectPage(page, queryWrapper);

        if (poPage == null || poPage.getRecords() == null || poPage.getRecords().isEmpty()) {
             return PageResult.empty();
        }

        List<ItemNameMapping> entityList = converter.toEntityList(poPage.getRecords());
        return new PageResult<>(entityList, poPage.getTotal());
    }

    @Override
    public boolean incrementUsageCount(Long id) {
        // 调用 DAO 中的自定义方法
        int updatedRows = itemNameMappingDao.incrementUsageCountAndUpdateTime(id, LocalDateTime.now());
        if (updatedRows == 0) {
            log.warn("更新映射使用次数失败，可能ID不存在或已被删除，ID: {}", id);
        }
        return updatedRows > 0;
    }
} 