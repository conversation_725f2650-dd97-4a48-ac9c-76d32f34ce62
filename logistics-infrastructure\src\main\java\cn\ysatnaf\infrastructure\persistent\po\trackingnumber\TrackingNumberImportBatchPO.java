package cn.ysatnaf.infrastructure.persistent.po.trackingnumber;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 单号导入批次记录持久化对象
 * <AUTHOR>
 */
@Data
@TableName("tracking_number_import_batch")
public class TrackingNumberImportBatchPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 上传操作员ID
     */
    private Long uploaderId;

    /**
     * 本次导入的目标渠道ID (FK -> tracking_number_channels.id)
     */
    private Long channelId;

    /**
     * 原始文件名
     */
    private String originalFilename;

    /**
     * 总导入数量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failedCount;

    /**
     * 导入时间
     */
    private LocalDateTime importTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 