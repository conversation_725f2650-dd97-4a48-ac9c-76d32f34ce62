package cn.ysatnaf.domain.order.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * ManifestCreateReq
 *
 * <AUTHOR>
 * @date 2023/12/22 16:00
 */
@Schema(description = "创建订单 入参")
@Data
public class OrderCreateReq {

    @Schema(description = "订单创建者openid")
    @NotNull(message = "openid不能为空")
    private String openid;

    @Schema(description = "送件人地址簿ID")
    @NotNull(message = "送件人地址簿ID不能为空")
    private Long senderAddressBookId;

    @Schema(description = "收件人地址簿ID")
    @NotNull(message = "收件人地址簿ID不能为空")
    private Long receiverAddressBookId;

    @Schema(description = "物品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "物品名称不能为空")
    private String itemName;

    @Schema(description = "物品数量")
    @NotNull(message = "物品数量不能为空")
    private Integer itemQuantity;

    @Schema(description = "预估重量")
    @NotNull(message = "预估重量不能为空")
    private BigDecimal estimatedWeight;

    @Schema(description = "预估价格")
    @NotNull(message = "预估价格不能为空")
    private BigDecimal estimatedPrice;
}
