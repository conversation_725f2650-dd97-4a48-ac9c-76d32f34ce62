package cn.ysatnaf.domain.parcelsorting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "修改分箱记录入参")
@Data
public class ParcelSoringUpdateReq {

    @Schema(description = "分箱记录ID")
    @NotNull
    private Long id;

    @Schema(description = "分箱记录名称")
    @NotBlank
    @Length(max = 32)
    private String recordName;

    @Schema(description = "提单ID")
    private Long masterBillId;
}
