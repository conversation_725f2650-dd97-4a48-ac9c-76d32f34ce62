package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.ysatnaf.domain.manifest.model.entity.MasterBill;
import cn.ysatnaf.domain.manifest.repository.MasterBillRepository;
import cn.ysatnaf.infrastructure.persistent.converter.MasterBillConverter;
import cn.ysatnaf.infrastructure.persistent.dao.MasterBillDao;
import cn.ysatnaf.infrastructure.persistent.po.MasterBillPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 主提单仓库实现类
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class MasterBillRepositoryImpl implements MasterBillRepository {

    private final MasterBillDao masterBillDao;

    @Override
    public Long insert(MasterBill masterBill) {
        MasterBillPO masterBillPO = MasterBillConverter.INSTANCE.toPO(masterBill);
        masterBillDao.insert(masterBillPO);
        return masterBillPO.getId();
    }

    @Override
    public boolean update(MasterBill masterBill) {
        MasterBillPO masterBillPO = MasterBillConverter.INSTANCE.toPO(masterBill);
        return masterBillDao.updateById(masterBillPO) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return masterBillDao.deleteById(id) > 0;
    }

    @Override
    public MasterBill getById(Long id) {
        MasterBillPO masterBillPO = masterBillDao.selectById(id);
        return MasterBillConverter.INSTANCE.toEntity(masterBillPO);
    }

    @Override
    public MasterBill getByMasterBillNumber(String masterBillNumber) {
        LambdaQueryWrapper<MasterBillPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MasterBillPO::getMasterBillNumber, masterBillNumber)
                .eq(MasterBillPO::getIsDeleted, false);
        MasterBillPO masterBillPO = masterBillDao.selectOne(queryWrapper);
        return MasterBillConverter.INSTANCE.toEntity(masterBillPO);
    }

    @Override
    public PageResult<MasterBill> page(String masterBillNumber, 
                                     LocalDateTime departureDateStart, 
                                     LocalDateTime departureDateEnd,
                                     LocalDateTime arrivalDateStart, 
                                     LocalDateTime arrivalDateEnd,
                                     LocalDate createDate,
                                     Integer timeIntervalPoint,
                                     String origin, 
                                     String destination, 
                                     String carrierCode, 
                                     Integer status,
                                     Integer pageNo, 
                                     Integer pageSize) {

        LocalDateTime createTimeStart = null;
        LocalDateTime createTimeEnd = null;
        if (createDate != null) {
            createTimeStart = createDate.atStartOfDay().withHour(timeIntervalPoint);
            createTimeEnd = createTimeStart.plusDays(1);
        }
        
        // 分页查询参数
        Page<MasterBillPO> page = new Page<>(pageNo, pageSize);
        
        // 执行分页查询
        IPage<MasterBillPO> pageResult = masterBillDao.selectPage(
                page, 
                masterBillNumber, 
                departureDateStart, 
                departureDateEnd, 
                arrivalDateStart, 
                arrivalDateEnd,
                createTimeStart,
                createTimeEnd,
                origin, 
                destination, 
                carrierCode, 
                status
        );
        
        // 转换结果
        List<MasterBillPO> records = pageResult.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageResult<>(pageResult.getTotal());
        }
        
        List<MasterBill> masterBills = MasterBillConverter.INSTANCE.toEntityList(records);
        return new PageResult<>(masterBills, pageResult.getTotal());
    }

    @Override
    public boolean updateStatistics(Long masterBillId) {
        if (ObjectUtil.isNull(masterBillId)) {
            return false;
        }
        
        return masterBillDao.updateStatistics(masterBillId) > 0;
    }

    @Override
    public List<MasterBill> listByIds(Set<Long> masterBillIds) {
        if (CollUtil.isEmpty(masterBillIds)) {
            return Collections.emptyList();
        }
        return MasterBillConverter.INSTANCE.toEntityList(masterBillDao.selectBatchIds(masterBillIds));
    }
} 