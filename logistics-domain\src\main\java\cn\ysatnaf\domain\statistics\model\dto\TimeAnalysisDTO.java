package cn.ysatnaf.domain.statistics.model.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class TimeAnalysisDTO {
    // 统计概览
    private StatisticsSummary summary;
    
    // 趋势数据
    private TrendData trend;
    
    // 模板类型对比
    private ComparisonData comparison;
    
    // 热力图数据
    private HeatmapData heatmap;
    
    @Data
    public static class StatisticsSummary {
        // 总发货数量
        private Integer totalQuantity;
        // 较上期变化百分比（原"较上月"）
        private BigDecimal quantityPeriodOnPeriodChange;
        // 总发货金额
        private BigDecimal totalAmount;
        // 较上期变化百分比（原"较上月"）
        private BigDecimal amountPeriodOnPeriodChange;
        // 平均日发货数量
        private Integer avgDailyQuantity;
        // 较上期变化百分比（原"较上月"）
        private BigDecimal avgQuantityPeriodOnPeriodChange;
        // 平均日发货金额
        private BigDecimal avgDailyAmount;
        // 较上期变化百分比（原"较上月"）
        private BigDecimal avgAmountPeriodOnPeriodChange;
    }
    
    @Data
    public static class TrendData {
        // 时间轴标签
        private List<String> timeLabels;
        
        // 当前周期数量
        private List<Integer> currentQuantity;
        // 上个周期数量
        private List<Integer> prevPeriodQuantity;
        
        // 当前周期金额
        private List<BigDecimal> currentAmount;
        // 上个周期金额
        private List<BigDecimal> prevPeriodAmount;
    }
    
    @Data
    public static class ComparisonData {
        // 发货数量分布
        private List<TypeDistribution> quantityDistribution;
        // 发货金额分布
        private List<TypeDistribution> amountDistribution;
    }
    
    @Data
    public static class TypeDistribution {
        private String name;        // 类型名称
        private Object value;       // 数量或金额
        private BigDecimal percentage; // 百分比
    }
    
    @Data
    public static class HeatmapData {
        private List<String> hours;      // 小时标签(0:00-23:00)
        private List<String> days;       // 星期标签(周一-周日)
        private List<List<Integer>> data; // 热力图数据[[hour, day, value], ...]
    }
}