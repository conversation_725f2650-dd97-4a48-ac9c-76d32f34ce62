package cn.ysatnaf.domain.manifest.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "导出预报信息入参")
@Data
public class ManifestExportPreReportInfoReq {

    @Schema(description = "运单ids")
    private List<Long> ids;

    @Schema(description = "预报时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date preReportedTimeFrom;

    @Schema(description = "预报时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date preReportedTimeTo;
}
