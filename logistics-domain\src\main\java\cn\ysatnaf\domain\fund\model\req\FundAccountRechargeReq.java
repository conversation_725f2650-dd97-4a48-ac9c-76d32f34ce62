package cn.ysatnaf.domain.fund.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */

@Schema(description = "充值 入参")
@Data
public class FundAccountRechargeReq {

    @Schema(description = "账户ID")
    private Long accountId;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String ownPassword;
}
