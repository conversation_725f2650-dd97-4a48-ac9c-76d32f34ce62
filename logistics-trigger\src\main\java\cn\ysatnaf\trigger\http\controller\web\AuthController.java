package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.auth.req.AuthWebLoginReq;
import cn.ysatnaf.domain.auth.res.AuthWebLoginRes;
import cn.ysatnaf.domain.auth.service.WebAuthService;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;

/**
 * <AUTHOR>
 */
@Tag(name = "网页登录等")
@RequestMapping("/web/auth")
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
public class AuthController {

    private final WebAuthService webAuthService;

    @PostMapping("/login")
    @PermitAll
    @Operation(summary = "登录")
    public CommonResult<AuthWebLoginRes> login(@RequestBody @Validated AuthWebLoginReq req) {
        return CommonResult.success(webAuthService.login(req));
    }
}
