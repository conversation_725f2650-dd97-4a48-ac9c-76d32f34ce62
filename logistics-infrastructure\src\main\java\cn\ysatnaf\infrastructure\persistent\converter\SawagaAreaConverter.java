package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.address.model.entity.SawagaAreaEntity;
import cn.ysatnaf.infrastructure.persistent.po.SawagaAreaPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface SawagaAreaConverter {

    SawagaAreaConverter INSTANCE = Mappers.getMapper(SawagaAreaConverter.class);

    SawagaAreaEntity toEntity(SawagaAreaPO po);
}
