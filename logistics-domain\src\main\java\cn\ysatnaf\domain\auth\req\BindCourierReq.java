package cn.ysatnaf.domain.auth.req;

import cn.ysatnaf.types.validation.Phone;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * BindByMobileNumberReq
 *
 * <AUTHOR>
 * @date 2024/2/7 15:12
 */
@Schema(description = "快递员绑定手机号 请求入参")
@Data
public class BindCourierReq {

    @Schema(description = "openid", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String openid;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @Phone
    @NotBlank
    private String mobileNumber;
}
