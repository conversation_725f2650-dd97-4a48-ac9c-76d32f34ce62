package cn.ysatnaf.infrastructure.persistent.converter.trackingnumber;

import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberPool;
import cn.ysatnaf.domain.trackingnumber.model.valobj.TrackingNumberStatus;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberPoolPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 预报单号池对象转换器
 * <AUTHOR>
 */
@Mapper
public interface TrackingNumberPoolConverter {

    TrackingNumberPoolConverter INSTANCE = Mappers.getMapper(TrackingNumberPoolConverter.class);

    @Mapping(source = "status", target = "status", qualifiedByName = "integerToTrackingNumberStatus")
    TrackingNumberPool toEntity(TrackingNumberPoolPO poolPO);

    @Mapping(source = "status", target = "status", qualifiedByName = "trackingNumberStatusToInteger")
    TrackingNumberPoolPO toPO(TrackingNumberPool pool);
    
    List<TrackingNumberPool> toEntityList(List<TrackingNumberPoolPO> poolPOs);
    
    List<TrackingNumberPoolPO> toPOList(List<TrackingNumberPool> pools);

    @Named("integerToTrackingNumberStatus")
    default TrackingNumberStatus integerToTrackingNumberStatus(Integer code) {
        return TrackingNumberStatus.fromCode(code);
    }

    @Named("trackingNumberStatusToInteger")
    default Integer trackingNumberStatusToInteger(TrackingNumberStatus status) {
        return status != null ? status.getCode() : null;
    }
} 