package cn.ysatnaf.domain.shippingfeetemplate.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "运费模板绑定入参")
@Data
public class ShippingFeeTemplateBindReq {

    @Schema(description = "用户ID")
    @NotNull
    private Long userId;

    @Schema(description = "普通模板ID")
    @NotNull
    private Long generalTemplateId;

    @Schema(description = "带电模板ID")
    @NotNull
    private Long electronicsTemplateId;

    @Schema(description = "投函模板ID")
    @NotNull
    private Long smallParcelTemplateId;
}
