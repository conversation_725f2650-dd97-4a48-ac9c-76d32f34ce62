package cn.ysatnaf.domain.auth;

import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;

/**
 * <AUTHOR>
 */
public class LoginUserHolder {

    private static final ThreadLocal<LoginUserEntity> loginUserHolder = new ThreadLocal<>();

    public static LoginUserEntity getLoginUser() {
        return loginUserHolder.get();
    }

    public static void setLoginUser(LoginUserEntity loginUser) {
        loginUserHolder.set(loginUser);
    }

    public static void clearLoginUser() {
        loginUserHolder.remove();
    }
}
