package cn.ysatnaf.domain.item.model.dto;

import cn.ysatnaf.types.common.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "违禁关键词分页查询请求")
public class ProhibitedItemKeywordPageReq extends PageParam {

    @Schema(description = "关键词 (模糊查询)", example = "Batt")
    private String keyword;

    @Schema(description = "是否启用 (精确查询)", example = "true")
    private Boolean isActive;

} 