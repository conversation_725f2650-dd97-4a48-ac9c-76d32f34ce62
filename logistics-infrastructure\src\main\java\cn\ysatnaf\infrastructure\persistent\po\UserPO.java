package cn.ysatnaf.infrastructure.persistent.po;

import cn.ysatnaf.domain.user.model.valobj.GenderVO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * UserPO
 *
 * <AUTHOR> Hang
 * @date 2023/12/21 11:17
 */
@Data
@TableName("tb_user")
public class UserPO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 密码
     */
    private String password;

    /**
     * 第三方平台唯一标识
     */
    private String openid;

    /**
     * 第三方平台类型
     */
    private Integer socialType;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 备注
     */
    private String remark;

    /**
     * city	普通用户个人资料填写的城市
     */
    private String city;

    /**
     * province	普通用户个人资料填写的省份
     */
    private String province;
    /**
     * country	国家，如中国为CN
     */
    private String country;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 用户性别
     * 枚举类 {@link GenderVO}
     */
    private Integer gender;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 帐号状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 最后登录IP
     */
    private String loginIp;
    /**
     * 最后登录时间
     */
    private LocalDateTime loginDate;
}
