package cn.ysatnaf.domain.order.repository;

import cn.ysatnaf.domain.order.model.entity.OrderEntity;
import cn.ysatnaf.types.common.PageResult;

/**
 * <AUTHOR> Hang
 */
public interface OrderRepository {

    void insert(OrderEntity orderEntity);

    PageResult<OrderEntity> search(String openid, String expressNo, String orderNo, Integer status, Integer pageNo, Integer pageSize);

    OrderEntity getById(Long orderId);

    void updateById(OrderEntity orderEntity);

    PageResult<OrderEntity> pageMpOrder(Integer status, Integer pageNo, Integer pageSize);

    Integer countByStatus(Integer status);
}
