package cn.ysatnaf.domain.excel.serviice.generator.converter;

import cn.ysatnaf.domain.excel.serviice.generator.row.OsakaCustomsDocumentItemExcelRow;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import cn.ysatnaf.domain.manifest.service.ManifestItemService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@RequiredArgsConstructor
public class OsakaCustomsDocumentItemExcelConverter {

    private final ManifestItemService manifestItemService;

    /**
     * 将manifest转换为excel数据
     */
    public List<OsakaCustomsDocumentItemExcelRow> convert(List<Manifest> manifests) {

        // 转换成对应的excel对象
        List<OsakaCustomsDocumentItemExcelRow> manifestExcelData = new ArrayList<>();
        Set<String> expressNumbers = new HashSet<>();
        Set<Long> manifestIds = new HashSet<>();
        Map<Long, Manifest> manifestMap = new HashMap<>(manifests.size());
        for (int i = 1; i < manifests.size(); i++) {
            Manifest manifest = manifests.get(i);
            manifestIds.add(manifest.getId());
            manifestMap.put(manifest.getId(), manifest);
        }
        List<ManifestItem> manifestItems = manifestItemService.listBatchByManifestIds(manifestIds);
        for (ManifestItem manifestItem : manifestItems) {
            Manifest manifest = manifestMap.get(manifestItem.getManifestId());
            String trackingNumber = manifest.getTrackingNumber();
            if (expressNumbers.contains(trackingNumber)) {
                continue;
            }
            expressNumbers.add(trackingNumber);
            OsakaCustomsDocumentItemExcelRow row = new OsakaCustomsDocumentItemExcelRow();
            row.setHawb(trackingNumber);
            row.setProductNameInvoice(manifestItem.getNameEn());
            row.setProductNameLocal(manifestItem.getName());
            row.setQuantity(manifestItem.getQuantity());
            row.setTotalPrice(manifest.getValue());
            manifestExcelData.add(row);
        }

        return manifestExcelData;
    }
}
