package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.infrastructure.persistent.po.ReceiverAddressBookPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ReceiverAddressDao
 *
 * <AUTHOR>
 * @date 2023/12/22 15:44
 */
@Mapper
public interface ReceiverAddressBookDao extends BaseMapper<ReceiverAddressBookPO> {
    void updateBatchById(@Param("list") List<ReceiverAddressBookPO> list);
}
