package cn.ysatnaf.domain.cost.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "更新计价规则 入参")
@Data
public class CostRuleUpdateReq {

    @Schema(description = "计价规则使用平台类型：1-WEB端；2-公众号端", requiredMode = REQUIRED)
    @NotNull
    private Integer platformType;

    @Schema(description = "价格区间规则列表")
    @NotEmpty
    private List<CostRangeRuleDTO> priceRangeRules;

    @Schema(description = "拋比系数，计算体积重量的时候作为除数使用")
    private BigDecimal throwRatio;
}
