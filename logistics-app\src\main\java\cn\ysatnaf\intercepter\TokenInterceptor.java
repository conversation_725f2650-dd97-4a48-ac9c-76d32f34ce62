package cn.ysatnaf.intercepter;

import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.types.common.ErrorCodeConstants;
import cn.ysatnaf.types.constants.CacheConstants;
import cn.ysatnaf.types.util.ServiceExceptionUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.TimeUnit;

/**
 * TokenInterceptor
 *
 * <AUTHOR> Hang
 * @date 2023/12/21 20:55
 */
@Component
@RequiredArgsConstructor
public class TokenInterceptor implements HandlerInterceptor {

    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("token");
        if (StrUtil.isBlank(token)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_TOKEN_EXPIRED);
        }

        String loginUserJson = stringRedisTemplate.opsForValue().get(String.format(CacheConstants.LOGIN_USER, token));
        if (StrUtil.isBlank(loginUserJson)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_TOKEN_EXPIRED);
        }

        // token续期
        stringRedisTemplate.expire(String.format(CacheConstants.LOGIN_USER, token), 1, TimeUnit.DAYS);
        LoginUserEntity loginUserEntity = JSON.parseObject(loginUserJson, LoginUserEntity.class);
        LoginUserHolder.setLoginUser(loginUserEntity);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        LoginUserHolder.clearLoginUser();
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
