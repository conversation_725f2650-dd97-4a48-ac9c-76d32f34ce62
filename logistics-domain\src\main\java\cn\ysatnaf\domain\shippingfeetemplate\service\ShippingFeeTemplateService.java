package cn.ysatnaf.domain.shippingfeetemplate.service;

import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplatePO;
import cn.ysatnaf.domain.shippingfeetemplate.model.req.ShippingFeeTemplateBindReq;
import cn.ysatnaf.domain.shippingfeetemplate.model.req.ShippingFeeTemplateCreateReq;
import cn.ysatnaf.domain.shippingfeetemplate.model.req.ShippingFeeTemplateListReq;
import cn.ysatnaf.domain.shippingfeetemplate.model.req.ShippingFeeTemplateUpdateReq;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;

import java.util.List;

/**
 * 运费模板业务层接口
 */
public interface ShippingFeeTemplateService {

    /**
     * 创建运费模板
     *
     * @param req 创建运费模板入参
     */
    void create(ShippingFeeTemplateCreateReq req);

    /**
     * 更新运费模板
     *
     * @param req 更新运费模板入参
     */
    void update(ShippingFeeTemplateUpdateReq req);

    /**
     * 删除运费模板
     *
     * @param id 模板ID
     */
    void delete(Long id);

    /**
     * 运费模板列表
     *
     * @param req 查看运费模板列表入参
     * @return 运费模板列表
     */
    List<ShippingFeeTemplatePO> list(ShippingFeeTemplateListReq req);

    /**
     * 获取运费模板
     *
     * @param templateId 运费模板ID
     * @param type       运费模板类型 {@link ShippingFeeTemplateTypeEnum#getCode()}
     * @param userId     用户ID
     * @return 运费模板
     */
    ShippingFeeTemplatePO get(Long templateId, Integer type, Long userId);

    /**
     * 绑定运费模板
     *
     * @param req 绑定运费模板入参
     */
    void bind(ShippingFeeTemplateBindReq req);
}
