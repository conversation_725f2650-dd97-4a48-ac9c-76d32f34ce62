package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.fund.model.req.*;
import cn.ysatnaf.domain.fund.model.res.BalanceDetailPageRes;
import cn.ysatnaf.domain.fund.model.res.FundAccountGetRes;
import cn.ysatnaf.domain.fund.service.BalanceDetailService;
import cn.ysatnaf.domain.fund.service.FundAccountService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> Hang
 */
@Tag(name = "资金账户管理")
@RequestMapping("/web/fundAccount")
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
public class FundAccountController {

    private final FundAccountService fundAccountService;

    private final BalanceDetailService balanceDetailService;

    @Operation(summary = "获取资金账户")
    @PermitAll
    @PostMapping("/get")
    public CommonResult<FundAccountGetRes> get(@RequestBody FundAccountGetReq req) {
        return CommonResult.success(fundAccountService.get(req));
    }
    
//    @Operation(summary = "更新资金账户余额")
//    @PermitAll
//    @PostMapping("/update")
//    public CommonResult<Boolean> update(@RequestBody FundAccountUpdateReq req) {
//        return CommonResult.success(fundAccountService.update(req));
//    }

    @Operation(summary = "充值")
    @PermitAll
    @PostMapping("/recharge")
    public CommonResult<Boolean> recharge(@RequestBody FundAccountRechargeReq req) {
        return CommonResult.success(fundAccountService.recharge(req));
    }

    @Operation(summary = "获取账户变动明细")
    @PermitAll
    @PostMapping("/getBalanceDetails")
    public CommonResult<PageResult<BalanceDetailPageRes>> pageBalanceDetails(@RequestBody BalanceDetailPageReq req) {
        return CommonResult.success(balanceDetailService.pageBalanceDetails(req));
    }

    @Operation(summary = "导出账户变动明细")
    @PermitAll
    @PostMapping("/exportBalanceDetails")
    public void exportBalanceDetails(@RequestBody BalanceDetailExportReq req, HttpServletResponse response) {
        balanceDetailService.exportBalanceDetails(req, response);
    }
    
    
}
