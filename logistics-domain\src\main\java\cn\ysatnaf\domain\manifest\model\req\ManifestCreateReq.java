package cn.ysatnaf.domain.manifest.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * ManifestCreateReq
 *
 * <AUTHOR>
 * @date 2023/12/22 16:00
 */
@Schema(description = "发起货单 入参")
@Data
public class ManifestCreateReq {

    @Schema(description = "送件人地址簿ID")
    private Long sendAddressBookId;

    @Schema(description = "收件人地址簿ID")
    private Long receiverAddressBookId;

    @Schema(description = "物品信息")
    private String description;

    @Schema(description = "预估重量")
    private BigDecimal weight;
}
