package cn.ysatnaf.domain.trackingnumber.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 单号分配批次记录领域实体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingNumberAllocationBatch {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 分配操作员ID
     */
    private Long allocatorId;

    /**
     * 请求分配的地点ID (FK -> locations.id)
     */
    private Long requestedLocationId;

    /**
     * 请求分配的货物类型ID (FK -> shipment_types.id)
     */
    private Long requestedShipmentTypeId;

    /**
     * 分配目标客户账户ID
     */
    private Long customerAccountId;

    /**
     * 请求分配的数量
     */
    private Integer quantityRequested;

    /**
     * 实际分配的数量
     */
    private Integer quantityAllocated;

    /**
     * 分配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime allocationTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 分配者姓名 (非持久化字段，查询时填充)
     */
    private String allocatorName;

    /**
     * 客户账户昵称 (非持久化字段，查询时填充)
     */
    @Schema(description = "客户账户昵称")
    private String customerAccountName;

    /**
     * 是否已导出 (0:否, 1:是)
     */
    private Boolean isExported;

    /**
     * 是否已打印 (0:否, 1:是)
     */
    private Boolean isPrinted;

    /**
     * 请求的渠道代码 (冗余字段，查询时填充)
     * Note: Populating this might require extra lookups based on Location/ShipmentType
     */
    private String channelCode;

    /**
     * 请求的渠道名称 (冗余字段，查询时填充)
     * Note: Populating this might require extra lookups based on Location/ShipmentType
     */
    private String channelName;
} 