package cn.ysatnaf.domain.item.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder; // 假设用于获取当前用户ID
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingAddReq;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingDTO;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingPageReq;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingUpdateReq;
import cn.ysatnaf.domain.item.model.entity.ItemNameMapping;
import cn.ysatnaf.domain.item.repository.ItemNameMappingRepository;
import cn.ysatnaf.domain.item.service.ItemNameMappingService;
import cn.ysatnaf.types.common.PageResult; // 使用确认的路径
import cn.ysatnaf.types.exception.ServiceException; // 假设有通用的业务异常类
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 物品名称映射服务实现
 */
@Slf4j
@Service // 添加 @Service 注解
@RequiredArgsConstructor
public class ItemNameMappingServiceImpl implements ItemNameMappingService {

    private final ItemNameMappingRepository itemNameMappingRepository;

    @Override
    @Transactional
    public Long addMapping(ItemNameMappingAddReq req) {
        // 检查原始名称是否已存在映射
        itemNameMappingRepository.findByOriginalName(req.getOriginalName().trim()).ifPresent(m -> {
            throw new ServiceException("原始名称 '" + req.getOriginalName() + "' 的映射已存在");
        });

        ItemNameMapping mapping = ItemNameMapping.builder()
                .originalName(req.getOriginalName().trim())
                .mappedName(req.getMappedName().trim())
                .isActive(req.getIsActive())
                .usageCount(0) // 初始使用次数为0
                .lastUsedTime(LocalDateTime.now()) // 设置初始时间
                .remarks(req.getRemarks())
                // .creatorId(LoginUserHolder.getUserId())
                .build();

        return itemNameMappingRepository.insert(mapping);
    }

    @Override
    @Transactional
    public boolean updateMapping(ItemNameMappingUpdateReq req) {
        ItemNameMapping existingMapping = itemNameMappingRepository.findById(req.getId())
                .orElseThrow(() -> new ServiceException("ID 为 " + req.getId() + " 的映射不存在"));

        // 检查是否需要更新 mappedName
        if (req.getMappedName() != null) {
            existingMapping.setMappedName(req.getMappedName().trim());
        }
        // 检查是否需要更新 isActive 状态
        if (req.getIsActive() != null) {
            existingMapping.setIsActive(req.getIsActive());
        }
        // 检查是否需要更新 remarks
        if (req.getRemarks() != null) {
            existingMapping.setRemarks(req.getRemarks());
        }
        // existingMapping.setUpdaterId(LoginUserHolder.getUserId());

        return itemNameMappingRepository.updateById(existingMapping);
    }

    @Override
    @Transactional
    public boolean deleteMapping(Long id) {
        itemNameMappingRepository.findById(id)
                .orElseThrow(() -> new ServiceException("ID 为 " + id + " 的映射不存在"));
        return itemNameMappingRepository.deleteById(id);
    }

    @Override
    public ItemNameMappingDTO getMappingById(Long id) {
        return itemNameMappingRepository.findById(id)
                .map(entity -> BeanUtil.copyProperties(entity, ItemNameMappingDTO.class))
                .orElse(null);
    }

    @Override
    public PageResult<ItemNameMappingDTO> pageMappings(ItemNameMappingPageReq req) {
        PageResult<ItemNameMapping> entityPageResult = itemNameMappingRepository.page(req);

        if (entityPageResult == null || entityPageResult.getList() == null || entityPageResult.getList().isEmpty()) {
            return PageResult.empty();
        }

        List<ItemNameMappingDTO> dtoList = entityPageResult.getList().stream()
                .map(entity -> BeanUtil.copyProperties(entity, ItemNameMappingDTO.class))
                .collect(Collectors.toList());

        return new PageResult<>(dtoList, entityPageResult.getTotal());
    }

    @Override
    @Transactional // 需要事务，因为包含更新操作
    public Optional<String> findAndUseActiveMappedName(String originalName) {
        if (originalName == null || originalName.trim().isEmpty()) {
            return Optional.empty();
        }
        // 查找启用的映射
        Optional<ItemNameMapping> mappingOpt = itemNameMappingRepository.findActiveByOriginalName(originalName.trim());

        if (mappingOpt.isPresent()) {
            ItemNameMapping mapping = mappingOpt.get();
            // 异步或同步更新使用次数和时间，这里简化为同步调用
            boolean updated = itemNameMappingRepository.incrementUsageCount(mapping.getId());
            if (!updated) {
                log.warn("尝试增加映射使用次数失败，映射ID: {}", mapping.getId());
                // 可以选择是否因为更新失败而阻止返回映射名称，取决于业务需求
            }
            return Optional.of(mapping.getMappedName());
        }

        return Optional.empty(); // 未找到启用的映射
    }

    @Override
    @Transactional // Ensure atomicity for the check-then-act operation
    public Long upsertMapping(ItemNameMappingAddReq req) {
        String originalNameTrimmed = req.getOriginalName().trim();
        String mappedNameTrimmed = req.getMappedName().trim();

        // 1. 尝试根据 originalName 查找现有映射
        Optional<ItemNameMapping> existingMappingOpt = itemNameMappingRepository.findByOriginalName(originalNameTrimmed);

        if (existingMappingOpt.isPresent()) {
            // --- 更新逻辑 ---
            ItemNameMapping existingMapping = existingMappingOpt.get();
            log.info("Found existing mapping for '{}', updating ID: {}", originalNameTrimmed, existingMapping.getId());

            boolean needsUpdate = false;
            if (!mappedNameTrimmed.equals(existingMapping.getMappedName())) {
                existingMapping.setMappedName(mappedNameTrimmed);
                needsUpdate = true;
            }
            if (!req.getIsActive().equals(existingMapping.getIsActive())) {
                existingMapping.setIsActive(req.getIsActive());
                needsUpdate = true;
            }
            // Remarks can always be updated if provided (or set to null/empty)
            String reqRemarks = req.getRemarks() == null ? "" : req.getRemarks();
             if (!reqRemarks.equals(existingMapping.getRemarks() == null ? "" : existingMapping.getRemarks())) {
                 existingMapping.setRemarks(reqRemarks);
                 needsUpdate = true;
             }

            // Optional: Set updaterId
            // existingMapping.setUpdaterId(LoginUserHolder.getUserId());
            // existingMapping.setUpdateTime(LocalDateTime.now()); // Or rely on DB auto-update

            if (needsUpdate) {
                boolean updated = itemNameMappingRepository.updateById(existingMapping);
                if (!updated) {
                    // Handle potential update failure (e.g., concurrent modification)
                     log.error("Failed to update existing item name mapping for ID: {}", existingMapping.getId());
                     throw new ServiceException("更新物品名称映射失败，请重试");
                }
            } else {
                 log.info("No changes detected for existing mapping ID: {}", existingMapping.getId());
            }
            return existingMapping.getId(); // 返回现有记录的 ID

        } else {
            // --- 新增逻辑 ---
            log.info("No existing mapping found for '{}', creating new.", originalNameTrimmed);
            ItemNameMapping newMapping = ItemNameMapping.builder()
                    .originalName(originalNameTrimmed)
                    .mappedName(mappedNameTrimmed)
                    .isActive(req.getIsActive())
                    .usageCount(0) // New mapping starts with 0 usage
                    .lastUsedTime(LocalDateTime.now()) // Set initial time
                    .remarks(req.getRemarks())
                    // Optional: Set creatorId
                    // .creatorId(LoginUserHolder.getUserId())
                    // .createTime(LocalDateTime.now()) // Or rely on DB default/auto-fill
                    // .updateTime(LocalDateTime.now())
                    .build();

            Long newId = itemNameMappingRepository.insert(newMapping);
             if (newId == null) {
                 log.error("Failed to insert new item name mapping for original name: {}", originalNameTrimmed);
                 throw new ServiceException("添加物品名称映射失败，请重试");
             }
            return newId; // 返回新创建记录的 ID
        }
    }
} 