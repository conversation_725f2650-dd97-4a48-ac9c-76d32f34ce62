package cn.ysatnaf.config;

import cn.ysatnaf.intercepter.TokenInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private final TokenInterceptor tokenInterceptor;

    private final ApplicationContext applicationContext;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tokenInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                        "/mp/**",
                        "/auth/**",
                        "/web/auth/login",
                        "/web/auth/logout",
                        "/web/captcha/get",
                        "/web/tracking/**",
                        "/web/cost/calculate",
                        "/web/tracking/fetchSawagaInfo",
                        "/web/manifest/batchSearchByExpressNumbers",
                        "/api/logistics/tracking/**",
                        "/api/statistics/**",
                        "/swagger-ui.html/**", "/swagger/**", "/swagger-ui/**", "/swagger-resources/**", "/v3/**");
//                .excludePathPatterns(Lists.newArrayList(getPermitAllUrlsFromAnnotations().values()));
    }

//    private Multimap<HttpMethod, String> getPermitAllUrlsFromAnnotations() {
//        Multimap<HttpMethod, String> result = HashMultimap.create();
//        // 获得接口对应的 HandlerMethod 集合
//        RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping)
//                applicationContext.getBean("requestMappingHandlerMapping");
//        Map<RequestMappingInfo, HandlerMethod> handlerMethodMap = requestMappingHandlerMapping.getHandlerMethods();
//        // 获得有 @PermitAll 注解的接口
//        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethodMap.entrySet()) {
//            HandlerMethod handlerMethod = entry.getValue();
//            if (!handlerMethod.hasMethodAnnotation(PermitAll.class)) {
//                continue;
//            }
//            if (entry.getKey().getPatternsCondition() == null) {
//                continue;
//            }
//            Set<String> urls = entry.getKey().getPatternsCondition().getPatterns();
//            // 特殊：使用 @RequestMapping 注解，并且未写 method 属性，此时认为都需要免登录
//            Set<RequestMethod> methods = entry.getKey().getMethodsCondition().getMethods();
//            if (CollUtil.isEmpty(methods)) { //
//                result.putAll(HttpMethod.GET, urls);
//                result.putAll(HttpMethod.POST, urls);
//                result.putAll(HttpMethod.PUT, urls);
//                result.putAll(HttpMethod.DELETE, urls);
//                continue;
//            }
//            // 根据请求方法，添加到 result 结果
//            entry.getKey().getMethodsCondition().getMethods().forEach(requestMethod -> {
//                switch (requestMethod) {
//                    case GET:
//                        result.putAll(HttpMethod.GET, urls);
//                        break;
//                    case POST:
//                        result.putAll(HttpMethod.POST, urls);
//                        break;
//                    case PUT:
//                        result.putAll(HttpMethod.PUT, urls);
//                        break;
//                    case DELETE:
//                        result.putAll(HttpMethod.DELETE, urls);
//                        break;
//                }
//            });
//        }
//        return result;
//    }
}