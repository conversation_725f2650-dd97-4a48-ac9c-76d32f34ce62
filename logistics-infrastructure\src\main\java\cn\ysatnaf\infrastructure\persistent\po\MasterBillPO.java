package cn.ysatnaf.infrastructure.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 主提单数据库实体类
 * <AUTHOR>
 */
@Data
@TableName("tb_master_bill")
public class MasterBillPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 提单号/航班号
     */
    @TableField("master_bill_number")
    private String masterBillNumber;

    /**
     * 起飞日期
     */
    @TableField("departure_date")
    private LocalDateTime departureDate;

    /**
     * 到达日期
     */
    @TableField("arrival_date")
    private LocalDateTime arrivalDate;

    /**
     * 始发地
     */
    @TableField("origin")
    private String origin;

    /**
     * 目的地
     */
    @TableField("destination")
    private String destination;

    /**
     * 承运商代码
     */
    @TableField("carrier_code")
    private String carrierCode;

    /**
     * 提单状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 总重量
     */
    @TableField("total_weight")
    private BigDecimal totalWeight;

    /**
     * 总体积
     */
    @TableField("total_volume")
    private BigDecimal totalVolume;

    /**
     * 运单数量
     */
    @TableField("waybill_count")
    private Integer waybillCount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建者ID
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
} 