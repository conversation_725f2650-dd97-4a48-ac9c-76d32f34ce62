package cn.ysatnaf.domain.manifest.model.valobj;

import cn.hutool.core.date.DateUtil;
import cn.ysatnaf.domain.manifest.model.dto.StatisticsTimeRangeDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR> Hang
 */
@Getter
@AllArgsConstructor
public enum StatisticsTimeType {

    YEAR() {
        @Override
        public StatisticsTimeRangeDTO calcTimeRange(Date date) {
            return StatisticsTimeRangeDTO.builder()
                    .startTime(DateUtil.beginOfYear(date))
                    .endTime(DateUtil.endOfYear(date))
                    .build();
        }
    },
    MONTH() {
        @Override
        public StatisticsTimeRangeDTO calcTimeRange(Date date) {
            return StatisticsTimeRangeDTO.builder()
                    .startTime(DateUtil.beginOfMonth(date))
                    .endTime(DateUtil.endOfMonth(date))
                    .build();
        }
    },
    DATE() {
        @Override
        public StatisticsTimeRangeDTO calcTimeRange(Date date) {
            return StatisticsTimeRangeDTO.builder()
                    .startTime(DateUtil.beginOfDay(date))
                    .endTime(DateUtil.endOfDay(date))
                    .build();
        }
    },
    ;

    public abstract StatisticsTimeRangeDTO calcTimeRange(Date date);
}
