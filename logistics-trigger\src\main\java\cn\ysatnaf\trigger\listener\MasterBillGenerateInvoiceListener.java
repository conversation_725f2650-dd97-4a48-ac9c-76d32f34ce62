package cn.ysatnaf.trigger.listener;

import cn.ysatnaf.domain.manifest.service.InvoiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class MasterBillGenerateInvoiceListener {

    private final InvoiceService invoiceService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = "masterBill.generate.invoice.queue", durable = "true"),
            exchange = @Exchange(name = "generate.exchange"),
            key = {"masterBill"}
    ))
    public void listen(Long taskId) {
        invoiceService.masterBillGenerate(taskId);
    }
}
