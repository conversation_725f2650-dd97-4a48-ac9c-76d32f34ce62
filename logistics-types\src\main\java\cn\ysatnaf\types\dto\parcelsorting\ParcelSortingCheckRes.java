package cn.ysatnaf.types.dto.parcelsorting;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 装箱检测响应 DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "装箱检测响应")
public class ParcelSortingCheckRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "运单ID")
    private Long manifestId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "快递单号 (运单号)")
    private String expressNumber;

    @Schema(description = "收件人姓名")
    private String receiverName;

} 