package cn.ysatnaf.domain.captcha.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.captcha.service.CaptchaService;
import cn.ysatnaf.domain.captche.model.entity.CaptchaEntity;
import cn.ysatnaf.types.common.ErrorCodeConstants;
import cn.ysatnaf.types.constants.CacheConstants;
import cn.ysatnaf.types.exception.ServerException;
import cn.ysatnaf.types.exception.ServiceException;
import cn.ysatnaf.types.util.RedisCache;
import cn.ysatnaf.types.util.ServiceExceptionUtil;
import com.google.code.kaptcha.Producer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.FastByteArrayOutputStream;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Hang
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CaptchaServiceImpl implements CaptchaService {

    private final RedisCache redisCache;

    private final Producer captchaProducer;

    @Override
    public void validate(String username, String captcha, String captchaId) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + captchaId;
        String captchaCode = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (StrUtil.isBlank(captchaCode)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_LOGIN_CAPTCHA_CODE_TIMEOUT);
        }
        if (!StrUtil.equalsIgnoreCase(captcha, captchaCode)) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_LOGIN_CAPTCHA_CODE_ERROR);
        }
    }

    @Override
    public CaptchaEntity get() {
        // 保存验证码信息
        String captchaId = IdUtil.fastSimpleUUID();
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + captchaId;

        String capStr, code;
        BufferedImage image;

        // 生成验证码
        String capText = captchaProducer.createText();
        capStr = capText.substring(0, capText.lastIndexOf("@"));
        code = capText.substring(capText.lastIndexOf("@") + 1);
        image = captchaProducer.createImage(capStr);

        redisCache.setCacheObject(verifyKey, code, 3, TimeUnit.MINUTES);
        // 转换流信息写出
        FastByteArrayOutputStream os = new FastByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", os);
        } catch (IOException e) {
            log.error("[生成验证码图片错误: ]", e);
            throw new ServiceException(ErrorCodeConstants.SYSTEM_ERROR);
        }

        CaptchaEntity captchaEntity = new CaptchaEntity();
        captchaEntity.setCaptchaId(captchaId);
        captchaEntity.setImage(Base64.encode(os.toByteArray()));
        os.close();
        return captchaEntity;
    }
}
