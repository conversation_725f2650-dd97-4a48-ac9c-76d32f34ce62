package cn.ysatnaf.domain.auth.model.entity;

import cn.ysatnaf.domain.user.model.valobj.GenderVO;
import cn.ysatnaf.domain.user.model.valobj.RoleVO;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LoginUserEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 手机号码
     */
    private String phone;

    /**
     * city	普通用户个人资料填写的城市
     */
    private String city;

    /**
     * province	普通用户个人资料填写的省份
     */
    private String province;
    /**
     * country	国家，如中国为CN
     */
    private String country;
    /**
     * 用户性别
     *
     * 枚举类 {@link GenderVO}
     */
    private Integer gender;

    private Long roleId;
    /**
     * 用户头像
     */
    private String avatar;

    public boolean ifSuperAdmin() {
        return roleId.equals(RoleVO.SUPER_ADMIN.getCode());
    }

    public boolean ifAdmin() {
        return roleId.equals(RoleVO.ADMIN.getCode()) || roleId.equals(RoleVO.SUPER_ADMIN.getCode());
    }

    public boolean ifLocalUser() {
        return roleId.equals(RoleVO.LOCAL_USER.getCode());
    }

    public boolean ifNetworkUser() {
        return roleId.equals(RoleVO.NETWORK_USER.getCode());
    }
}
