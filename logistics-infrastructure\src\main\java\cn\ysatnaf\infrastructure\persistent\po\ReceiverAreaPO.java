package cn.ysatnaf.infrastructure.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * ReceiverAreaPO
 * 收件地址相关
 * <AUTHOR>
 * @date 2023/12/22 11:42
 */
@Data
@TableName("tb_receiver_area")
public class ReceiverAreaPO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 日本都道府县级名称
     */
    private String prefectureName;
    /**
     * 日本市区町村级名称
     */
    private String municipalName;
    /**
     * 日本丁目级名称
     */
    private String localitiesName;

    /**
     * 日本都道府县级名称(英文)
     */
    private String prefectureEnName;
    /**
     * 日本市区町村级名称(英文)
     */
    private String municipalEnName;
    /**
     * 日本丁目级名称(英文)
     */
    private String localitiesEnName;

}
