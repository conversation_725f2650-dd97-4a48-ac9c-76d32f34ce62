package cn.ysatnaf.domain.trackingnumber.model.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用于读取单号导入Excel的监听器
 * <AUTHOR>
 */
@Slf4j
@Getter
public class TrackingNumberExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    // 存储读取到的单号 (假设单号在第一列)
    private final List<String> trackingNumbers = new ArrayList<>();
    private static final int TRACKING_NUMBER_COLUMN_INDEX = 0; // 单号所在列索引 (0-based)

    /**
     * 每解析一行数据会调用此方法
     * @param data            one row value. Is is same as {@link #invoke(Object, AnalysisContext)}.
     * @param context
     */
    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        // Excel数据是 Map<列索引, 单元格字符串值>
        String trackingNumber = data.get(TRACKING_NUMBER_COLUMN_INDEX);
        if (trackingNumber != null && !trackingNumber.trim().isEmpty()) {
            trackingNumbers.add(trackingNumber.trim());
        } else {
            // 根据需要处理空行或空格，这里选择忽略
            log.debug("忽略空单号或空格行，行号: {}", context.readRowHolder().getRowIndex() + 1);
        }
    }

    /**
     * 所有数据解析完成后会调用此方法
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel单号读取完成，共读取到 {} 条有效单号", trackingNumbers.size());
    }

    /**
     * 处理表头数据
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 校验表头是否符合预期 ("单号")
        String expectedHeader = "单号";
        String actualHeader = headMap.get(TRACKING_NUMBER_COLUMN_INDEX);
        if (actualHeader == null || !expectedHeader.equals(actualHeader.trim())) {
            log.error("Excel表头格式错误，第一列应为 '{}'，实际为 '{}'", expectedHeader, actualHeader);
            // 可以选择抛出异常中断读取
            throw new RuntimeException("导入失败：Excel表头格式错误，第一列应为 '单号'");
        }
         log.info("Excel表头读取成功: {}", headMap);
    }
    
    /**
     * 发生异常时调用
     * @param exception
     * @param context
     * @throws Exception
     */
    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("解析Excel行号 {} 时发生异常:", context.readRowHolder().getRowIndex() + 1, exception);
        // 如果是数据转换异常，可以提供更友好的提示
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException dataConvertException = (ExcelDataConvertException) exception;
            log.error("数据转换错误: 第{}行，第{}列，数据 '{}'", 
                      dataConvertException.getRowIndex() + 1, 
                      dataConvertException.getColumnIndex() + 1,
                      dataConvertException.getCellData());
        }
        // 选择是否继续抛出异常
         super.onException(exception, context);
    }
} 