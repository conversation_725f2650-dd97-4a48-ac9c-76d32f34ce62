package cn.ysatnaf.domain.auth.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.ysatnaf.domain.auth.adatper.UserAdapter;
import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.domain.auth.res.AuthWebLoginRes;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.auth.req.AuthWebLoginReq;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.domain.auth.service.WebAuthService;
import cn.ysatnaf.domain.captcha.service.CaptchaService;
import cn.ysatnaf.types.common.ErrorCodeConstants;
import cn.ysatnaf.types.constants.CacheConstants;
import cn.ysatnaf.types.util.ServiceExceptionUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class WebAuthServiceImpl implements WebAuthService {

    private final CaptchaService captchaService;

    private final UserService userService;

    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public AuthWebLoginRes login(AuthWebLoginReq req) {
        // 校验验证码
//        captchaService.validate(req.getUsername(), req.getCaptcha(), req.getCaptchaId());

        // 查询用户信息
        UserEntity userEntity = userService.findByUsername(req.getUsername());
        if (userEntity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }
        // 校验密码是否一致
        if (!userEntity.getPassword().equals(DigestUtil.md5Hex(req.getPassword()))) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.AUTH_LOGIN_BAD_CREDENTIALS);
        }

        String token = IdUtil.fastSimpleUUID();
        // 保存用户信息到 redis
        LoginUserEntity loginUserEntity = UserAdapter.INSTANCE.entity2loginUser(userEntity);
        stringRedisTemplate.opsForValue().set(String.format(CacheConstants.LOGIN_USER, token), JSON.toJSONString(loginUserEntity), 1, TimeUnit.DAYS);

        // 返回用户信息
        AuthWebLoginRes authWebLoginRes = new AuthWebLoginRes();
        authWebLoginRes.setToken(token);
        authWebLoginRes.setUserId(userEntity.getId());
        authWebLoginRes.setUsername(userEntity.getUsername());
        authWebLoginRes.setNickname(userEntity.getNickname());
        authWebLoginRes.setAvatar(userEntity.getAvatar());
        authWebLoginRes.setRoleId(userEntity.getRoleId());
        authWebLoginRes.setGender(userEntity.getGender());
        return authWebLoginRes;
    }
}
