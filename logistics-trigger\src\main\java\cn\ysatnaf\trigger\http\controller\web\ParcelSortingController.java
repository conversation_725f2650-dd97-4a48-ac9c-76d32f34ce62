package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingBoxPO;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingPackagePO;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingRecordPO;
import cn.ysatnaf.domain.parcelsorting.model.req.*;
import cn.ysatnaf.domain.parcelsorting.model.res.ParcelSortingRecordDetailRes;
import cn.ysatnaf.domain.parcelsorting.service.ParcelSortingService;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageParam;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.dto.parcelsorting.ParcelSortingCheckReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Tag(name = "装箱模块")
@RequestMapping("/web/parcelSorting")
@Slf4j
@RestController
@RequiredArgsConstructor
public class ParcelSortingController {

    private final ParcelSortingService parcelSortingService;

    @PostMapping("/createRecord")
    @Operation(summary = "创建记录")
    public CommonResult<Boolean> createRecord(@RequestBody @Validated ParcelSoringCreateReq req) {
        parcelSortingService.createRecord(req.getRecordName(), req.getMasterBillId());
        return CommonResult.success(true);
    }

    @PostMapping("/deleteRecord")
    @Operation(summary = "删除记录")
    public CommonResult<Boolean> deleteRecord(@RequestBody @Validated ParcelSoringDeleteReq req) {
        parcelSortingService.deleteRecord(req.getId());
        return CommonResult.success(true);
    }

    @PostMapping("/updateRecord")
    @Operation(summary = "修改记录")
    public CommonResult<Boolean> updateRecord(@RequestBody @Validated ParcelSoringUpdateReq req) {
        parcelSortingService.updateRecord(req);
        return CommonResult.success(true);
    }

    @PostMapping("/listRecord")
    @Operation(summary = "记录列表")
    public CommonResult<PageResult<ParcelSortingRecordPO>> listRecord(@RequestBody PageParam pageParam) {
        return CommonResult.success(parcelSortingService.listRecord(pageParam));
    }

    @PostMapping("/createBox")
    @Operation(summary = "创建箱子")
    public CommonResult<Boolean> createBox(@RequestBody @Validated ParcelSortingBoxCreateReq req) {
        parcelSortingService.createBox(req);
        return CommonResult.success(true);
    }

    @PostMapping("/updateBox")
    @Operation(summary = "修改箱子")
    public CommonResult<Boolean> updateBox(@RequestBody @Validated ParcelSortingBoxUpdateReq req) {
        parcelSortingService.updateBox(req);
        return CommonResult.success(true);
    }

    @PostMapping("/deleteBox")
    @Operation(summary = "删除箱子")
    public CommonResult<Boolean> deleteBox(@RequestBody @Validated ParcelSortingBoxDeleteReq req) {
        parcelSortingService.deleteBox(req.getId());
        return CommonResult.success(true);
    }

    @PostMapping("/listBox")
    @Operation(summary = "箱子列表")
    public CommonResult<List<ParcelSortingBoxPO>> listBox(@RequestBody @Validated ParcelSortingBoxListReq req) {
        return CommonResult.success(parcelSortingService.listBox(req.getRecordId()));
    }

    @PostMapping("/boxing")
    @Operation(summary = "装箱")
    public CommonResult<Integer> boxing(@RequestBody @Validated ParcelSortingBoxingReq req) {
        return CommonResult.success(parcelSortingService.boxing(req.getBoxId(), req.getExpressNumber(), req.getMasterBillId()));
    }

    @PostMapping("/check")
    @Operation(summary = "装箱检测")
    public CommonResult<Long> check(@RequestBody @Validated ParcelSortingCheckReq req) {
        Long manifestId = parcelSortingService.check(req.getOrderNo(), req.getMasterBillId());
        return CommonResult.success(manifestId);
    }

    @PostMapping("/unboxing")
    @Operation(summary = "拆箱")
    public CommonResult<Integer> unboxing(@RequestBody @Validated ParcelSortingUnboxingReq req) {
        return CommonResult.success(parcelSortingService.unboxing(req.getBoxId(), req.getExpressNumber()));
    }

    @PostMapping("/changeBox")
    @Operation(summary = "换箱")
    public CommonResult<Boolean> changeBox(@RequestBody @Validated ParcelSortingChangeBoxReq req) {
        return CommonResult.success(parcelSortingService.changeBox(req.getIds(), req.getBoxId()));
    }

    @PostMapping("/listPackage")
    @Operation(summary = "包裹列表")
    public CommonResult<List<ParcelSortingPackagePO>> listPackage(@RequestBody @Validated ParcelSortingPackageListReq req) {
        return CommonResult.success(parcelSortingService.listPackage(req.getBoxId()));
    }

    @PostMapping("/exportCNCustomsDoc")
    @Operation(summary = "导出中国海关文件")
    public void exportCNCustomsDoc(@RequestBody @Validated ParcelSortingBoxListReq req, HttpServletResponse httpServletResponse) {
        parcelSortingService.exportCNCustomsDoc(req.getRecordId(), httpServletResponse);
    }

    @GetMapping("/recordDetail/{recordId}")
    @Operation(summary = "获取装箱记录详情及分箱列表")
    public CommonResult<ParcelSortingRecordDetailRes> getRecordDetail(@PathVariable Long recordId) {
        ParcelSortingRecordDetailRes detail = parcelSortingService.getRecordDetail(recordId);
        return CommonResult.success(detail);
    }

    @PostMapping("/batchBoxing")
    @Operation(summary = "批量装箱")
    public CommonResult<Boolean> batchBoxing(@RequestBody @Validated ParcelSortingBatchBoxingReq req) {
        parcelSortingService.batchBoxing(req);
        return CommonResult.success(true);
    }

    @PostMapping("/removePackage/{packageId}")
    @Operation(summary = "移除包裹")
    public CommonResult<Boolean> removePackage(@PathVariable Long packageId) {
        parcelSortingService.removePackage(packageId);
        return CommonResult.success(true);
    }

    @GetMapping("/{recordId}/unboxedPackages")
    @Operation(summary = "根据装箱记录ID查询未装箱包裹列表")
    public CommonResult<List<Manifest>> listUnboxedPackages(@PathVariable Long recordId) {
        List<Manifest> unboxedManifests = parcelSortingService.listUnboxedManifestsByRecordId(recordId);
        return CommonResult.success(unboxedManifests);
    }
}
