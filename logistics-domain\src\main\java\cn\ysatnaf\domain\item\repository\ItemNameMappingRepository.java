package cn.ysatnaf.domain.item.repository;

import cn.ysatnaf.domain.item.model.dto.ItemNameMappingPageReq;
import cn.ysatnaf.domain.item.model.entity.ItemNameMapping;
import cn.ysatnaf.types.common.PageResult; // 使用确认的路径

import java.util.List;
import java.util.Optional;

/**
 * 物品名称映射仓库接口
 */
public interface ItemNameMappingRepository {

    /**
     * 新增映射
     * @param mapping 实体
     * @return ID
     */
    Long insert(ItemNameMapping mapping);

    /**
     * 根据ID更新映射
     * @param mapping 实体
     * @return 是否成功
     */
    boolean updateById(ItemNameMapping mapping);

    /**
     * 根据ID删除映射
     * @param id ID
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询映射
     * @param id ID
     * @return 实体Optional
     */
    Optional<ItemNameMapping> findById(Long id);

    /**
     * 根据原始名称查询启用的映射 (精确匹配)
     * @param originalName 原始物品名称
     * @return 实体Optional
     */
    Optional<ItemNameMapping> findActiveByOriginalName(String originalName);

    /**
     * 根据原始名称查询映射 (包含禁用的)
     * @param originalName 原始物品名称
     * @return 实体Optional
     */
    Optional<ItemNameMapping> findByOriginalName(String originalName);

    /**
     * 分页查询映射
     * @param req 查询请求参数
     * @return 分页结果
     */
    PageResult<ItemNameMapping> page(ItemNameMappingPageReq req);

    /**
     * 增加指定映射的使用次数并更新最后使用时间
     * @param id 映射ID
     * @return 是否成功
     */
    boolean incrementUsageCount(Long id);

} 