package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.task.model.po.TaskPO;
import cn.ysatnaf.domain.task.repository.TaskRepository;
import cn.ysatnaf.infrastructure.persistent.dao.TaskDao;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
@RequiredArgsConstructor
public class TaskRepositoryImpl implements TaskRepository {

    private final TaskDao taskDao;

    @Override
    public Long insert(TaskPO taskPO) {
        taskDao.insert(taskPO);
        return taskPO.getId();
    }

    @Override
    public TaskPO getById(Long taskId) {
        return taskDao.selectById(taskId);
    }

    @Override
    public void updateById(TaskPO taskPO) {
        taskDao.updateById(taskPO);
    }

    @Override
    public PageResult<TaskPO> list(Long userId, String taskType, String taskStatus, Date createTimeStart, Date createTimeEnd, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<TaskPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(userId != null, TaskPO::getUserId, userId);
        wrapper.eq(StrUtil.isNotBlank(taskType), TaskPO::getTaskType, taskType);
        wrapper.eq(StrUtil.isNotBlank(taskStatus), TaskPO::getStatus, taskStatus);
        wrapper.ge(createTimeStart != null, TaskPO::getCreateTime, createTimeStart);
        wrapper.le(createTimeEnd != null, TaskPO::getCreateTime, createTimeEnd);
        wrapper.orderByDesc(TaskPO::getCreateTime);
        Page<TaskPO> taskPOPage = taskDao.selectPage(new Page<>(pageNo, pageSize), wrapper);
        return new PageResult<>(taskPOPage.getRecords(), taskPOPage.getTotal());
    }
}
