package cn.ysatnaf.domain.manifest.service.impl;

import cn.ysatnaf.domain.manifest.model.entity.DivisionCode;
import cn.ysatnaf.domain.manifest.mapper.DivisionCodeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DivisionCodeService {
    @Autowired
    private DivisionCodeMapper mapper;

    public String getDivisionCode(String prefix) {
        DivisionCode entity = mapper.selectById(prefix);
        return entity != null ? entity.getDivisionCode() : null;
    }
}