package cn.ysatnaf.types.constants;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
public class CacheConstants {
    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";
    public static final String LOGIN_USER = "login_user:%s";
    public static final String SYSTEM_INFO = "system_info";
    public static final String EXPORT_APPLICATION_FORM_LOCK = "export_application_form:lock:";
    public static final String EXPORT_APPLICATION_FORM_PROGRESS_KEY = "export_application_form:progress:";
    public static final String MANIFEST_CHANGE_SHIPPING_LOCK = "manifest_change_shipping_lock:";

    public static String getExportApplicationFormLock(List<Long> ids, Long userId) {
        return EXPORT_APPLICATION_FORM_LOCK + userId + ":" + Integer.toHexString(ids.hashCode());
    }

    public static String getExportApplicationProgressKey(List<Long> ids, Long userId) {
        return EXPORT_APPLICATION_FORM_PROGRESS_KEY + userId + ":" + Integer.toHexString(ids.hashCode());
    }

    public static String getExportApplicationFormLock(Date pickUpTimeFrom, Date pickUpTimeTo, List<Integer> shippingFeeTemplateTypes, Long userId) {
        return EXPORT_APPLICATION_FORM_LOCK + userId + ":" + pickUpTimeFrom + pickUpTimeTo + shippingFeeTemplateTypes;
    }
}
