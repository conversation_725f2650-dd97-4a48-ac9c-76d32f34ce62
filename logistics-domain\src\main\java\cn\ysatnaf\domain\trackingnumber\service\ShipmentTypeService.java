package cn.ysatnaf.domain.trackingnumber.service;

import cn.ysatnaf.domain.trackingnumber.model.entity.ShipmentType;

import java.util.List;

/**
 * 货物类型基础数据服务接口
 * <AUTHOR>
 */
public interface ShipmentTypeService {

    /**
     * 获取所有启用的货物类型
     * @return 货物类型列表
     */
    List<ShipmentType> listActiveShipmentTypes();

    /**
     * 根据ID获取货物类型
     * @param id 货物类型ID
     * @return 货物类型，不存在则抛出异常或返回null
     */
    ShipmentType getShipmentTypeById(Long id);
    
    /**
     * 根据Code获取货物类型
     * @param code 货物类型Code
     * @return 货物类型，不存在则抛出异常或返回null
     */
    ShipmentType getShipmentTypeByCode(String code);
    
    // 可能还需要添加、修改、删除等管理功能

} 