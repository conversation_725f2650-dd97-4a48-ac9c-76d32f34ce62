package cn.ysatnaf.domain.manifest.service.prereport.handledata;

import cn.hutool.extra.spring.SpringUtil;
import cn.ysatnaf.domain.address.service.ReceiverAreaService;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import cn.ysatnaf.domain.manifest.model.valobj.SourceType;
import cn.ysatnaf.domain.manifest.service.ExpressNumberService;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportContext;
import cn.ysatnaf.domain.manifest.service.prereport.validate.ValidationResult;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 网络用户预上报数据转换器
 */
public class NetworkUserPreReportDataConverter extends BasePreReportDataConverter {

    /**
     * 国际运单号
     */
    private final List<String> internationalExpressNumbers;

    /**
     * 国际运单号下标，用于记录分配到了哪一个
     */
    private Integer internationalExpressNumberIndex;

    public NetworkUserPreReportDataConverter(ManifestPreReportContext context, ReceiverAreaService receiverAreaService) {
        super.context = context;
        super.receiverAreaService = receiverAreaService;
        internationalExpressNumberIndex = 0;
        // 准备足够的国际订单号
        // 1. 判断有多少个非重复的国内运单号
        Boolean overwrite = context.getOverwrite();
        ValidationResult validationResult = context.getValidationResult();
        List<ManifestPreReportRow> rows = validationResult.getNotPreReportedList();
        if (overwrite) {
            rows.addAll(validationResult.getPreReportedList());
        }
        // 2. 获取国际订单号
        Set<String> expressNubmerSet = rows.stream().map(ManifestPreReportRow::getExpressNumber).collect(Collectors.toSet());
        ExpressNumberService expressNumberService = SpringUtil.getBean(ExpressNumberService.class);
        internationalExpressNumbers = expressNumberService.getInternationalNumberBatch(expressNubmerSet.size());
    }

    @Override
    protected void setSourceType(Manifest manifest) {
        manifest.setSourceType(SourceType.NETWORK_USER.getCode());
    }

    @Override
    protected void setInternationalExpressNumber(Manifest manifest, String expressNumber) {
        String internationalExpressNumber = internationalExpressNumbers.get(internationalExpressNumberIndex);
        internationalExpressNumberIndex += 1;
        manifest.setSawagaNumber(internationalExpressNumber);
    }
}
