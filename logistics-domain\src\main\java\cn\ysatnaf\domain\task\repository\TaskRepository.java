package cn.ysatnaf.domain.task.repository;

import cn.ysatnaf.domain.task.model.po.TaskPO;
import cn.ysatnaf.types.common.PageResult;

import java.util.Date;

public interface TaskRepository {

    Long insert(TaskPO taskPO);

    TaskPO getById(Long taskId);

    void updateById(TaskPO taskPO);

    PageResult<TaskPO> list(Long id, String taskType, String taskStatus, Date createTimeStart, Date createTimeEnd, Integer pageNo, Integer pageSize);
}
