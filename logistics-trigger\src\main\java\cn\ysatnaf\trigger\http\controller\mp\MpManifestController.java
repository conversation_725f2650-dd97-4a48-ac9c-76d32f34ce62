package cn.ysatnaf.trigger.http.controller.mp;

import cn.ysatnaf.domain.manifest.model.entity.ManifestSearchDTO;
import cn.ysatnaf.domain.manifest.model.req.ManifestSearchReq;
import cn.ysatnaf.domain.manifest.service.ManifestService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;

/**
 * ManaFestController
 *
 * <AUTHOR>
 * @date 2023/12/22 15:14
 */
@Tag(name = "运单相关")
@RequestMapping("/mp/manifest")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class MpManifestController {

    private final ManifestService manifestService;

    @PostMapping("/search")
    @PermitAll
    @Operation(summary = "查询运单")
    public CommonResult<PageResult<ManifestSearchDTO>> update(@RequestBody @Validated ManifestSearchReq req) {
        return CommonResult.success(manifestService.searchManifest(req));
    }
}
