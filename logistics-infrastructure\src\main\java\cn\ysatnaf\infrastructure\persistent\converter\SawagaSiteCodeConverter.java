package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.manifest.model.entity.SawagaSiteCodeEntity;
import cn.ysatnaf.infrastructure.persistent.po.SawagaSiteCodePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SawagaSiteCodeConverter {

    SawagaSiteCodeConverter INSTANCE = Mappers.getMapper(SawagaSiteCodeConverter.class);

    SawagaSiteCodeEntity toEntity(SawagaSiteCodePO sawagaSiteCodePO);

    List<SawagaSiteCodeEntity> toEntityList(List<SawagaSiteCodePO> selectList);
}
