package cn.ysatnaf.domain.excel.serviice.generator;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.ysatnaf.domain.excel.serviice.generator.converter.OsakaCustomsDocumentDataExcelConverter;
import cn.ysatnaf.domain.excel.serviice.generator.converter.OsakaCustomsDocumentItemExcelConverter;
import cn.ysatnaf.domain.excel.serviice.generator.row.OsakaCustomsDocumentExcelRow;
import cn.ysatnaf.domain.excel.serviice.generator.row.OsakaCustomsDocumentItemExcelRow;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class OsakaCustomsDocumentGenerator implements JapanCustomsDocumentGenerator {

    private static final String TEMPLATE_PATH = "templates/OsakaCustomsDocumentExportTemplate.xlsx";

    private final OsakaCustomsDocumentDataExcelConverter osakaCustomsDocumentDataExcelConverter;

    private final OsakaCustomsDocumentItemExcelConverter osakaCustomsDocumentItemExcelConverter;


    @Override
    public void generate(DocumentDataContext documentDataContext) {
        BatchInfo batchInfo = documentDataContext.getBatchInfo();
        List<Manifest> manifests = documentDataContext.getManifests();
        List<OsakaCustomsDocumentExcelRow> rows = osakaCustomsDocumentDataExcelConverter.convert(documentDataContext);
        List<OsakaCustomsDocumentItemExcelRow> itemRows = osakaCustomsDocumentItemExcelConverter.convert(manifests);
        HttpServletResponse response = documentDataContext.getHttpServletResponse();
        // 模板注意 用{} 来表示你要用的变量 如果本来就有"{","}" 特殊字符 用"{","}"代替
        // {} 代表普通变量 {.} 代表是list的变量
        // 填充数据

        // 处理数据 (此处是业务需要，可以自行更改)
        //获取模板 (我是将模板放到 resource文件下面了)
//        ClassPathResource classPathResource = new ClassPathResource("/data/templates/ManifestExportTemplate.xlsx");
        try (InputStream inputStream = ResourceUtil.getStream(TEMPLATE_PATH)) {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "Manifest-" + batchInfo.getLadingBill();
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncodeUtil.encode(fileName) + ".xlsx");

            ExcelWriter writer = EasyExcel.write(response.getOutputStream())
                    .withTemplate(inputStream)
                    .build();
            // 生成sheet
            WriteSheet sheet1 = EasyExcel.writerSheet(0)
                    .head(OsakaCustomsDocumentExcelRow.class)
                    .build();
            writer.fill(rows, sheet1);

            WriteSheet sheet2 = EasyExcel.writerSheet(1)
                    .head(OsakaCustomsDocumentItemExcelRow.class)
                    .build();
            writer.fill(itemRows, sheet2);

            writer.finish();
            writer.close();
        } catch (IOException e) {
            log.error("导出失败：", e);
            throw new ServiceException("导出失败, 请联系管理员");
        }
    }
}
