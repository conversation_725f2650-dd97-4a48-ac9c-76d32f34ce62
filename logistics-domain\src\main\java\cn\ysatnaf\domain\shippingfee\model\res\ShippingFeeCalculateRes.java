package cn.ysatnaf.domain.shippingfee.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "运费计算返回参数")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ShippingFeeCalculateRes {

    @Schema(description = "费用")
    private BigDecimal cost;

    @Schema(description = "体积重量")
    private BigDecimal dimensionalWeight;
}
