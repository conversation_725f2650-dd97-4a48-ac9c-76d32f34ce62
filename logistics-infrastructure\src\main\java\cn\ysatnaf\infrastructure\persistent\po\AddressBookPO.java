package cn.ysatnaf.infrastructure.persistent.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * AddressBookPO
 *
 * <AUTHOR>
 * @date 2023/12/22 9:29
 */
@Data
@TableName("tb_address_book")
public class AddressBookPO extends BasePO {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户OPENID
     */
    private String openid;

    /**
     * 名字
     */
    private String name;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省编码
     */
    private Long provinceCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 市编码
     */
    private Long cityCode;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private Long districtCode;

    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 街道编码
     */
    private Long streetCode;

    /**
     * 街道名称
     */
    private String streetName;

    /**
     * 居委/社区编码
     */
    private Long committeeCode;

    /**
     * 居委/社区名称
     */
    private String committeeName;

    /**
     * 详细地址
     */
    private String detail;

    /**
     * 标签
     */
    private String label;

    /**
     * 是否默认地址
     */
    private Boolean isDefault;

}
