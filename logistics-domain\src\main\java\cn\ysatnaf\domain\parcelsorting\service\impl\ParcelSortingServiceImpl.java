package cn.ysatnaf.domain.parcelsorting.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.excel.serviice.impl.ChineseCustomsDocumentGenerationV3ServiceImpl;
import cn.ysatnaf.domain.manifest.model.aggregate.ManifestAggregate;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.repository.ManifestRepository;
import cn.ysatnaf.domain.manifest.service.ManifestService;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingPackagePO;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingBoxPO;
import cn.ysatnaf.domain.parcelsorting.model.entity.ParcelSortingRecord;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingRecordPO;
import cn.ysatnaf.domain.parcelsorting.model.req.ParcelSoringUpdateReq;
import cn.ysatnaf.domain.parcelsorting.model.req.ParcelSortingBatchBoxingReq;
import cn.ysatnaf.domain.parcelsorting.model.req.ParcelSortingBoxCreateReq;
import cn.ysatnaf.domain.parcelsorting.model.req.ParcelSortingBoxUpdateReq;
import cn.ysatnaf.domain.parcelsorting.model.res.ParcelSortingRecordDetailRes;
import cn.ysatnaf.domain.parcelsorting.repository.ParcelSortingPackageRepository;
import cn.ysatnaf.domain.parcelsorting.repository.ParcelSortingDetailRepository;
import cn.ysatnaf.domain.parcelsorting.repository.ParcelSortingRecordRepository;
import cn.ysatnaf.domain.parcelsorting.service.ParcelSortingService;
import cn.ysatnaf.types.common.PageParam;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Arrays;

/**
 * <AUTHOR> Hang
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ParcelSortingServiceImpl implements ParcelSortingService {

    private final ParcelSortingRecordRepository parcelSortingRecordRepository;

    private final ParcelSortingDetailRepository parcelSortingDetailRepository;

    private final ParcelSortingPackageRepository parcelSortingPackageRepository;

    private final ManifestService manifestService;

    private final ChineseCustomsDocumentGenerationV3ServiceImpl generateService;

    private final ManifestRepository manifestRepository;

    @Override
    @Transactional
    public void createRecord(String recordName, Long masterBillId) {
        if (parcelSortingRecordRepository.countByRecordName(recordName) > 0) {
            throw new ServiceException("已存在相同名称的记录");
        }

        ParcelSortingRecord record = ParcelSortingRecord.builder()
                .recordName(recordName)
                .masterBillId(masterBillId)
                .isDelete(false)
                .build();

        parcelSortingRecordRepository.save(record);

        log.info("Created parcel sorting record '{}' for master bill {}", recordName, masterBillId);
    }

    @Override
    public void deleteRecord(Long id) {
        parcelSortingRecordRepository.delete(id);
    }

    @Override
    public void updateRecord(ParcelSoringUpdateReq req) {
        ParcelSortingRecordPO record = parcelSortingRecordRepository.getByRecordName(req.getRecordName());
        if (record != null && !Objects.equals(record.getId(), req.getId())) {
            throw new ServiceException("已存在相同名称的记录");
        }
        parcelSortingRecordRepository.update(req.getId(), req.getRecordName(), req.getMasterBillId());
    }

    @Override
    public PageResult<ParcelSortingRecordPO> listRecord(PageParam pageParam) {
        return parcelSortingRecordRepository.list(pageParam.getPageNo(), pageParam.getPageSize());
    }

    @Override
    public void createBox(ParcelSortingBoxCreateReq req) {
        List<ParcelSortingBoxPO> detailPOList = parcelSortingDetailRepository.listByRecordId(req.getRecordId());
        if (CollUtil.isNotEmpty(detailPOList)) {
            for (ParcelSortingBoxPO parcelSortingBoxPO : detailPOList) {
                if (parcelSortingBoxPO.getBoxName().equals(req.getBoxName())) {
                    throw new ServiceException("已存在相同的箱号");
                }
                if (parcelSortingBoxPO.getPackageNumber().equals(req.getPackageNumber())) {
                    throw new ServiceException("已存在相同的袋号");
                }
            }
        }
        ParcelSortingBoxPO parcelSortingBoxPO = new ParcelSortingBoxPO();
        parcelSortingBoxPO.setRecordId(req.getRecordId());
        parcelSortingBoxPO.setBoxName(req.getBoxName());
        parcelSortingBoxPO.setPackageNumber(req.getPackageNumber());
        parcelSortingDetailRepository.create(parcelSortingBoxPO);
    }

    @Override
    public void updateBox(ParcelSortingBoxUpdateReq req) {
        ParcelSortingBoxPO detail = parcelSortingDetailRepository.getById(req.getId());
        if (detail == null) {
            throw new ServiceException("参数错误，箱子不存在");
        }
        List<ParcelSortingBoxPO> detailPOList = parcelSortingDetailRepository.listByRecordId(detail.getRecordId());
        if (CollUtil.isNotEmpty(detailPOList)) {
            for (ParcelSortingBoxPO parcelSortingBoxPO : detailPOList) {
                if (parcelSortingBoxPO.getId().equals(req.getId())) {
                    continue;
                }
                if (parcelSortingBoxPO.getBoxName().equals(req.getBoxName())) {
                    throw new ServiceException("已存在相同的箱号");
                }
                if (parcelSortingBoxPO.getPackageNumber().equals(req.getPackageNumber())) {
                    throw new ServiceException("已存在相同的袋号");
                }
            }
        }
        detail.setBoxName(req.getBoxName());
        detail.setPackageNumber(req.getPackageNumber());
        parcelSortingDetailRepository.updateById(detail);
    }

    @Override
    @Transactional
    public void deleteBox(Long id) {
        parcelSortingDetailRepository.delete(id);
        parcelSortingPackageRepository.deleteByBoxId(id);
    }

    @Override
    public List<ParcelSortingBoxPO> listBox(Long recordId) {
        return parcelSortingDetailRepository.listByRecordId(recordId);
    }

    @Override
    public Integer boxing(Long boxId, String orderNo, Long masterBillId) {
        if (orderNo.startsWith("A") && orderNo.endsWith("A")) {
            orderNo = orderNo.replace("A", "");
        }
        Manifest manifest = manifestService.getManifestByOrderNoOrExpressNumber(orderNo);
        if (manifest == null) {
            throw new ServiceException("该物流订单不存在，请检查物流单号是否正确");
        }
        if (!manifest.ifPickedUp()) {
            throw new ServiceException("该订单未揽件，无法装箱");
        }
        if (manifest.getMasterBillId() != null && !manifest.getMasterBillId().equals(masterBillId)) {
            throw new ServiceException("该物流订单的提单号与运单号不一致，请检查提单号是否正确");
        }
        ParcelSortingPackagePO parcelSortingPackagePO = parcelSortingPackageRepository.getByExpressNumber(manifest.getExpressNumber());
        if (parcelSortingPackagePO != null) {
            ParcelSortingBoxPO parcelSortingBoxPO = parcelSortingDetailRepository.getById(parcelSortingPackagePO.getBoxId());
            throw new ServiceException("该物流订单已装箱，装箱于箱号：" + parcelSortingBoxPO.getBoxName());
        }
        parcelSortingPackagePO = new ParcelSortingPackagePO();
        parcelSortingPackagePO.setBoxId(boxId);
        parcelSortingPackagePO.setManifestId(manifest.getId());
        parcelSortingPackagePO.setExpressNumber(manifest.getTrackingNumber());
        parcelSortingPackagePO.setOrderNo(manifest.getOrderNo());
        parcelSortingPackagePO.setReceiverName(manifest.getReceiverName());

        parcelSortingPackageRepository.create(parcelSortingPackagePO);
        return parcelSortingDetailRepository.countById(boxId);
    }

    @Override
    public Integer unboxing(Long boxId, String expressNumber) {
        ParcelSortingPackagePO parcelSortingPackagePO = parcelSortingPackageRepository.getByBoxIdAndExpressNumber(boxId, expressNumber);
        if (parcelSortingPackagePO == null) {
            throw new ServiceException("该包裹不在指定的箱子内");
        }
        parcelSortingPackageRepository.deleteById(parcelSortingPackagePO.getId());
        return parcelSortingDetailRepository.countById(boxId);
    }

    @Override
    public Boolean changeBox(List<Long> ids, Long boxId) {
        parcelSortingPackageRepository.updateByIds(ids, boxId);
        return true;
    }

    @Override
    public List<ParcelSortingPackagePO> listPackage(Long boxId) {
        return parcelSortingPackageRepository.listByBoxId(boxId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportCNCustomsDoc(Long recordId, HttpServletResponse httpServletResponse) {
        ParcelSortingRecordPO record = parcelSortingRecordRepository.getById(recordId);
        if (record == null) {
            throw new ServiceException("参数错误，记录不存在");
        }
        List<ParcelSortingBoxPO> boxes = parcelSortingDetailRepository.listByRecordId(recordId);
        if (CollUtil.isEmpty(boxes)) {
            throw new ServiceException("该记录下暂无分箱内容，无法导出文件");
        }
        List<Long> boxIds = new ArrayList<>(boxes.size());
        Map<Long, ParcelSortingBoxPO> boxMap = new HashMap<>(boxes.size());
        for (ParcelSortingBoxPO box : boxes) {
            boxIds.add(box.getId());
            boxMap.put(box.getId(), box);
        }
        List<ParcelSortingPackagePO> packages = parcelSortingPackageRepository.listByBoxIds(boxIds);
        if (CollUtil.isEmpty(packages)) {
            throw new ServiceException("包裹数据为空，无法导出文件");
        }
        List<String> expressNumbers = new ArrayList<>(packages.size());
        Map<String, ParcelSortingPackagePO> expressNumberPackageMap = new HashMap<>(packages.size());
        for (ParcelSortingPackagePO aPackage : packages) {
            expressNumbers.add(aPackage.getExpressNumber());
            expressNumberPackageMap.put(aPackage.getExpressNumber(), aPackage);
        }
        List<ManifestAggregate> manifestAggregates = manifestService.getManifestAggregateByExpressNumbers(expressNumbers);
        if (CollUtil.isEmpty(manifestAggregates)) {
            throw new ServiceException("物流单数据为空，无法导出文件");
        }
        List<Manifest> manifestList = new ArrayList<>();
        for (ManifestAggregate manifestAggregate : manifestAggregates) {
            Manifest manifest = manifestAggregate.getManifest();
            ParcelSortingPackagePO parcelSortingPackagePO = expressNumberPackageMap.get(manifest.getTrackingNumber());
            ParcelSortingBoxPO parcelSortingBoxPO = boxMap.get(parcelSortingPackagePO.getBoxId());
            manifest.setPackageNumber(parcelSortingBoxPO.getPackageNumber());
        }
        manifestService.ship(manifestList);
        generateService.generate(manifestAggregates, record.getRecordName(), httpServletResponse);
    }

    @Override
    public List<ParcelSortingPackagePO> listPackageByExpressNumbers(Collection<String> expressNumbers) {
        return parcelSortingPackageRepository.listByExpressNumbers(expressNumbers);
    }

    @Override
    public List<ParcelSortingBoxPO> listBoxByBoxIds(Set<Long> boxIds) {
        return parcelSortingDetailRepository.listByIds(boxIds);
    }

    @Override
    public Long check(String orderNo, Long masterBillId) {
        if (orderNo.startsWith("A") && orderNo.endsWith("A")) {
            orderNo = orderNo.replace("A", "");
        }
        Manifest manifest = manifestService.getManifestByOrderNoOrExpressNumber(orderNo);
        if (manifest == null) {
            throw new ServiceException("该物流订单不存在，请检查物流单号是否正确");
        }
        if (!manifest.ifPickedUp()) {
            throw new ServiceException("该订单未揽件，无法装箱");
        }
        if (manifest.getMasterBillId() != null && !manifest.getMasterBillId().equals(masterBillId)) {
            throw new ServiceException("该物流订单的提单号与主提单不一致，请检查提单号是否正确");
        }
        ParcelSortingPackagePO parcelSortingPackagePO = parcelSortingPackageRepository.getByManifestId(manifest.getId());
        if (parcelSortingPackagePO != null) {
            ParcelSortingBoxPO parcelSortingBoxPO = parcelSortingDetailRepository.getById(parcelSortingPackagePO.getBoxId());
            throw new ServiceException("该物流订单已装箱，装箱于箱号：" + (parcelSortingBoxPO != null ? parcelSortingBoxPO.getBoxName() : "未知箱号"));
        }

        return manifest.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchBoxing(ParcelSortingBatchBoxingReq req) {
        Long recordId = req.getParcelSortingRecordId();
        Long boxId = req.getBoxId();
        // 从 req 中获取 manifestScanItems 列表
        List<ParcelSortingBatchBoxingReq.ManifestScanItem> manifestScanItems = req.getManifestScanItems();

        ParcelSortingRecordPO record = parcelSortingRecordRepository.getById(recordId);
        if (record == null) {
            throw new ServiceException("分箱记录不存在: ID " + recordId);
        }
        ParcelSortingBoxPO box = parcelSortingDetailRepository.getById(boxId);
        if (box == null) {
            throw new ServiceException("箱子不存在: ID " + boxId);
        }
        if (!box.getRecordId().equals(recordId)) {
            throw new ServiceException("箱子 " + box.getBoxName() + " 不属于记录 " + record.getRecordName());
        }

        // 校验 manifestScanItems 是否为空
        if (CollUtil.isEmpty(manifestScanItems)) {
            throw new ServiceException("运单列表不能为空");
        }

        // 提取 manifestIds 以便后续查询
        List<Long> manifestIds = manifestScanItems.stream()
                                                  .map(ParcelSortingBatchBoxingReq.ManifestScanItem::getManifestId)
                                                  .collect(Collectors.toList());

        List<Manifest> manifests = manifestRepository.getByIds(manifestIds);
        if (manifests.size() != manifestIds.size()) {
            Set<Long> foundIds = manifests.stream().map(Manifest::getId).collect(Collectors.toSet());
            List<Long> notFoundIds = manifestIds.stream().filter(id -> !foundIds.contains(id)).collect(Collectors.toList());
            throw new ServiceException("部分运单不存在: IDs " + notFoundIds);
        }

        List<ParcelSortingPackagePO> existingPackages = parcelSortingPackageRepository.listByManifestIds(manifestIds);
        if (CollUtil.isNotEmpty(existingPackages)) {
            Map<Long, ParcelSortingPackagePO> existingMap = existingPackages.stream()
                    .collect(Collectors.toMap(ParcelSortingPackagePO::getManifestId, Function.identity()));
            List<String> alreadyBoxedErrors = new ArrayList<>();
            for (Long manifestId : manifestIds) {
                if (existingMap.containsKey(manifestId)) {
                    ParcelSortingBoxPO existingBox = parcelSortingDetailRepository.getById(existingMap.get(manifestId).getBoxId());
                    alreadyBoxedErrors.add("运单ID " + manifestId + " 已在箱号 " + (existingBox != null ? existingBox.getBoxName() : "未知箱号") + " 中");
                }
            }
            if (!alreadyBoxedErrors.isEmpty()) {
                throw new ServiceException("部分运单已装箱: " + String.join("; ", alreadyBoxedErrors));
            }
        }

        Long masterBillId = record.getMasterBillId();
        List<String> validationErrors = new ArrayList<>();
        List<ParcelSortingPackagePO> packagesToCreate = new ArrayList<>(manifests.size());

        // 创建一个 Map 用于快速查找 manifestId 对应的 scanTime (如果需要使用 scanTime)
         Map<Long, java.time.LocalDateTime> scanTimeMap = manifestScanItems.stream()
            .collect(Collectors.toMap(ParcelSortingBatchBoxingReq.ManifestScanItem::getManifestId, ParcelSortingBatchBoxingReq.ManifestScanItem::getScanTime));

        for (Manifest manifest : manifests) {
            if (!manifest.ifPickedUp()) {
                validationErrors.add("运单 " + manifest.getTrackingNumber() + " 未揽件");
            }
            if (manifest.getMasterBillId() != null && !manifest.getMasterBillId().equals(masterBillId)) {
                validationErrors.add("运单 " + manifest.getTrackingNumber() + " 的提单号与记录的主提单不匹配");
            }
            ParcelSortingPackagePO newPackage = new ParcelSortingPackagePO();
            newPackage.setBoxId(boxId);
            newPackage.setManifestId(manifest.getId());
            newPackage.setOrderNo(manifest.getOrderNo());
            newPackage.setExpressNumber(manifest.getTrackingNumber());
            newPackage.setReceiverName(manifest.getReceiverName());
            // 如果 ParcelSortingPackagePO 中添加了 scanTime 字段，并需要存储扫描时间，则取消下面的注释
             java.time.LocalDateTime scanTime = scanTimeMap.get(manifest.getId());
             if (scanTime != null) { // 防御性编程，确保 scanTime 存在
                 newPackage.setCreateTime(scanTime); // 假设 ParcelSortingPackagePO 有 setScanTime 方法和相应的字段
             }
            packagesToCreate.add(newPackage);
        }

        if (!validationErrors.isEmpty()) {
            throw new ServiceException(String.join("; ", validationErrors));
        }

        int updatedCount = manifestRepository.batchUpdatePackingStatusAndRecordId(manifestIds, 1, recordId);
        if (updatedCount != manifestIds.size()) {
             log.warn("批量更新 Manifest 状态数量不匹配，预期: {}, 实际: {}", manifestIds.size(), updatedCount);
        }

        parcelSortingPackageRepository.batchCreate(packagesToCreate);

        // 更新日志信息，记录整个 manifestScanItems 列表，其中包含ID和时间
        log.info("批量装箱成功: Record ID {}, Box ID {}, ManifestScanItems {}", recordId, boxId, manifestScanItems);
    }

    @Override
    public ParcelSortingRecordDetailRes getRecordDetail(Long recordId) {
        // 1. 获取装箱记录信息
        ParcelSortingRecordPO record = parcelSortingRecordRepository.getById(recordId);
        if (record == null || record.getIsDelete()) { // 检查是否为 null 或已删除
            throw new ServiceException("装箱记录不存在: ID " + recordId);
        }

        // 2. 获取该记录下的分箱列表
        List<ParcelSortingBoxPO> boxes = parcelSortingDetailRepository.listByRecordId(recordId);

        // 3. 实时计算预期总件数 (根据指定模板类型)
        List<Integer> expectedTemplateTypes = Arrays.asList(1, 3, 4, 5); // 普货(1,4) + 投函(3,5)
        Long totalItemsExpectedResult = manifestRepository.countByMasterBillIdAndTemplateTypes(record.getMasterBillId(), expectedTemplateTypes);
        Integer totalItemsExpected = (totalItemsExpectedResult != null) ? totalItemsExpectedResult.intValue() : 0;

        // 4. 实时计算已装箱件数 (通过查询与这些箱子关联的包裹数量)
        Integer itemsPackedCount = 0;
        if (CollUtil.isNotEmpty(boxes)) {
            List<Long> boxIds = boxes.stream().map(ParcelSortingBoxPO::getId).collect(Collectors.toList());
            itemsPackedCount = parcelSortingPackageRepository.countByBoxIds(boxIds); // 需要在 Repository 添加此方法
        }

        // 5. 构建并返回 DTO
        return ParcelSortingRecordDetailRes.builder()
                .id(record.getId())
                .recordName(record.getRecordName())
                .masterBillId(record.getMasterBillId())
                .createTime(record.getCreateTime()) // 假设 BasePO 有 getCreateTime
                .totalItemsExpected(totalItemsExpected)
                .itemsPackedCount(itemsPackedCount)
                .boxes(boxes)
                .build();
    }

    @Override
    @Transactional
    public void removePackage(Long packageId) {
        ParcelSortingPackagePO existingPackage = parcelSortingPackageRepository.getById(packageId);
        if (existingPackage == null) {
            throw new ServiceException("该包裹不存在");
        }
        manifestRepository.updatePackingStatusAndRecordId(existingPackage.getManifestId(), 0, null);
        parcelSortingPackageRepository.deleteById(packageId);
    }

    @Override
    public List<Manifest> listUnboxedManifestsByRecordId(Long recordId) {
        // 1. 根据 recordId 查询记录，获取 masterBillId
        ParcelSortingRecordPO record = parcelSortingRecordRepository.getById(recordId);
        if (record == null || record.getMasterBillId() == null) {
            log.warn("找不到装箱记录或记录未关联主提单，recordId: {}", recordId);
            return Collections.emptyList(); // 返回空列表
        }
        Long masterBillId = record.getMasterBillId();

        // 2. 根据 masterBillId 查询所有相关运单聚合信息
        List<ManifestAggregate> manifestsInMasterBill = manifestService.listByMasterBillId(masterBillId);
        if (CollUtil.isEmpty(manifestsInMasterBill)) {
            log.info("主提单下没有找到运单，masterBillId: {}", masterBillId);
            return Collections.emptyList();
        }

        // 3. 查询该 recordId 下所有已装箱的包裹
        List<ParcelSortingPackagePO> boxedPackages = parcelSortingPackageRepository.listByRecordId(recordId);
        // 提取已装箱包裹的 ExpressNumber
        Set<Long> manifestIds = boxedPackages.stream()
                                            .map(ParcelSortingPackagePO::getManifestId)
                                            .filter(Objects::nonNull)
                                            .collect(Collectors.toSet());

        // 4. 过滤出未装箱且符合模板类型的 Manifest 对象
        // --- 定义允许的模板类型 --- START
        final List<Integer> allowedTemplateTypes = Arrays.asList(1, 3, 4, 5);
        // --- 定义允许的模板类型 --- END

        // 5. 返回未装箱且符合条件的 Manifest 列表
        return manifestsInMasterBill.stream()
                .map(ManifestAggregate::getManifest) // 获取 Manifest 对象
                .filter(Objects::nonNull) // 过滤 null Manifest
                // --- 添加模板类型过滤条件 --- START
                .filter(manifest -> manifest.getShippingFeeTemplateType() != null &&
                                   allowedTemplateTypes.contains(manifest.getShippingFeeTemplateType()))
                // --- 添加模板类型过滤条件 --- END
                .filter(manifest -> !manifestIds.contains(manifest.getId())) // 过滤掉已装箱的
                .collect(Collectors.toList());
    }
}
