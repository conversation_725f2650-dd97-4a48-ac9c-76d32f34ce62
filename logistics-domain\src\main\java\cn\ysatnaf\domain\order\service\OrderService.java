package cn.ysatnaf.domain.order.service;

import cn.ysatnaf.domain.order.model.req.*;
import cn.ysatnaf.domain.order.model.res.MpOrderPageRes;
import cn.ysatnaf.domain.order.model.res.OrderDetailRes;
import cn.ysatnaf.domain.order.model.res.OrderSearchRes;
import cn.ysatnaf.types.common.PageResult;

/**
 * <AUTHOR> Hang
 */
public interface OrderService {

    String create(OrderCreateReq req);

    PageResult<OrderSearchRes> search(MpOrderSearchReq req);

    Boolean pickUp(OrderPickUpReq req);

    OrderDetailRes getById(Long id);

    void assignOrder(Long orderId, Long courierId);

    Boolean cancel(OrderCancelReq req);

    PageResult<MpOrderPageRes> pageMpOrder(MpOrderPageReq req);

    Integer countMpOrder(Integer status);
}
