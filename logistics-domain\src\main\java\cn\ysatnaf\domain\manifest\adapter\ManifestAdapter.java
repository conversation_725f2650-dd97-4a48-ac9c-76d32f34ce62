package cn.ysatnaf.domain.manifest.adapter;

import cn.ysatnaf.domain.manifest.model.dto.ManifestOrderDTO;
import cn.ysatnaf.domain.manifest.model.dto.ManifestUpdateByIdDTO;
import cn.ysatnaf.domain.manifest.model.dto.WayBillImageGenDTO;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestSearchDTO;
import cn.ysatnaf.domain.excel.serviice.generator.row.TokyoCustomsDocumentExcelRow;
import cn.ysatnaf.domain.manifest.model.req.ManifestPickupReq;
import cn.ysatnaf.domain.manifest.model.req.ManifestUpdateByIdReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Mapper
public interface ManifestAdapter {
    ManifestAdapter INSTANCE = Mappers.getMapper(ManifestAdapter.class);

    TokyoCustomsDocumentExcelRow po2excel(ManifestSearchDTO manifestEntity);

    List<TokyoCustomsDocumentExcelRow> po2excelList(List<ManifestSearchDTO> manifestEntities);

    ManifestOrderDTO entity2OrderDTO(Manifest manifest);

    List<ManifestOrderDTO> entity2OrderDTOList(List<Manifest> manifest);

    WayBillImageGenDTO entity2WayBillImageGenDTO(Manifest manifest);

    ManifestUpdateByIdDTO pickUpReq2UpdateByIdDTO(ManifestPickupReq req);

    ManifestUpdateByIdDTO updateByIdReq2UpdateByIdDTO(ManifestUpdateByIdReq req);
}
