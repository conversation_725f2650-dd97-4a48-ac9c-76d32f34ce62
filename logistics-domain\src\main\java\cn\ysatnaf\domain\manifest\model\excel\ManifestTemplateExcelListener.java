package cn.ysatnaf.domain.manifest.model.excel;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class ManifestTemplateExcelListener implements ReadListener<ManifestPreReportRow> {

    private List<ManifestPreReportRow> parsedData = new ArrayList<>();

    @Override
    public void invoke(ManifestPreReportRow manifestPreReportRow, AnalysisContext analysisContext) {
        if (StrUtil.isBlank(manifestPreReportRow.getExpressNumber())) {
            return;
        }
        // 收集数据
        parsedData.add(manifestPreReportRow);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
