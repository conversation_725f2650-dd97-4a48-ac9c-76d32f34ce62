package cn.ysatnaf.domain.manifest.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
@Data
public class ApplicationFormExcelDatum {

    @ExcelProperty("主单号")
    private String ladingBill;

    @ExcelProperty("分单号")
    private String wayBillNumber;

    @ExcelProperty("总包号")
    private String masterLadingBill;

    @ExcelProperty("货物名称")
    private String goodsDescription;

    @ExcelProperty("包裹总毛重")
    private BigDecimal totalGrossWeight;

    @ExcelProperty("单项商品净重")
    private BigDecimal perNetWeight;

    @ExcelProperty("件数")
    private Integer quantity;

    @ExcelProperty("单价")
    private BigDecimal unitPrice;
}
