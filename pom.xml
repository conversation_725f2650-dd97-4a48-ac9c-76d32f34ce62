<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.ysatnaf</groupId>
    <artifactId>logistics</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>logistics-app</module>
        <module>logistics-domain</module>
        <module>logistics-trigger</module>
        <module>logistics-infrastructure</module>
        <module>logistics-types</module>
    </modules>

    <properties>
        <revision>2.0.0-jdk8-snapshot</revision>
        <!-- Maven 相关 -->
        <java.version>1.8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <!-- 看看咋放到 bom 里 -->
        <lombok.version>1.18.30</lombok.version>
        <spring.boot.version>2.7.17</spring.boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <revision>2.0.0-jdk8-snapshot</revision>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <!-- 统一依赖管理 -->
        <spring.boot.version>2.7.17</spring.boot.version>
        <!-- Web 相关 -->
        <springdoc.version>1.6.15</springdoc.version>
        <knife4j.version>4.3.0</knife4j.version>
        <servlet.versoin>2.5</servlet.versoin>
        <!-- DB 相关 -->
        <druid.version>1.2.20</druid.version>
        <mybatis-plus.version>3.5.4</mybatis-plus.version>
        <mybatis-plus-generator.version>3.5.4</mybatis-plus-generator.version>
        <dynamic-datasource.version>3.6.1</dynamic-datasource.version>
        <mybatis-plus-join.version>1.4.7</mybatis-plus-join.version>
        <redisson.version>3.18.0</redisson.version>
        <dm8.jdbc.version>8.1.3.62</dm8.jdbc.version>
        <!-- 消息队列 -->
        <rocketmq-spring.version>2.2.3</rocketmq-spring.version>
        <!-- 服务保障相关 -->
        <lock4j.version>2.2.5</lock4j.version>
        <resilience4j.version>1.7.1</resilience4j.version>
        <!-- 监控相关 -->
        <skywalking.version>8.12.0</skywalking.version>
        <spring-boot-admin.version>2.7.11</spring-boot-admin.version>
        <opentracing.version>0.33.0</opentracing.version>
        <!-- Test 测试相关 -->
        <podam.version>7.2.11.RELEASE</podam.version>
        <jedis-mock.version>1.0.7</jedis-mock.version>
        <mockito-inline.version>4.11.0</mockito-inline.version>
        <!-- Bpm 工作流相关 -->
        <flowable.version>6.8.0</flowable.version>
        <!-- 工具类相关 -->
        <captcha-plus.version>1.0.10</captcha-plus.version>
        <jsoup.version>1.16.2</jsoup.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool.version>5.8.22</hutool.version>
        <easyexcel.verion>3.1.0</easyexcel.verion>
        <velocity.version>2.3</velocity.version>
        <screw.version>1.0.5</screw.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>32.1.3-jre</guava.version>
        <guice.version>5.1.0</guice.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <commons-net.version>3.10.0</commons-net.version>
        <jsch.version>0.1.55</jsch.version>
        <tika-core.version>2.7.0</tika-core.version>
        <ip2region.version>2.7.0</ip2region.version>
        <!-- 三方云服务相关 -->
        <okio.version>3.5.0</okio.version>
        <okhttp3.version>4.11.0</okhttp3.version>
        <commons-io.version>2.11.0</commons-io.version>
        <minio.version>8.5.6</minio.version>
        <aliyun-java-sdk-core.version>4.6.4</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-dysmsapi.version>2.2.1</aliyun-java-sdk-dysmsapi.version>
        <tencentcloud-sdk-java.version>3.1.880</tencentcloud-sdk-java.version>
        <justauth.version>1.0.8</justauth.version>
        <jimureport.version>1.6.1</jimureport.version>
        <xercesImpl.version>2.12.2</xercesImpl.version>
        <weixin-java.version>4.5.7.B</weixin-java.version>
        <ureport2.version>2.2.9</ureport2.version>
    </properties>

    <developers>
        <developer>
            <name>Ysatnaf</name>
            <email><EMAIL></email>
            <organization>ysatnaf</organization>
            <organizationUrl>https://github.com/ysatnaf</organizationUrl>
        </developer>
    </developers>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0</url>
        </license>
    </licenses>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.12</version>
    </parent>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
            </dependency>
            <!-- # 多数据源路由配置
                 # mysql 5.x driver-class-name: com.mysql.jdbc.Driver    mysql-connector-java 5.1.34
                 # mysql 8.x driver-class-name: com.mysql.cj.jdbc.Driver mysql-connector-java 8.0.22-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.22</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>2.0.28</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.9</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.1.3-jre</version>
            </dependency>
            <dependency>
                <groupId>dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>1.6.1</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.10</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>0.9.1</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>4.4.0</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.15</version>
            </dependency>

            <!-- 工程模块 -->
            <dependency>
                <groupId>cn.ysatnaf</groupId>
                <artifactId>logistics-domain</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cn.ysatnaf</groupId>
                <artifactId>logistics-infrastructure</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cn.ysatnaf</groupId>
                <artifactId>logistics-types</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>cn.ysatnaf</groupId>
                <artifactId>logistics-trigger</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- maven-surefire-plugin 插件，用于运行单元测试。 -->
                <!-- 注意，需要使用 3.0.X+，因为要支持 Junit 5 版本 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <!-- maven-compiler-plugin 插件，解决 spring-boot-configuration-processor + Lombok + MapStruct 组合 -->
                <!-- https://stackoverflow.com/questions/33483697/re-run-spring-boot-configuration-annotation-processor-to-update-generated-metada -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring.boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.1.0</version>
                    <configuration>
                        <encoding>utf-8</encoding>
                        <useDefaultDelimiters>true</useDefaultDelimiters>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                            <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                            <nonFilteredFileExtension>zip</nonFilteredFileExtension>
                            <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                            <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                            <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                            <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
<!--                <plugin>-->
<!--                    <artifactId>maven-resources-plugin</artifactId>-->
<!--                    <configuration>-->
<!--                        <encoding>utf-8</encoding>-->
<!--                        <useDefaultDelimiters>true</useDefaultDelimiters>-->
<!--                        <nonFilteredFileExtensions>-->
<!--                            <nonFilteredFileExtension>ttf</nonFilteredFileExtension>-->
<!--                            <nonFilteredFileExtension>woff</nonFilteredFileExtension>-->
<!--                            <nonFilteredFileExtension>woff2</nonFilteredFileExtension>-->
<!--                        </nonFilteredFileExtensions>-->
<!--                    </configuration>-->
<!--                </plugin>-->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <!-- 统一 revision 版本 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                    </execution>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!-- 使用 huawei / aliyun 的 Maven 源，提升下载速度 -->
    <repositories>
        <repository>
            <id>huaweicloud</id>
            <name>huawei</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
    </repositories>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <java_jvm>-Xms1G -Xmx1G -server  -XX:MaxPermSize=256M -Xss256K -Dspring.profiles.active=test -XX:+DisableExplicitGC -XX:+UseG1GC  -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/export/Logs/logistics-boot -Xloggc:/export/Logs/logistics-boot/gc-logistics-boot.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps</java_jvm>
                <profileActive>dev</profileActive>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <java_jvm>-Xms1G -Xmx1G -server  -XX:MaxPermSize=256M -Xss256K -Dspring.profiles.active=test -XX:+DisableExplicitGC -XX:+UseG1GC  -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/export/Logs/logistics-boot -Xloggc:/export/Logs/logistics-boot/gc-logistics-boot.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps</java_jvm>
                <profileActive>test</profileActive>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <java_jvm>-Xms6G -Xmx6G -server  -XX:MaxPermSize=256M -Xss256K -Dspring.profiles.active=release -XX:+DisableExplicitGC -XX:+UseG1GC  -XX:LargePageSizeInBytes=128m -XX:+UseFastAccessorMethods -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/export/Logs/fq-mall-activity-app -Xloggc:/export/Logs/logistics-boot/gc-logistics-boot.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps</java_jvm>
                <profileActive>prod</profileActive>
            </properties>
        </profile>
    </profiles>

</project>
