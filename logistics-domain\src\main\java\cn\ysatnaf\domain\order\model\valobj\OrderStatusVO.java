package cn.ysatnaf.domain.order.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderStatusVO {

    /**
     * 1-待取件
     * 2-待支付
     * 3-待揽件
     * 4-待发货
     * 5-运送中
     * 6-已签收
     * -1-已取消
     */
    PENDING_PICK_UP(1, "待取件"),
    PENDING_PAYMENT(2, "待支付"),
    WAIT_DELIVER(3, "待揽件"),
    WAIT_DELIVERED(4, "待发货"),
    DELIVERING(5, "运送中"),
    DELIVERED(6, "已签收"),
    CANCEL(-1, "已取消");

    private final Integer code;
    private final String message;

    public static OrderStatusVO getByCode(Integer code) {
        for (OrderStatusVO orderStatusVO : OrderStatusVO.values()) {
            if (orderStatusVO.getCode().equals(code)) {
                return orderStatusVO;
            }
        }
        return null;
    }
}
