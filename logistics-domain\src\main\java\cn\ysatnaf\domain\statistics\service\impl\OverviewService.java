package cn.ysatnaf.domain.statistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.manifest.repository.ManifestRepository;
import cn.ysatnaf.domain.statistics.calculator.DateTimeUtil;
import cn.ysatnaf.domain.statistics.calculator.TimeRangeCalculator;
import cn.ysatnaf.domain.statistics.model.dto.*;
import cn.ysatnaf.domain.statistics.model.req.CompanyRankingReq;
import cn.ysatnaf.domain.statistics.model.req.MonthlyQuantityReq;
import cn.ysatnaf.domain.statistics.model.vo.*;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OverviewService {

    private final ManifestRepository manifestRepository;

    private final StringRedisTemplate redisTemplate;

    @Qualifier("queryExecutor")
    private final ExecutorService executorService;

    public SummaryVO getSummary(Integer startHour) {
        // 1. 获取总数据
        SummaryVO.TotalData totalData = manifestRepository.selectTotalSummary();

        // 2. 获取本月数据
        LocalDateTime[] currentMonthRange = TimeRangeCalculator.calculateTimeRangeByMonthOffset(startHour, 0);
        SummaryVO.CurrentMonthData currentMonthData = manifestRepository.selectPeriodSummary(
                currentMonthRange[0], currentMonthRange[1]);

        // 3. 获取上月同期数据
        LocalDateTime[] lastMonthRange = TimeRangeCalculator.calculateTimeRangeByMonthOffset(startHour, -1);
        SummaryVO.CurrentMonthData lastMonthData = manifestRepository.selectPeriodSummary(
                lastMonthRange[0], lastMonthRange[1]);

        // 4. 计算增长率
        calculateGrowthRate(currentMonthData, lastMonthData);

        return new SummaryVO()
                .setTotal(totalData)
                .setCurrentMonth(currentMonthData);
    }

    public TodayVO getTodaySummary(Integer startHour) {
        // 获取今日数据
        LocalDateTime[] todayRange = TimeRangeCalculator.calculateTodayRange(startHour);
        TodayVO todayData = manifestRepository.selectTodaySummary(
                todayRange[0],
                todayRange[1]
        );

        // 获取昨日数据
        LocalDateTime[] yesterdayRange = TimeRangeCalculator.calculateYesterdayRange(startHour);
        TodayVO yesterdayData = manifestRepository.selectTodaySummary(
                yesterdayRange[0],
                yesterdayRange[1]
        );

        // 计算增长率
        calculateTodayGrowthRate(todayData, yesterdayData);

        return todayData;
    }

    public List<TemplateDailyVO> getDailyTemplateStats(Integer startHour) {
        // 时间范围计算
        LocalDateTime[] todayRange = TimeRangeCalculator.calculateTodayRange(startHour);
        LocalDateTime[] yesterdayRange = TimeRangeCalculator.calculateYesterdayRange(startHour);

        // 直接调用Mapper获取数据
        Map<Integer, TemplateStatsDTO> todayStats = manifestRepository.selectTemplateStats(todayRange[0], todayRange[1])
                .stream()
                .collect(Collectors.toMap(TemplateStatsDTO::getTemplateType, Function.identity()));

        Map<Integer, TemplateStatsDTO> yesterdayStats = manifestRepository.selectTemplateStats(yesterdayRange[0], yesterdayRange[1])
                .stream()
                .collect(Collectors.toMap(TemplateStatsDTO::getTemplateType, Function.identity()));

        // 构建结果
        return buildResult(todayStats, yesterdayStats);
    }


    public List<TemplateProportionVO> getTemplateProportion() {
        // 1. 计算统计时间范围

        // 2. 查询数据库原始数据
        List<TemplateStatsDTO> rawStats = manifestRepository.selectTemplateStats(null, null);

        // 3. 合并模板类型数据
        Map<String, TemplateStatsDTO> mergedStats = mergeTemplateStats(rawStats);

        // 4. 计算总数量金额
        long totalQty = mergedStats.values().stream()
                .mapToLong(TemplateStatsDTO::getQuantity).sum();
        BigDecimal totalAmount = mergedStats.values().stream()
                .map(TemplateStatsDTO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 5. 计算占比并构建结果
        return mergedStats.entrySet().stream()
                .map(entry -> buildVO(entry.getKey(), entry.getValue(), totalQty, totalAmount))
                .collect(Collectors.toList());
    }

    private Map<String, TemplateStatsDTO> mergeTemplateStats(List<TemplateStatsDTO> rawStats) {
        Map<String, TemplateStatsDTO> merged = new HashMap<>();

        rawStats.forEach(dto -> {
            String type = resolveTemplateType(dto.getTemplateType());
            merged.compute(type, (k, v) -> {
                if (v == null) {
                    return new TemplateStatsDTO()
                            .setQuantity(dto.getQuantity())
                            .setAmount(dto.getAmount());
                }
                return v.add(dto.getQuantity(), dto.getAmount());
            });
        });

        return merged;
    }

    private String resolveTemplateType(Integer templateType) {
        switch (templateType) {
            case 1:
            case 4:
                return "normal";
            case 2:
                return "battery";
            case 3:
            case 5:
                return "letter";
            default:
                return "unknown";
        }
    }

    private TemplateProportionVO buildVO(String type, TemplateStatsDTO stats,
                                         long totalQty, BigDecimal totalAmount) {
        BigDecimal qtyProp = calculateProportion(stats.getQuantity(), totalQty);
        BigDecimal amtProp = calculateProportion(stats.getAmount(), totalAmount);

        return new TemplateProportionVO()
                .setTemplateType(type)
                .setQuantity(stats.getQuantity())
                .setAmount(stats.getAmount())
                .setQuantityProportion(qtyProp)
                .setAmountProportion(amtProp);
    }

    private BigDecimal calculateProportion(Number part, Number total) {
        if (total.doubleValue() == 0) return BigDecimal.ZERO;
        return new BigDecimal(part.toString())
                .divide(new BigDecimal(total.toString()), 4, RoundingMode.HALF_UP);
    }

    private List<TemplateDailyVO> buildResult(Map<Integer, TemplateStatsDTO> today,
                                              Map<Integer, TemplateStatsDTO> yesterday) {
        List<TemplateDailyVO> result = new ArrayList<>();

        // 处理普通模板（类型1和4）
        result.add(buildVO("normal", Arrays.asList(1, 4), today, yesterday));

        // 处理带电模板（类型2）
        result.add(buildVO("battery", Collections.singletonList(2), today, yesterday));

        // 处理投函模板（类型3和5）
        result.add(buildVO("letter", Arrays.asList(3, 5), today, yesterday));

        return result;
    }

    private TemplateDailyVO buildVO(String typeName, List<Integer> templateTypes,
                                    Map<Integer, TemplateStatsDTO> today,
                                    Map<Integer, TemplateStatsDTO> yesterday) {
        // 聚合今日数据
        TemplateStatsDTO todayAggregated = aggregateStats(templateTypes, today);

        // 聚合昨日数据
        TemplateStatsDTO yesterdayAggregated = aggregateStats(templateTypes, yesterday);

        // 计算增长率
        BigDecimal qRate = calculateRate(todayAggregated.getQuantity(), yesterdayAggregated.getQuantity());
        BigDecimal aRate = calculateRate(todayAggregated.getAmount(), yesterdayAggregated.getAmount());

        return new TemplateDailyVO()
                .setTemplateType(typeName)
                .setQuantity(todayAggregated.getQuantity())
                .setAmount(todayAggregated.getAmount())
                .setQuantityRate(qRate)
                .setAmountRate(aRate);
    }

    private TemplateStatsDTO aggregateStats(List<Integer> types, Map<Integer, TemplateStatsDTO> source) {
        long totalQty = 0L;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (Integer type : types) {
            TemplateStatsDTO dto = source.get(type);
            if (dto != null) {
                totalQty += dto.getQuantity() != null ? dto.getQuantity() : 0L;
                totalAmount = totalAmount.add(dto.getAmount() != null ? dto.getAmount() : BigDecimal.ZERO);
            }
        }

        return new TemplateStatsDTO()
                .setQuantity(totalQty)
                .setAmount(totalAmount);
    }

    private BigDecimal calculateRate(Number today, Number yesterday) {
        if (yesterday == null || yesterday.doubleValue() == 0) return null;

        double todayVal = today != null ? today.doubleValue() : 0;
        double yesterdayVal = yesterday.doubleValue();

        return BigDecimal.valueOf((todayVal - yesterdayVal) / yesterdayVal * 100)
                .setScale(2, RoundingMode.HALF_UP);
    }

    private void calculateTodayGrowthRate(TodayVO today, TodayVO yesterday) {
        // 数量增长率计算
        if (yesterday.getQuantity() != null && yesterday.getQuantity() != 0L) {
            BigDecimal qRate = BigDecimal.valueOf(today.getQuantity() - yesterday.getQuantity())
                    .divide(BigDecimal.valueOf(yesterday.getQuantity()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            today.setQuantityRate(qRate);
        }

        // 金额增长率计算
        if (yesterday.getAmount() != null &&
                yesterday.getAmount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal aRate = today.getAmount().subtract(yesterday.getAmount())
                    .divide(yesterday.getAmount(), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            today.setAmountRate(aRate);
        }
    }

    private void calculateGrowthRate(SummaryVO.CurrentMonthData current,
                                     SummaryVO.CurrentMonthData last) {
        // 数量增长率
        if (last.getQuantity() != 0) {
            BigDecimal quantityRate = BigDecimal.valueOf(current.getQuantity() - last.getQuantity())
                    .divide(BigDecimal.valueOf(last.getQuantity()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            current.setQuantityRate(quantityRate);
        }

        // 金额增长率
        if (last.getAmount().compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal amountRate = current.getAmount().subtract(last.getAmount())
                    .divide(last.getAmount(), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            current.setAmountRate(amountRate);
        }
    }

    public TrendVO getLast30DaysTrend(Integer startHour) {
        // 1. 生成日期范围
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(29);
        LocalDateTime datetime = endDate.minusDays(29);
        List<LocalDate> dateRange = new ArrayList<>();
        for (int i = 0; i < 30; i++) {
            dateRange.add(datetime.toLocalDate());
            datetime = datetime.plusDays(1);
        }

        // 2. 查询数据库
        List<TrendDTO> dbData = manifestRepository.selectTrendData(startHour, startDate, endDate);

        // 3. 构建完整数据集（补全空日期）
        Map<LocalDate, TrendDTO> dataMap = dbData.stream()
                .collect(Collectors.toMap(
                        TrendDTO::getDate,
                        Function.identity()
                ));

        TrendVO result = new TrendVO();
        result.setDates(new ArrayList<>());
        result.setQuantities(new ArrayList<>());
        result.setAmounts(new ArrayList<>());

        dateRange.forEach(date -> {
            String dateStr = date.toString();
            result.getDates().add(dateStr);

            if (dataMap.containsKey(date)) {
                result.getQuantities().add(dataMap.get(date).getQuantity());
                result.getAmounts().add(dataMap.get(date).getAmount());
            } else {
                result.getQuantities().add(0L);
                result.getAmounts().add(BigDecimal.ZERO);
            }
        });

        return result;
    }

    public List<CompanyRankingDTO> getCompanyRanking(CompanyRankingReq req) {
        if (req.getStartDate() != null && req.getEndDate() != null) {
            LocalDateTime[] timeRange = TimeRangeCalculator.calculateTimeRange(req.getStartHour(), req.getStartDate(), req.getEndDate());
            return manifestRepository.selectCompanyRanking(timeRange[0], timeRange[1], req.getLimit());
        }
        return manifestRepository.selectCompanyRanking(null, null, req.getLimit());
    }

    private static final String AMOUNT_DISTRIBUTION_KEY = "amount_distribution";

    public List<AmountDistributionDTO> getAmountDistribution() {
        // 从redis中查询
        String distributionJson = redisTemplate.opsForValue().get(AMOUNT_DISTRIBUTION_KEY);
        if (StrUtil.isNotBlank(distributionJson)) {
            return JSON.parseArray(distributionJson, AmountDistributionDTO.class);
        }
        List<AmountDistributionDTO> distributionDTOS = manifestRepository.selectAmountDistribution();
        distributionDTOS = distributionDTOS.stream()
                .sorted(Comparator.comparing(AmountDistributionDTO::getRange).reversed())
                .collect(Collectors.toList());
        // 缓存到redis中
        redisTemplate.opsForValue().set(AMOUNT_DISTRIBUTION_KEY, JSON.toJSONString(distributionDTOS), 1, TimeUnit.DAYS);
        return distributionDTOS;
    }

    public MonthlyQuantityDTO getMonthlyQuantity(MonthlyQuantityReq req) {
        // 时间范围计算
        final LocalDateTime start = req.getStartTime();
        final LocalDateTime end = req.getEndTime();
        final Integer startHour = req.getStartHour();

        try {
            // 并行执行所有独立查询
            CompletableFuture<Map<String, Object>> baseStatsFuture = asyncQuery(
                    () -> manifestRepository.selectBaseStats(start, end));

            CompletableFuture<Map<String, Object>> peakDayFuture = asyncQuery(
                    () -> manifestRepository.selectPeakDay(start, end, startHour));

            CompletableFuture<List<TemplateCategoryDTO>> templateDistFuture = asyncQuery(
                    () -> manifestRepository.selectTemplateDistribution(start, end));

            CompletableFuture<List<Map<String, Object>>> regionDistFuture = asyncQuery(
                    () -> manifestRepository.selectRegionalDistribution(start, end));

            CompletableFuture<List<MonthTrendDTO>> trendFuture = asyncQuery(
                    () -> manifestRepository.selectTwelveMonthTrend(start.minusMonths(11), end, startHour));

            // 等待所有查询完成（带超时机制）
            CompletableFuture.allOf(baseStatsFuture, peakDayFuture, templateDistFuture,
                            regionDistFuture, trendFuture)
                    .get(5, TimeUnit.SECONDS); // 设置总超时时间

            // 获取各查询结果
            Map<String, Object> baseStats = baseStatsFuture.get();
            Map<String, Object> peakDayMap = peakDayFuture.get();
            List<TemplateCategoryDTO> templateDist = templateDistFuture.get();
            List<Map<String, Object>> regionData = regionDistFuture.get();
            List<MonthTrendDTO> trendData = trendFuture.get();

            // 处理依赖关系
            Integer currentTotal = getInt(baseStats, "total");
            Integer currentDailyAvg = getInt(baseStats, "dailyAvg");

            // 并行计算环比
            CompletableFuture<Double> momChangeFuture = asyncTask(
                    () -> calculateMomChange(req, currentTotal));

            CompletableFuture<Double> avgChangeFuture = asyncTask(
                    () -> calculateAvgChange(req, currentDailyAvg));

            // 等待计算结果
            Double momChange = momChangeFuture.get(5, TimeUnit.SECONDS);
            Double avgChange = avgChangeFuture.get(5, TimeUnit.SECONDS);

            // 构建响应
            return MonthlyQuantityDTO.builder()
                    .total(currentTotal)
                    .dailyAvg(currentDailyAvg)
                    .monthOnMonthChange(momChange)
                    .monthOnMonthAvgChange(avgChange)
                    .peakDay(buildCountPeakDay(peakDayMap))
                    .monthlyTrend(trendData)
                    .templateDistribution(new TemplateDistributionDTO(currentTotal, templateDist))
                    .regionalDistribution(processRegionData(regionData))
                    .build();

        } catch (TimeoutException e) {
            log.error("查询超时", e);
            throw new ServiceException("查询超时，请重试");
        } catch (ExecutionException e) {
            log.error("数据查询失败", e);
            throw new ServiceException("数据查询失败");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("查询被中断", e);
            throw new ServiceException("查询被中断");
        }
    }

    // 模板金额分布构建方法
    private TemplateAmountDistributionDTO buildTemplateDist(BigDecimal totalAmount,
                                                            List<TemplateAmountDTO> rawData) {
        // 防御空值处理
        if (CollUtil.isEmpty(rawData)) {
            return new TemplateAmountDistributionDTO(BigDecimal.ZERO, Collections.emptyList());
        }

        // 计算精确比例
        final BigDecimal finalTotal = totalAmount.compareTo(BigDecimal.ZERO) == 0 ?
                BigDecimal.ONE : totalAmount; // 防除零保护

        List<TemplateAmountDTO> categories = rawData.stream()
                .map(dto -> {
                    // 计算比例（保留两位小数）
                    BigDecimal ratio = dto.getAmount()
                            .divide(finalTotal, 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100))
                            .setScale(2, RoundingMode.HALF_UP);

                    return new TemplateAmountDTO(
                            dto.getType(),
                            dto.getAmount(),
                            ratio.doubleValue()
                    );
                })
                .sorted(Comparator.comparing(TemplateAmountDTO::getRatio).reversed())
                .collect(Collectors.toList());

        return new TemplateAmountDistributionDTO(totalAmount, categories);
    }

    // 区域金额分布构建方法
    private RegionalAmountDistributionDTO buildRegionalDist(List<Map<String, Object>> rawData) {
        if (CollUtil.isEmpty(rawData)) {
            return new RegionalAmountDistributionDTO(BigDecimal.ZERO, Collections.emptyList());
        }

        // 第一层分组：按区域
        Map<String, List<Map<String, Object>>> areaGroup = rawData.stream()
                .collect(Collectors.groupingBy(
                        item -> (String) item.get("area"),
                        LinkedHashMap::new, // 保持顺序
                        Collectors.toList()
                ));

        List<RegionAmountDTO> areas = new ArrayList<>();
        BigDecimal grandTotal = BigDecimal.ZERO;

        for (Map.Entry<String, List<Map<String, Object>>> entry : areaGroup.entrySet()) {
            String area = entry.getKey();
            List<Map<String, Object>> areaData = entry.getValue();

            // 区域总金额
            BigDecimal areaTotal = areaData.stream()
                    .map(item -> (BigDecimal) item.get("amount"))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            grandTotal = grandTotal.add(areaTotal);

            // 第二层处理：区域内模板类型
            List<TemplateAmountDTO> templates = areaData.stream()
                    .map(item -> {
                        String type = (String) item.get("type");
                        BigDecimal amount = (BigDecimal) item.get("amount");

                        // 计算区域内的类型占比
                        BigDecimal ratio = areaTotal.compareTo(BigDecimal.ZERO) == 0 ?
                                BigDecimal.ZERO :
                                amount.divide(areaTotal, 4, RoundingMode.HALF_UP)
                                        .multiply(BigDecimal.valueOf(100))
                                        .setScale(2, RoundingMode.HALF_UP);

                        return new TemplateAmountDTO(type, amount, ratio.doubleValue());
                    })
                    .sorted(Comparator.comparing(TemplateAmountDTO::getRatio).reversed())
                    .collect(Collectors.toList());

            areas.add(new RegionAmountDTO(area, areaTotal, templates));
        }

        return new RegionalAmountDistributionDTO(grandTotal, areas);
    }


    private Double calculateChange(BigDecimal current, BigDecimal last) {
        if (last.compareTo(BigDecimal.ZERO) == 0) return 0.0;
        return current.subtract(last)
                .divide(last, 4, RoundingMode.HALF_UP)
                .doubleValue() * 100;
    }

    // 通用异步查询封装
    private <T> CompletableFuture<T> asyncQuery(Supplier<T> query) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return query.get();
            } catch (Exception e) {
                throw new CompletionException("Async query failed", e);
            }
        }, executorService);
    }

    // 通用异步计算任务
    private <T> CompletableFuture<T> asyncTask(Supplier<T> task) {
        return CompletableFuture.supplyAsync(task, executorService);
    }

    private PeakDayDTO buildCountPeakDay(Map<String, Object> map) {
        if (map == null || map.isEmpty()) return null;
        return new PeakDayDTO(
                map.get("peakDate").toString(),
                (Long) map.get("quantity"));
    }

    private Integer getInt(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? ((Number) value).intValue() : 0;
    }

    // 环比计算（保持原有逻辑）
    private Double calculateMomChange(MonthlyQuantityReq req, Integer currentTotal) {
        LocalDateTime lastStart = req.getStartTime().minusMonths(1);
        LocalDateTime lastEnd = req.getEndTime().minusMonths(1);
        Long lastTotal = (Long) manifestRepository.selectBaseStats(lastStart, lastEnd).get("total");
        return calculateChange(currentTotal, new BigDecimal(lastTotal));
    }

    // 平均变化计算
    private Double calculateAvgChange(MonthlyQuantityReq req, Integer currentAvg) {
        LocalDateTime lastStart = req.getStartTime().minusMonths(1);
        LocalDateTime lastEnd = req.getEndTime().minusMonths(1);
        BigDecimal lastAvg = (BigDecimal) manifestRepository.selectBaseStats(lastStart, lastEnd).get("dailyAvg");
        return calculateChange(currentAvg, lastAvg);
    }

    private Double calculateChange(Integer current, BigDecimal last) {
        if (last.compareTo(BigDecimal.ZERO) == 0) return 0.0;
        return ((new BigDecimal(current).subtract(last)).multiply(new BigDecimal(100))).divide(last, 1, RoundingMode.HALF_UP).doubleValue();
    }

    public MonthlyAmountDTO getMonthlyAmount(MonthlyQuantityReq req) {
        final LocalDateTime start = req.getStartTime();
        final LocalDateTime end = req.getEndTime();
        Integer startHour = req.getStartHour();

        try {
            // 并行执行所有查询
            CompletableFuture<Map<String, Object>> baseStatsFuture = asyncQuery(
                    () -> manifestRepository.selectAmountStats(start, end));

            CompletableFuture<Map<String, Object>> peakDayFuture = asyncQuery(
                    () -> manifestRepository.selectPeakAmountDay(start, end, startHour));

            CompletableFuture<List<TemplateAmountDTO>> templateDistFuture = asyncQuery(
                    () -> manifestRepository.selectTemplateAmountDistribution(start, end));

            CompletableFuture<List<Map<String, Object>>> regionDistFuture = asyncQuery(
                    () -> manifestRepository.selectRegionalAmountDistribution(start, end));

            CompletableFuture<List<MonthAmountTrendDTO>> trendFuture = asyncQuery(
                    () -> manifestRepository.selectAmountTrend(start.minusMonths(11), end, startHour));

            // 等待结果
            CompletableFuture.allOf(baseStatsFuture, peakDayFuture,
                            templateDistFuture, regionDistFuture, trendFuture)
                    .get(3, TimeUnit.SECONDS);

            // 数据转换
            BigDecimal totalAmount = getBigDecimal(baseStatsFuture.get(), "total");
            BigDecimal dailyAvg = getBigDecimal(baseStatsFuture.get(), "dailyAvg");

            // 环比计算
            CompletableFuture<Double> totalChangeFuture = asyncTask(() ->
                    calculateTotalChange(req, totalAmount));
            CompletableFuture<Double> avgChangeFuture = asyncTask(() ->
                    calculateAvgChange(req, dailyAvg));

            return MonthlyAmountDTO.builder()
                    .totalAmount(totalAmount)
                    .dailyAvgAmount(dailyAvg)
                    .monthOnMonthChange(totalChangeFuture.get(1, TimeUnit.SECONDS))
                    .avgDailyChange(avgChangeFuture.get(1, TimeUnit.SECONDS))
                    .peakDay(buildPeakDay(peakDayFuture.get()))
                    .trends(trendFuture.get())
                    .templateDistribution(buildTemplateDist(
                            totalAmount, templateDistFuture.get()))
                    .regionalDistribution(buildRegionalDist(regionDistFuture.get()))
                    .build();

        } catch (TimeoutException e) {
            log.error("查询超时", e);
            throw new ServiceException("查询超时，请重试");
        } catch (ExecutionException e) {
            log.error("数据查询失败", e);
            throw new ServiceException("数据查询失败");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("查询被中断", e);
            throw new ServiceException("查询被中断");
        }
    }

    public TodayQuantityDetailDTO getTodayQuantityDetail(int startHour) {
        try {
            // 计算时间范围
            LocalDateTime todayStart = DateTimeUtil.getTodayStartWithHour(startHour);
            LocalDateTime todayEnd = DateTimeUtil.getTodayEndWithHour(startHour);
            LocalDateTime yesterdayStart = DateTimeUtil.getYesterdayStartWithHour(startHour);
            LocalDateTime yesterdayEnd = DateTimeUtil.getYesterdayEndWithHour(startHour);
            LocalDateTime beforeYesterdayStart = yesterdayStart.minusDays(1);
            LocalDateTime beforeYesterdayEnd = yesterdayEnd.minusDays(1);

            log.debug("时间范围: todayStart={}, todayEnd={}, startHour={}",
                    todayStart, todayEnd, startHour);

            TodayQuantityDetailDTO result = new TodayQuantityDetailDTO();

            // 并行执行查询
            CompletableFuture<Map<String, Object>> summaryFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTodayYesterdayQuantitySummary(
                            todayStart, todayEnd, yesterdayStart, yesterdayEnd, beforeYesterdayStart, beforeYesterdayEnd),
                    executorService
            );

            CompletableFuture<List<Map<String, Object>>> hourlyStatsFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTodayHourlyStats(todayStart, todayEnd, startHour),
                    executorService
            );

            CompletableFuture<List<Map<String, Object>>> todayTemplateFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTemplateQuantityDistribution(todayStart, todayEnd),
                    executorService
            );

            CompletableFuture<List<Map<String, Object>>> yesterdayTemplateFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTemplateQuantityDistribution(yesterdayStart, yesterdayEnd),
                    executorService
            );

            // 等待所有查询完成
            CompletableFuture.allOf(
                    summaryFuture,
                    hourlyStatsFuture,
                    todayTemplateFuture,
                    yesterdayTemplateFuture
            ).join();

            // 1. 处理总数和环比
            Map<String, Object> summary = summaryFuture.get();
            int todayCount = Integer.parseInt(summary.get("today_count").toString());
            int yesterdayCount = Integer.parseInt(summary.get("yesterday_count").toString());
            int beforeYesterdayCount = Integer.parseInt(summary.get("before_yesterday_count").toString());

            result.setTotalQuantity(todayCount);
            result.setYesterdayQuantity(yesterdayCount);

            // 计算环比变化
            if (yesterdayCount > 0) {
                BigDecimal change = new BigDecimal(todayCount - yesterdayCount)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal(yesterdayCount), 1, RoundingMode.HALF_UP);
                result.setCompareYesterday(change);
            } else {
                result.setCompareYesterday(BigDecimal.ZERO);
            }

            // 计算昨日与前日比较
            if (beforeYesterdayCount > 0) {
                BigDecimal yesterdayCompare = new BigDecimal(yesterdayCount - beforeYesterdayCount)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal(beforeYesterdayCount), 1, RoundingMode.HALF_UP);
                result.setYesterdayCompare(yesterdayCompare);
            } else {
                result.setYesterdayCompare(BigDecimal.ZERO);
            }

            // 2. 处理小时分布
            processHourDistribution(result, hourlyStatsFuture.get());

            // 3. 处理今日模板类型分布
            List<TodayQuantityDetailDTO.TemplateDistributionDTO> todayTemplateList =
                    processTemplateDistribution(todayTemplateFuture.get(), todayCount);
            result.setTemplateDistribution(todayTemplateList);

            // 4. 处理今日与昨日模板类型对比
            processTemplateCompare(result, todayTemplateFuture.get(), yesterdayTemplateFuture.get());

            return result;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("查询今日运单数量详情被中断", e);
            throw new RuntimeException("查询今日运单数量详情被中断", e);
        } catch (ExecutionException e) {
            log.error("查询今日运单数量详情失败", e);
            throw new RuntimeException("查询今日运单数量详情失败", e.getCause());
        }
    }

    public TodayAmountDetailDTO getTodayAmountDetail(int startHour) {
        try {
            // 计算时间范围
            LocalDateTime todayStart = DateTimeUtil.getTodayStartWithHour(startHour);
            LocalDateTime todayEnd = DateTimeUtil.getTodayEndWithHour(startHour);
            LocalDateTime yesterdayStart = DateTimeUtil.getYesterdayStartWithHour(startHour);
            LocalDateTime yesterdayEnd = DateTimeUtil.getYesterdayEndWithHour(startHour);
            LocalDateTime beforeYesterdayStart = yesterdayStart.minusDays(1);
            LocalDateTime beforeYesterdayEnd = yesterdayEnd.minusDays(1);

            log.debug("时间范围: todayStart={}, todayEnd={}, startHour={}",
                    todayStart, todayEnd, startHour);

            TodayAmountDetailDTO result = new TodayAmountDetailDTO();

            // 并行执行查询
            CompletableFuture<Map<String, Object>> summaryFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTodayYesterdayBeforeAmountSummary(
                            todayStart, todayEnd, yesterdayStart, yesterdayEnd,
                            beforeYesterdayStart, beforeYesterdayEnd),
                    executorService
            );

            CompletableFuture<List<Map<String, Object>>> hourlyStatsFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTodayHourlyAmountStats(todayStart, todayEnd, startHour),
                    executorService
            );

            CompletableFuture<List<Map<String, Object>>> todayTemplateFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTemplateAmountDistribution(todayStart, todayEnd),
                    executorService
            );

            CompletableFuture<List<Map<String, Object>>> yesterdayTemplateFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTemplateAmountDistribution(yesterdayStart, yesterdayEnd),
                    executorService
            );

            // 等待所有查询完成
            CompletableFuture.allOf(
                    summaryFuture,
                    hourlyStatsFuture,
                    todayTemplateFuture,
                    yesterdayTemplateFuture
            ).join();

            // 处理查询结果
            Map<String, Object> summaryData = summaryFuture.get();
            List<Map<String, Object>> hourlyStats = hourlyStatsFuture.get();
            List<Map<String, Object>> todayTemplateStats = todayTemplateFuture.get();
            List<Map<String, Object>> yesterdayTemplateStats = yesterdayTemplateFuture.get();

            // 设置今日和昨日运单金额和比较
            processSummaryData(result, summaryData);

            // 设置时段分布
            processHourlyDistribution(result, hourlyStats);

            // 设置模板分布
            result.setTemplateDistribution(processTemplateDistribution(todayTemplateStats));

            // 设置今日与昨日模板对比
            processTemplateCompare(result, todayTemplateStats, yesterdayTemplateStats);

            return result;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取今日运单金额详情时线程被中断", e);
            throw new RuntimeException("获取今日运单金额详情失败: 线程被中断", e);
        } catch (ExecutionException e) {
            log.error("获取今日运单金额详情时发生执行异常", e);
            throw new RuntimeException("获取今日运单金额详情失败: " + e.getMessage(), e);
        }
    }

    private void processSummaryData(TodayAmountDetailDTO result, Map<String, Object> summaryData) {
        BigDecimal todayAmount = new BigDecimal(summaryData.get("today_amount").toString());
        BigDecimal yesterdayAmount = new BigDecimal(summaryData.get("yesterday_amount").toString());
        BigDecimal beforeYesterdayAmount = new BigDecimal(summaryData.get("before_yesterday_amount").toString());

        result.setTotalAmount(todayAmount);
        result.setYesterdayAmount(yesterdayAmount);

        // 计算今日与昨日比较
        if (yesterdayAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal compareYesterday = todayAmount
                    .subtract(yesterdayAmount)
                    .multiply(new BigDecimal("100"))
                    .divide(yesterdayAmount, 1, RoundingMode.HALF_UP);
            result.setCompareYesterday(compareYesterday);
        } else {
            result.setCompareYesterday(BigDecimal.ZERO);
        }

        // 计算昨日与前日比较
        if (beforeYesterdayAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal yesterdayCompare = yesterdayAmount
                    .subtract(beforeYesterdayAmount)
                    .multiply(new BigDecimal("100"))
                    .divide(beforeYesterdayAmount, 1, RoundingMode.HALF_UP);
            result.setYesterdayCompare(yesterdayCompare);
        } else {
            result.setYesterdayCompare(BigDecimal.ZERO);
        }
    }

    private void processHourlyDistribution(TodayAmountDetailDTO result,
                                           List<Map<String, Object>> hourlyStats) {
        List<TodayAmountDetailDTO.HourDistributionDTO> hourList = new ArrayList<>();

        for (Map<String, Object> stat : hourlyStats) {
            String hourLabel = (String) stat.get("hour_label");
            BigDecimal amount = new BigDecimal(stat.get("amount").toString());

            TodayAmountDetailDTO.HourDistributionDTO hourDTO = new TodayAmountDetailDTO.HourDistributionDTO();
            hourDTO.setHour(hourLabel);
            hourDTO.setAmount(amount);

            hourList.add(hourDTO);
        }

        result.setHourDistribution(hourList);
    }

    private List<TodayAmountDetailDTO.TemplateDistributionDTO> processTemplateDistribution(
            List<Map<String, Object>> templateStats) {
        List<TodayAmountDetailDTO.TemplateDistributionDTO> templateList = new ArrayList<>();

        // 计算总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (Map<String, Object> stat : templateStats) {
            BigDecimal amount = new BigDecimal(stat.get("amount").toString());
            totalAmount = totalAmount.add(amount);
        }

        // 填充数据
        for (Map<String, Object> stat : templateStats) {
            String type = (String) stat.get("template_type");
            BigDecimal amount = new BigDecimal(stat.get("amount").toString());

            TodayAmountDetailDTO.TemplateDistributionDTO templateDTO = new TodayAmountDetailDTO.TemplateDistributionDTO();
            templateDTO.setName(type);
            templateDTO.setAmount(amount);

            // 计算百分比
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal percentage = amount
                        .multiply(new BigDecimal("100"))
                        .divide(totalAmount, 1, RoundingMode.HALF_UP);
                templateDTO.setPercentage(percentage);
            } else {
                templateDTO.setPercentage(BigDecimal.ZERO);
            }

            templateList.add(templateDTO);
        }

        return templateList;
    }

    private void processTemplateCompare(TodayAmountDetailDTO result,
                                        List<Map<String, Object>> todayTemplateStats,
                                        List<Map<String, Object>> yesterdayTemplateStats) {
        TodayAmountDetailDTO.ComparisonDTO comparison = new TodayAmountDetailDTO.ComparisonDTO();
        List<String> types = new ArrayList<>();
        List<BigDecimal> todayAmounts = new ArrayList<>();
        List<BigDecimal> yesterdayAmounts = new ArrayList<>();

        // 创建映射，用于快速查找
        Map<String, BigDecimal> todayMap = new HashMap<>();
        Map<String, BigDecimal> yesterdayMap = new HashMap<>();

        // 获取所有模板类型
        for (Map<String, Object> stat : todayTemplateStats) {
            String type = (String) stat.get("template_type");
            BigDecimal amount = new BigDecimal(stat.get("amount").toString());
            todayMap.put(type, amount);
            if (!types.contains(type)) {
                types.add(type);
            }
        }

        for (Map<String, Object> stat : yesterdayTemplateStats) {
            String type = (String) stat.get("template_type");
            BigDecimal amount = new BigDecimal(stat.get("amount").toString());
            yesterdayMap.put(type, amount);
            if (!types.contains(type)) {
                types.add(type);
            }
        }

        // 填充数据
        for (String type : types) {
            todayAmounts.add(todayMap.getOrDefault(type, BigDecimal.ZERO));
            yesterdayAmounts.add(yesterdayMap.getOrDefault(type, BigDecimal.ZERO));
        }

        comparison.setTypes(types);
        comparison.setTodayAmounts(todayAmounts);
        comparison.setYesterdayAmounts(yesterdayAmounts);

        result.setComparison(comparison);
    }

    private void processHourDistribution(TodayQuantityDetailDTO result,
                                         List<Map<String, Object>> hourlyStats) {
        List<TodayQuantityDetailDTO.HourDistributionDTO> hourDistribution = new ArrayList<>();

        for (Map<String, Object> stat : hourlyStats) {
            Long hourLabel = (Long) stat.get("original_hour");
            int count = Integer.parseInt(stat.get("count").toString());

            TodayQuantityDetailDTO.HourDistributionDTO hourDTO = new TodayQuantityDetailDTO.HourDistributionDTO();
            hourDTO.setHour(hourLabel + ":00");
            hourDTO.setCount(count);

            hourDistribution.add(hourDTO);
        }

        result.setHourDistribution(hourDistribution);
    }

    private void processTemplateCompare(TodayQuantityDetailDTO result,
                                        List<Map<String, Object>> todayTemplateStats,
                                        List<Map<String, Object>> yesterdayTemplateStats) {
        TodayQuantityDetailDTO.ComparisonDTO comparison = new TodayQuantityDetailDTO.ComparisonDTO();
        List<String> types = new ArrayList<>();
        List<Integer> todayCounts = new ArrayList<>();
        List<Integer> yesterdayCounts = new ArrayList<>();

        // 创建映射，用于快速查找
        Map<String, Integer> todayMap = new HashMap<>();
        Map<String, Integer> yesterdayMap = new HashMap<>();

        // 获取所有模板类型
        for (Map<String, Object> stat : todayTemplateStats) {
            String type = (String) stat.get("template_type");
            int count = Integer.parseInt(stat.get("count").toString());
            todayMap.put(type, count);
            if (!types.contains(type)) {
                types.add(type);
            }
        }

        for (Map<String, Object> stat : yesterdayTemplateStats) {
            String type = (String) stat.get("template_type");
            int count = Integer.parseInt(stat.get("count").toString());
            yesterdayMap.put(type, count);
            if (!types.contains(type)) {
                types.add(type);
            }
        }

        // 填充数据
        for (String type : types) {
            todayCounts.add(todayMap.getOrDefault(type, 0));
            yesterdayCounts.add(yesterdayMap.getOrDefault(type, 0));
        }

        comparison.setTypes(types);
        comparison.setTodayCounts(todayCounts);
        comparison.setYesterdayCounts(yesterdayCounts);

        result.setComparison(comparison);
    }

    private PeakAmountDayDTO buildPeakDay(Map<String, Object> map) {
        if (map == null || map.isEmpty()) return null;
        return new PeakAmountDayDTO(
                map.get("peak_date").toString(),
                ((BigDecimal) map.get("amount")));
    }

    private BigDecimal getBigDecimal(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? new BigDecimal(value.toString()) : BigDecimal.ZERO;
    }

    private Double calculateTotalChange(MonthlyQuantityReq req, BigDecimal currentTotal) {
        LocalDateTime lastStart = req.getStartTime().minusMonths(1);
        LocalDateTime lastEnd = req.getEndTime().minusMonths(1);
        BigDecimal lastTotal = getBigDecimal(manifestRepository.selectAmountStats(lastStart, lastEnd), "total");
        return calculateChange(currentTotal, lastTotal);
    }

    private Double calculateAvgChange(MonthlyQuantityReq req, BigDecimal currentAvg) {
        LocalDateTime lastStart = req.getStartTime().minusMonths(1);
        LocalDateTime lastEnd = req.getEndTime().minusMonths(1);
        BigDecimal lastAvg = getBigDecimal(manifestRepository.selectAmountStats(lastStart, lastEnd), "dailyAvg");
        return calculateChange(currentAvg, lastAvg);
    }

    private RegionalDistributionDTO processRegionData(List<Map<String, Object>> data) {
        Map<String, RegionDTO> areaMap = new LinkedHashMap<>();

        data.forEach(item -> {
            String area = (String) item.get("area");
            String type = (String) item.get("type");
            Integer quantity = ((Long) item.get("quantity")).intValue();

            RegionDTO region = areaMap.computeIfAbsent(area, k ->
                    new RegionDTO(area, 0, new ArrayList<>()));

            region.setTotal(region.getTotal() + quantity);
            region.getTemplates().add(new TemplateCategoryDTO(type, quantity, null));
        });

        // 计算比例
        areaMap.values().forEach(region ->
                region.getTemplates().forEach(t ->
                        t.setRatio(t.getQuantity() * 100.0 / region.getTotal())
                )
        );

        return new RegionalDistributionDTO(
                areaMap.values().stream().mapToInt(RegionDTO::getTotal).sum(),
                new ArrayList<>(areaMap.values())
        );
    }

    private List<TodayQuantityDetailDTO.TemplateDistributionDTO> processTemplateDistribution(
            List<Map<String, Object>> templateStats, int totalCount) {

        List<TodayQuantityDetailDTO.TemplateDistributionDTO> templateList = new ArrayList<>();

        for (Map<String, Object> stat : templateStats) {
            String templateType = (String) stat.get("template_type");
            int count = Integer.parseInt(stat.get("count").toString());

            TodayQuantityDetailDTO.TemplateDistributionDTO templateDTO = new TodayQuantityDetailDTO.TemplateDistributionDTO();
            templateDTO.setName(templateType);
            templateDTO.setValue(count);

            // 计算百分比
            if (totalCount > 0) {
                BigDecimal percentage = new BigDecimal(count)
                        .multiply(new BigDecimal("100"))
                        .divide(new BigDecimal(totalCount), 1, RoundingMode.HALF_UP);
                templateDTO.setPercentage(percentage);
            } else {
                templateDTO.setPercentage(BigDecimal.ZERO);
            }

            templateList.add(templateDTO);
        }

        return templateList;
    }

}