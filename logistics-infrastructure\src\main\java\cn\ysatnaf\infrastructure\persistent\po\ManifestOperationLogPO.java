package cn.ysatnaf.infrastructure.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler; // 用于 JSON 字段
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map; // 用于 changeDetails

/**
 * 运单操作日志 PO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "manifest_operation_log", autoResultMap = true) // autoResultMap=true 用于 JSON 类型处理器
public class ManifestOperationLogPO {

    @TableId(type = IdType.AUTO)
    @Schema(description = "日志主键ID")
    private Long id;

    @Schema(description = "被操作的运单ID")
    private Long manifestId;

    @Schema(description = "操作员ID")
    private Long operatorId;

    @Schema(description = "操作员姓名")
    private String operatorName;

    @Schema(description = "操作发生时间")
    private LocalDateTime operationTime;

    @Schema(description = "操作类型")
    private String operationType;

    @Schema(description = "操作描述")
    private String description;

    // 使用 MyBatis-Plus 提供的 JacksonTypeHandler 处理 JSON 字段
    // 需要确保项目中引入了 jackson 依赖
    @TableField(typeHandler = JacksonTypeHandler.class)
    @Schema(description = "变更详情 (JSON)")
    private Map<String, Object> changeDetails; // 或者使用自定义的 DTO 类

    @Schema(description = "操作上下文")
    private String context;

    @Schema(description = "操作员IP地址")
    private String ipAddress;
} 