package cn.ysatnaf.infrastructure.persistent.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * ReceiverAddressBookPO
 *
 * <AUTHOR>
 * @date 2023/12/22 15:19
 */
@Data
@TableName("tb_receiver_address_book")
public class ReceiverAddressBookPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String openid;

    private Long receiverAreaId;

    /**
     * 日本都道府县级名称
     */
    private String prefectureName;
    /**
     * 日本市区町村级名称
     */
    private String municipalName;
    /**
     * 日本丁目级名称
     */
    private String localitiesName;

    /**
     * 日本都道府县级名称(英文)
     */
    private String prefectureEnName;
    /**
     * 日本市区町村级名称(英文)
     */
    private String municipalEnName;
    /**
     * 日本丁目级名称(英文)
     */
    private String localitiesEnName;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 是否默认地址
     */
    private Boolean isDefault;

}
