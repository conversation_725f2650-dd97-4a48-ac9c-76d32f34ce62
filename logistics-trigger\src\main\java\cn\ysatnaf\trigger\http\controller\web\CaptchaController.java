package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.captcha.service.CaptchaService;
import cn.ysatnaf.domain.captche.model.entity.CaptchaEntity;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;

/**
 * <AUTHOR> <PERSON>
 */
@Tag(name = "验证码相关")
@RequestMapping("/web/captcha")
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
public class CaptchaController {

    private final CaptchaService captchaService;

    @Operation(summary = "获取验证码")
    @PermitAll
    @GetMapping("/get")
    public CommonResult<CaptchaEntity> get() {
        return CommonResult.success(captchaService.get());
    }
}
