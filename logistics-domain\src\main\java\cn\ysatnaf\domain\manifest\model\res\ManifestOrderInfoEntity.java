package cn.ysatnaf.domain.manifest.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
@Data
public class ManifestOrderInfoEntity {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "快递单号")
    private String expressNumber;

    @Schema(description = "商店订单号（客户自定）")
    private String orderNumber;

    @Schema(description = "物品中文名称")
    @NotBlank(message = "物品中文名称不能为空")
    private String itemChineseName;

    @Schema(description = "重量/KG")
    @NotNull(message = "重量不能为空")
    private BigDecimal weight;

    @Schema(description = "数量")
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    @Schema(description = "价值（JPY）")
    @NotNull(message = "价值不能为空")
    private BigDecimal value;

    @Schema(description = "邮编")
    private String zipCode;

    @Schema(description = "收件人")
    private String receiverName;

    @Schema(description = "收件地址")
    private String receiverAddress;

    @Schema(description = "收件人电话")
    private String receiverPhone;

}
