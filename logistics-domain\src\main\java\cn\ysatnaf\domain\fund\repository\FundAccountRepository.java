package cn.ysatnaf.domain.fund.repository;

import cn.ysatnaf.domain.fund.model.entity.BalanceDetailEntity;
import cn.ysatnaf.domain.fund.model.entity.FundAccountEntity;
import cn.ysatnaf.types.common.PageResult;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
public interface FundAccountRepository {
    FundAccountEntity getByUserId(Long userId);

    FundAccountEntity getById(Long id);

    void updateById(FundAccountEntity fundAccountEntity);

    void insert(FundAccountEntity fundAccountEntity);

    void deleteByUserId(Long userId);

    List<FundAccountEntity> listByUserIds(List<Long> userIds);
}
