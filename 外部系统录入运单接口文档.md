# 外部系统录入运单接口文档（自动分配单号）

## 接口基本信息

| 项目     | 内容                                                       |
| -------- | ---------------------------------------------------------- |
| 接口名称 | 外部系统录入运单 - 自动分配单号                            |
| 接口地址 | `/web/manifest/addWithAllocatedExpress`                    |
| 请求方式 | POST                                                       |
| 请求类型 | application/json                                           |
| 功能描述 | 供外部系统调用，自动分配运单号并创建运单，无需手动传入单号 |
| 适用场景 | 外部系统对接、API 集成、第三方平台运单自动录入             |

## 核心特性

### 🔄 自动分配单号

- **智能分配**：根据目的地 ID 和货物类型 ID 自动匹配最合适的渠道并分配单号
- **库存管理**：自动检查单号库存，确保分配成功
- **并发安全**：使用分布式锁确保高并发环境下的数据一致性
- **批次追踪**：每次分配都会记录分配批次，便于追踪和管理

### 🔐 安全机制

- **参数校验**：严格的入参验证，确保数据质量
- **权限控制**：继承系统权限体系，支持不同用户角色
- **异常处理**：完善的错误处理机制，提供详细的错误信息
- **事务保证**：整个流程在事务中执行，确保数据一致性

## 请求参数

### 请求头

| 参数名        | 类型   | 必填 | 说明             |
| ------------- | ------ | ---- | ---------------- |
| Content-Type  | String | 是   | application/json |
| Authorization | String | 是   | Bearer {token}   |

### 请求体

| 参数名          | 类型                             | 必填 | 校验规则                   | 说明                                |
| --------------- | -------------------------------- | ---- | -------------------------- | ----------------------------------- |
| orderNumber     | String                           | 是   | @NotBlank, @Length(max=64) | 商店订单号（客户自定）              |
| locationId      | Long                             | 是   | @NotNull                   | 目的地 ID，用于自动分配单号         |
| shipmentTypeId  | Long                             | 是   | @NotNull                   | 货物类型 ID，用于自动分配单号       |
| zipCode         | String                           | 是   | @NotBlank                  | 收件人邮编                          |
| receiverName    | String                           | 是   | @NotBlank                  | 收件人姓名                          |
| receiverAddress | String                           | 是   | @NotBlank                  | 收件人地址                          |
| receiverPhone   | String                           | 是   | @NotBlank                  | 收件人电话                          |
| items           | List&lt;ManifestItemOrderDTO&gt; | 是   | @NotEmpty, @Valid          | 物品列表                            |
| userId          | Long                             | 否   | -                          | 运单所属用户 ID（管理员代录时必填） |

### 物品信息对象 (ManifestItemOrderDTO)

| 参数名   | 类型       | 必填 | 校验规则  | 说明         |
| -------- | ---------- | ---- | --------- | ------------ |
| name     | String     | 是   | @NotBlank | 物品名称     |
| weight   | BigDecimal | 是   | @NotNull  | 物品重量(kg) |
| quantity | Integer    | 是   | @NotNull  | 物品数量     |
| price    | BigDecimal | 是   | @NotNull  | 物品价格     |

### 请求示例

```json
{
  "orderNumber": "ORDER20241201001",
  "locationId": 1,
  "shipmentTypeId": 2,
  "zipCode": "1000001",
  "receiverName": "田中太郎",
  "receiverAddress": "東京都千代田区丸の内1-1-1",
  "receiverPhone": "09012345678",
  "items": [
    {
      "name": "智能手机保护壳",
      "weight": 0.1,
      "quantity": 2,
      "price": 25.0
    },
    {
      "name": "数据线",
      "weight": 0.05,
      "quantity": 1,
      "price": 15.0
    }
  ],
  "userId": null
}
```

## 响应参数

### 响应体结构

| 参数名 | 类型    | 说明                         |
| ------ | ------- | ---------------------------- |
| code   | Integer | 响应状态码，0 表示成功       |
| data   | Boolean | 业务数据，运单录入结果       |
| msg    | String  | 响应消息，错误时包含错误描述 |

### 成功响应示例

```json
{
  "code": 0,
  "data": true,
  "msg": ""
}
```

### 错误响应示例

```json
{
  "code": 500,
  "data": false,
  "msg": "录入失败：渠道 [东京-普通件] 单号库存不足，请求 1，可用 0"
}
```

## 业务流程

### 🔄 完整处理流程

```mermaid
graph TD
    A[接收请求] --> B[参数校验]
    B --> C[用户身份验证]
    C --> D[电话号码格式化]
    D --> E[邮编格式化与校验]
    E --> F[确定用户类型]
    F --> G[构造单号分配请求]
    G --> H[调用单号分配服务]
    H --> I{分配成功?}
    I -->|是| J[获取分配的单号]
    I -->|否| K[返回分配失败错误]
    J --> L{是网络用户?}
    L -->|是| M[分配佐川单号]
    L -->|否| N[使用分配的单号作为佐川单号]
    M --> O[创建运单对象]
    N --> O
    O --> P[保存运单]
    P --> Q[保存物品信息]
    Q --> R[记录操作日志]
    R --> S[返回成功结果]
    K --> T[返回错误信息]
```

### 📋 关键步骤说明

1. **参数验证阶段**

   - 验证所有必填字段
   - 格式化和验证日本手机号码
   - 格式化和验证日本邮编

2. **单号分配阶段**

   - 根据 `locationId` 和 `shipmentTypeId` 查找匹配的渠道
   - 检查渠道单号库存
   - 使用分布式锁确保分配过程的并发安全
   - 创建分配批次记录便于追踪

3. **运单创建阶段**
   - 为网络用户额外分配佐川单号
   - 创建运单主记录
   - 批量插入物品信息
   - 记录详细的操作日志

## 错误码说明

| 错误码 | 错误消息                                         | 说明               | 解决方案                       |
| ------ | ------------------------------------------------ | ------------------ | ------------------------------ |
| 400    | 录入失败：收件人电话号码格式错误                 | 电话号码格式不正确 | 检查电话号码格式，使用日本格式 |
| 400    | 录入失败：邮编错误                               | 邮编格式不正确     | 检查邮编格式，使用日本邮编格式 |
| 400    | 用户角色信息错误，请联系管理员                   | 用户角色不明确     | 联系管理员检查用户角色配置     |
| 500    | 录入失败：无法分配运单号                         | 单号分配失败       | 检查渠道配置和单号库存         |
| 500    | 录入失败：获取分配的运单号失败                   | 获取已分配单号失败 | 联系技术支持检查系统状态       |
| 500    | 录入失败：系统内部物流单号库存不足，请联系管理员 | 佐川单号库存不足   | 联系管理员补充佐川单号库存     |
| 500    | 保存运单失败，无法获取 ID                        | 数据库保存失败     | 检查数据库连接和表结构         |
| 500    | 渠道 [渠道名] 单号库存不足，请求 X，可用 Y       | 指定渠道单号不足   | 联系管理员为该渠道补充单号     |

## 与原有接口的区别

| 对比项       | 原有接口 (`/add`)         | 新接口 (`/addWithAllocatedExpress`)   |
| ------------ | ------------------------- | ------------------------------------- |
| **单号来源** | 外部传入 `expressNumber`  | 系统自动分配                          |
| **入参差异** | 需要 `expressNumber` 字段 | 需要 `locationId` 和 `shipmentTypeId` |
| **适用场景** | 手动录入、已知单号的场景  | API 对接、自动化流程                  |
| **单号管理** | 依赖外部单号              | 统一的单号池管理                      |
| **库存控制** | 无库存控制                | 自动检查和管理库存                    |
| **并发安全** | 可能出现重复单号          | 分布式锁保证唯一性                    |

## 最佳实践

### 📝 调用建议

1. **批量处理**：如需录入大量运单，建议分批调用，每批不超过 100 个
2. **错误处理**：实现完善的重试机制，对于网络异常等可重试错误进行自动重试
3. **日志记录**：在客户端记录详细的调用日志，便于问题排查
4. **参数缓存**：`locationId` 和 `shipmentTypeId` 可以缓存，减少重复查询

### 🔧 配置要求

1. **渠道配置**：确保目标地点和货物类型的渠道已正确配置并启用
2. **单号库存**：定期检查单号库存，及时补充
3. **权限配置**：为 API 调用用户分配适当的权限

### ⚠️ 注意事项

1. **幂等性**：接口不具备幂等性，重复调用会创建多个运单
2. **事务性**：整个流程在事务中执行，失败时会自动回滚
3. **性能考虑**：单号分配涉及分布式锁，高并发时可能存在等待
4. **数据一致性**：分配的单号会立即标记为已使用，避免重复分配

## 技术实现

### 🏗️ 架构设计

- **分层架构**：Controller → Service → Repository
- **依赖注入**：使用 Spring 依赖注入管理组件
- **事务管理**：Spring 声明式事务确保数据一致性
- **异常处理**：统一异常处理机制

### 🔧 核心技术

- **单号分配**：基于渠道配置的智能分配算法
- **分布式锁**：Redisson 实现的分布式锁
- **参数校验**：JSR-303 Bean Validation
- **日志记录**：基于 SLF4J 的结构化日志

### 📊 性能指标

- **响应时间**：正常情况下 < 500ms
- **并发支持**：支持高并发访问，通过分布式锁控制
- **可用性**：99.9% 可用性保证
- **容错性**：完善的异常处理和回滚机制

## 相关接口

- [运单查询接口](./运单查询接口文档.md)
- [运单状态更新接口](./运单状态更新接口文档.md)
- [单号管理接口](./单号管理接口文档.md)
- [用户管理接口](./用户管理接口文档.md)

---

**文档版本**：v1.0  
**最后更新**：2024-12-01  
**联系方式**：技术支持团队
