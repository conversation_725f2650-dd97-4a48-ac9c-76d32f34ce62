package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.manifest.model.entity.Tracking;
import cn.ysatnaf.domain.manifest.service.TrackingService;
import cn.ysatnaf.trigger.job.TrackingOrderStatusJob;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Tag(name = "轨迹")
@RequestMapping("/web/tracking")
@Slf4j
@RestController
@RequiredArgsConstructor
public class TrackingController {

    private final TrackingService trackingService;

    private final TrackingOrderStatusJob trackingOrderStatusJob;

    @Operation(summary = "根据订单ID获取轨迹列表")
    @PermitAll
    @GetMapping("/getByManifestId/{manifestId}")
    public CommonResult<List<Tracking>> getByManifestId(@PathVariable("manifestId") Long manifestId) {
        return CommonResult.success(trackingService.getByManifestId(manifestId));
    }

    @Operation(summary = "根据运单号获取轨迹列表")
    @PermitAll  
    @GetMapping("/getByExpressNumber/{expressNumber}")
    public CommonResult<List<Tracking>> getByManifestId(@PathVariable("expressNumber") String expressNumber) {
        return CommonResult.success(trackingService.getByExpressNumber(expressNumber));
    }

    @Operation(summary = "抓取佐川物流轨迹")
    @PostMapping("/fetchSawagaInfo")
    public void fetchSawagaInfo() {
        trackingOrderStatusJob.trackingSawagaStatus();
    }

    @Operation(summary = "手动抓取日邮物流轨迹")
    @PostMapping("/fetchJapanPost")
    public void fetchJapanPost() {
        trackingOrderStatusJob.trackingJapanPostStatus();
    }

    @Operation(summary = "手动抓取黑猫物流轨迹")
    @PostMapping("/fetchNekoposuLogisticsJourney")
    public void fetchNekoposuLogisticsJourney() {
        trackingOrderStatusJob.trackNekoposuLogisticsJourney();
    }
}
