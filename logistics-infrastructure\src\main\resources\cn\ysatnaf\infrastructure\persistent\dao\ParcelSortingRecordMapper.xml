<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ysatnaf.infrastructure.persistent.dao.ParcelSortingRecordDao">

    <!-- 其他可能存在的 SQL -->

    <!-- 移除 增加已装箱件数 的 SQL -->
    <!-- 
    <update id="incrementItemsPackedCount">
        UPDATE tb_parcel_sorting_record
        SET items_packed_count = items_packed_count + #{count},
            update_time = NOW()
        WHERE id = #{recordId}
          AND is_delete = 0
    </update>
    -->

</mapper> 