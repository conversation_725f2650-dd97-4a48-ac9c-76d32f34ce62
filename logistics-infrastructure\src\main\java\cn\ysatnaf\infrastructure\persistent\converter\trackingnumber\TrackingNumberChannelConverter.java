package cn.ysatnaf.infrastructure.persistent.converter.trackingnumber;

import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberChannel;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberChannelPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 运单号渠道对象转换器
 * <AUTHOR>
 */
@Mapper
public interface TrackingNumberChannelConverter {

    TrackingNumberChannelConverter INSTANCE = Mappers.getMapper(TrackingNumberChannelConverter.class);

    TrackingNumberChannelPO toPO(TrackingNumberChannel entity);

    TrackingNumberChannel toEntity(TrackingNumberChannelPO po);

    List<TrackingNumberChannel> toEntityList(List<TrackingNumberChannelPO> poList);

    List<TrackingNumberChannelPO> toPOList(List<TrackingNumberChannel> entityList);

} 