package cn.ysatnaf.domain.address.service;

import cn.ysatnaf.domain.address.model.entity.ReceiverAddressBookEntity;
import cn.ysatnaf.types.common.PageResult;

/**
 * ReceiverAddressBookService
 *
 * <AUTHOR>
 * @date 2023/12/22 15:33
 */
public interface ReceiverAddressBookService {
    Boolean add(String openid, Long receiverAreaId, String addressDetail, String name, String phone, Boolean isDefault);

    Boolean delete(Long id);

    Boolean update(Long id, Long receiverAreaId, String openid, String addressDetail, String name, String phone, Boolean isDefault);

    PageResult<ReceiverAddressBookEntity> page(String openid, Integer pageNo, Integer pageSize);

    ReceiverAddressBookEntity getById(Long receiverAddressBookId);

    ReceiverAddressBookEntity getDefault(String openid);
}
