package cn.ysatnaf.domain.manifest.model.req;

import cn.ysatnaf.types.common.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "搜索订单 入参")
@Data
public class ManifestOrderSearchReq extends PageParam {

    @Schema(description = "运单号")
    private String expressNumber;

    @Schema(description = "佐川单号")
    private String sawagaNumber;

    @Schema(description = "商家订单号")
    private String orderNumber;

    @Schema(description = "系统订单号")
    private String orderNo;

    @Schema(description = "关键词")
    private String keyword;

    @Schema(description = "物品名称")
    private String itemName;

    @Schema(description = "收件人姓名，模糊搜索")
    private String receiverName;

    @Schema(description = "创建时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;


    @Schema(description = "揽件时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickUpTimeStart;

    @Schema(description = "揽件时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickUpTimeEnd;


    @Schema(description = "发货时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shipmentTimeStart;

    @Schema(description = "发货时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shipmentTimeEnd;

    /**
     * @see cn.ysatnaf.domain.manifest.model.valobj.ManifestStatus
     */
    @Schema(description = "订单状态: 0或不传为全部; 1-待揽件; 2-已揽件; 3-已发货; 4-已送达")
    private Integer status;

    @Schema(description = "订单所属用户ID, 仅管理员查询时可传")
    private Long userId;

    @Schema(description = "是否回收站")
    private Boolean isDelete = false;
}
