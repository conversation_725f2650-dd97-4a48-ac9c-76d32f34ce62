package cn.ysatnaf.domain.fund.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */

@Schema(description = "更新资金账户 入参")
@Data
public class FundAccountUpdateReq {

    @Schema(description = "账户ID")
    private Long id;

    @Schema(description = "修改额度 +N -N")
    private BigDecimal modifyAmount;

    @Schema(description = "直接设置额度 直接覆盖")
    private BigDecimal setAmount;

    @Schema(description = "备注（改动原因）")
    private String remark;
}
