package cn.ysatnaf.domain.tracking.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NekoposuLogisticsJourney {

    SHIPPING_INSTRUCTIONS(4, "出荷指示"),
    PARCEL_RECEPTION(5, "荷物受付"),
    DISPATCHED(6, "発送済み"),
    PASSED_THROUGH_THE_WORK_SHOP(7, "作業店通過"),
    DELIVERY_COMPLETED(8, "配達完了"),
    UNDEFINED(999, "")
    ;

    private final Integer value;
    private final String text;

    public static NekoposuLogisticsJourney matchByText(String text) {
        for (NekoposuLogisticsJourney journey : values()) {
            if (journey.text.equals(text)) {
                return journey;
            }
        }
        return UNDEFINED;
    }
}
