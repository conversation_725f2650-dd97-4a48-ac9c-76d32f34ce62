package cn.ysatnaf.domain.trackingnumber.service.impl;

import cn.ysatnaf.domain.trackingnumber.model.entity.ShipmentType;
import cn.ysatnaf.domain.trackingnumber.repository.ShipmentTypeRepository;
import cn.ysatnaf.domain.trackingnumber.service.ShipmentTypeService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 货物类型基础数据服务实现
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ShipmentTypeServiceImpl implements ShipmentTypeService {

    private final ShipmentTypeRepository shipmentTypeRepository;

    @Override
    public List<ShipmentType> listActiveShipmentTypes() {
        return shipmentTypeRepository.findAllActive();
    }

    @Override
    public ShipmentType getShipmentTypeById(Long id) {
        ShipmentType shipmentType = shipmentTypeRepository.findById(id);
        if (shipmentType == null) {
            throw new ServiceException("货物类型不存在, ID: " + id);
        }
        return shipmentType;
    }

    @Override
    public ShipmentType getShipmentTypeByCode(String code) {
        ShipmentType shipmentType = shipmentTypeRepository.findByCode(code);
        if (shipmentType == null) {
            throw new ServiceException("货物类型不存在, Code: " + code);
        }
        return shipmentType;
    }
} 