package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.infrastructure.persistent.po.ManifestOperationLogPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 运单操作日志 DAO
 * <AUTHOR>
 */
@Mapper
public interface ManifestOperationLogDao extends BaseMapper<ManifestOperationLogPO> {
    // BaseMapper 提供了基本的 CRUD 功能，如 insert, selectById, selectList 等
    // 如果需要复杂的查询（例如按时间范围、操作类型分页查询），可以在这里添加自定义方法
    // 示例：
    // Page<ManifestOperationLogPO> findByManifestId(@Param("page") Page<ManifestOperationLogPO> page, @Param("manifestId") Long manifestId);
    /**
     * 批量插入运单操作日志
     * @param poList PO 实体列表
     * @return 插入成功的记录数 (通常MyBatis批量返回这个)
     */
    Integer insertBatchSomeColumn(@Param("list")List<ManifestOperationLogPO> poList); // 返回值可以是 void 或 int/Integer
} 