package cn.ysatnaf.domain.manifest.adapter;

import cn.ysatnaf.domain.manifest.model.entity.MasterBill;
import cn.ysatnaf.domain.manifest.model.req.MasterBillCreateReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillUpdateReq;
import cn.ysatnaf.domain.manifest.model.res.MasterBillDetailRes;
import cn.ysatnaf.domain.manifest.model.valobj.MasterBillStatus;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 主提单适配器，用于DTO和Entity之间的转换
 * <AUTHOR>
 */
@Mapper
public interface MasterBillAdapter {

    MasterBillAdapter INSTANCE = Mappers.getMapper(MasterBillAdapter.class);

    /**
     * 创建请求转实体
     */
    MasterBill createReq2Entity(MasterBillCreateReq req);

    /**
     * 更新请求转实体
     */
    MasterBill updateReq2Entity(MasterBillUpdateReq req);

    /**
     * 实体转详情响应
     */
    @Mapping(target = "statusDesc", source = "status", qualifiedByName = "statusToDesc")
    MasterBillDetailRes entity2DetailRes(MasterBill entity);

    /**
     * 实体列表转详情响应列表
     */
    List<MasterBillDetailRes> entityList2DetailResList(List<MasterBill> entityList);

    /**
     * 状态码转描述
     */
    @Named("statusToDesc")
    default String statusToDesc(Integer status) {
        if (status == null) {
            return null;
        }
        MasterBillStatus masterBillStatus = MasterBillStatus.getByCode(status);
        return masterBillStatus == null ? null : masterBillStatus.getMessage();
    }
} 