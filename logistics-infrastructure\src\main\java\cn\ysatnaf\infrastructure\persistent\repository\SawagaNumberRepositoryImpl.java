package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.manifest.model.entity.SawagaNumberEntity;
import cn.ysatnaf.domain.manifest.repository.SawagaNumberRepository;
import cn.ysatnaf.infrastructure.persistent.converter.SawagaNumberConverter;
import cn.ysatnaf.infrastructure.persistent.dao.SawagaNumberDao;
import cn.ysatnaf.infrastructure.persistent.po.SawagaNumberPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class SawagaNumberRepositoryImpl implements SawagaNumberRepository {

    private final SawagaNumberDao sawagaNumberDao;

    @Override
    public void insertBatch(List<SawagaNumberEntity> numbers) {
        List<SawagaNumberPO> sawagaNumberPOS = SawagaNumberConverter.INSTANCE.toSawagaNumberPOList(numbers);
        sawagaNumberDao.insertBatchSomeColumn(sawagaNumberPOS);
    }

    @Override
    public SawagaNumberEntity getOne() {
        LambdaQueryWrapper<SawagaNumberPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SawagaNumberPO::getUsed, false);
        Page<SawagaNumberPO> sawagaNumberPOPage = sawagaNumberDao.selectPage(new Page<>(1, 1), wrapper);
        List<SawagaNumberPO> records = sawagaNumberPOPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        return SawagaNumberConverter.INSTANCE.toSawagaNumberEntity(records.get(0));
    }

    @Override
    public List<SawagaNumberEntity> getBatch(Integer count) {
        LambdaQueryWrapper<SawagaNumberPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SawagaNumberPO::getUsed, false);
        Page<SawagaNumberPO> sawagaNumberPOPage = sawagaNumberDao.selectPage(new Page<>(1, count), wrapper);
        List<SawagaNumberPO> records = sawagaNumberPOPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyList();
        }
        return SawagaNumberConverter.INSTANCE.toSawagaNumberEntityList(records);
    }

    @Override
    public void updateById(SawagaNumberEntity expressNumberEntity) {
        sawagaNumberDao.updateById(SawagaNumberConverter.INSTANCE.toSawagaNumberPO(expressNumberEntity));
    }

    @Override
    public List<SawagaNumberEntity> getBatchSawagaNumberUnused(int size) {
        LambdaQueryWrapper<SawagaNumberPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SawagaNumberPO::getUsed, false);
        Page<SawagaNumberPO> sawagaNumberPOPage = sawagaNumberDao.selectPage(new Page<>(1, size), wrapper);
        List<SawagaNumberPO> records = sawagaNumberPOPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return Collections.emptyList();
        }
        return SawagaNumberConverter.INSTANCE.toSawagaNumberEntityList(records);
    }

    @Override
    public void updateBatchById(List<SawagaNumberEntity> batchSawagaNumber) {
        sawagaNumberDao.updateBatchById(SawagaNumberConverter.INSTANCE.toSawagaNumberPOList(batchSawagaNumber));
    }

    @Override
    public Integer countUsable() {
        LambdaQueryWrapper<SawagaNumberPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SawagaNumberPO::getUsed, false);
        return Math.toIntExact(sawagaNumberDao.selectCount(lambdaQueryWrapper));
    }

    @Override
    public SawagaNumberEntity getByExpressNo(String expressNo) {
        LambdaQueryWrapper<SawagaNumberPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SawagaNumberPO::getExpressNumber, expressNo);
        return SawagaNumberConverter.INSTANCE.toSawagaNumberEntity(sawagaNumberDao.selectOne(wrapper));
    }
}
