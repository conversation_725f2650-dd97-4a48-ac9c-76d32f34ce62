package cn.ysatnaf.domain.item.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "物品名称映射 DTO")
public class ItemNameMappingDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "原始物品名称")
    private String originalName;

    @Schema(description = "映射后的合规物品名称")
    private String mappedName;

    @Schema(description = "是否启用")
    private Boolean isActive;

    @Schema(description = "应用次数")
    private Integer usageCount;

    @Schema(description = "最后应用时间")
    private LocalDateTime lastUsedTime;

    @Schema(description = "备注信息")
    private String remarks;

    @Schema(description = "创建者ID")
    private Long creatorId;

    @Schema(description = "最后更新者ID")
    private Long updaterId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
} 