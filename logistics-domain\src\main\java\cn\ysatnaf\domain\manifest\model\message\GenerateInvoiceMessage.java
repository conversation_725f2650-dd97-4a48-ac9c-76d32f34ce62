package cn.ysatnaf.domain.manifest.model.message;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GenerateInvoiceMessage {

    private List<Long> ids;

    private Date pickUpTimeFrom;

    private Date pickUpTimeTo;

    private List<Integer> shippingFeeTemplateTypes;
}
