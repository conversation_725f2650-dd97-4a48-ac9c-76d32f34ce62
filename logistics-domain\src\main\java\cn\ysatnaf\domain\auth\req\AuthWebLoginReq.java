package cn.ysatnaf.domain.auth.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 */
@Schema(description = "后台登录 入参")
@Data
public class AuthWebLoginReq {

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudaoyuanma")
    @NotEmpty(message = "登录账号不能为空")
    @Length(min = 4, max = 16, message = "账号长度为 4-16 位")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "账号格式为数字以及字母")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "buzhidao")
    @NotEmpty(message = "密码不能为空")
    @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
    private String password;

    // ========== 图片验证码相关 ==========
//
//    @Schema(description = "验证码", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotEmpty(message = "验证码不能为空")
//    private String captcha;
//
//    @Schema(description = "验证码ID", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotEmpty(message = "验证码ID不能为空")
//    private String captchaId;
}
