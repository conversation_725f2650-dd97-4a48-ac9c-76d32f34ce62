package cn.ysatnaf.domain.trackingnumber.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 地点/区域领域实体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Location {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 地点代码 (例如: TKY, OSK)
     */
    private String code;

    /**
     * 地点名称 (例如: 东京, 大阪)
     */
    private String name;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 