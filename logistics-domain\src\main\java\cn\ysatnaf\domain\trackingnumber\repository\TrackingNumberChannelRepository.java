package cn.ysatnaf.domain.trackingnumber.repository;

import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberChannel;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 单号渠道 Repository 接口
 */
public interface TrackingNumberChannelRepository {

    /**
     * 根据ID查找渠道
     * @param id 渠道ID
     * @return Optional 包装的渠道实体
     */
    Optional<TrackingNumberChannel> findById(Long id);

    /**
     * 根据ID列表批量查找渠道
     * @param ids 渠道ID集合
     * @return 渠道实体列表
     */
    List<TrackingNumberChannel> findByIds(Collection<Long> ids);

    /**
     * 根据渠道代码查找渠道
     * @param channelCode 渠道代码
     * @return Optional 包装的渠道实体
     */
    Optional<TrackingNumberChannel> findByChannelCode(String channelCode);

    /**
     * 根据承运商、地点、货物类型组合查找渠道
     * @param carrierId 承运商ID
     * @param locationId 地点ID
     * @param shipmentTypeId 货物类型ID
     * @return Optional 包装的渠道实体
     */
    Optional<TrackingNumberChannel> findByCombination(Long carrierId, Long locationId, Long shipmentTypeId);

    /**
     * 查找所有启用的渠道
     * @return 启用的渠道实体列表
     */
    List<TrackingNumberChannel> findAllActiveChannels();

    /**
     * 保存渠道信息（新增或更新）
     * @param channel 渠道实体
     * @return 保存后的渠道实体（可能包含生成的ID）
     */
    TrackingNumberChannel save(TrackingNumberChannel channel);

    // 可以根据需要添加其他方法，例如：
    // void deleteById(Long id);
    // PageResult<TrackingNumberChannel> pageQuery(...);

} 