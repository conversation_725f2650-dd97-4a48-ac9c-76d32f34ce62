package cn.ysatnaf.domain.item.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder; // 假设用于获取当前用户ID
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordAddReq;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordDTO;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordPageReq;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordUpdateReq;
import cn.ysatnaf.domain.item.model.entity.ProhibitedItemKeyword;
import cn.ysatnaf.domain.item.repository.ProhibitedItemKeywordRepository;
import cn.ysatnaf.domain.item.service.ProhibitedItemKeywordService;
import cn.ysatnaf.types.common.PageResult; // 使用确认的路径
import cn.ysatnaf.types.exception.ServiceException; // 假设有通用的业务异常类
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 违禁关键词服务实现
 */
@Slf4j
@Service // 添加 @Service 注解，让 Spring 管理
@RequiredArgsConstructor
public class ProhibitedItemKeywordServiceImpl implements ProhibitedItemKeywordService {

    private final ProhibitedItemKeywordRepository prohibitedItemKeywordRepository;
    // 注意：这里没有直接依赖 Converter，因为 Service 操作的是 Domain Entity

    @Override
    @Transactional
    public Long addKeyword(ProhibitedItemKeywordAddReq req) {
        // 检查关键词是否已存在
        prohibitedItemKeywordRepository.findByKeyword(req.getKeyword().trim()).ifPresent(k -> {
            throw new ServiceException("关键词 '" + req.getKeyword() + "' 已存在");
        });

        ProhibitedItemKeyword keyword = ProhibitedItemKeyword.builder()
                .keyword(req.getKeyword().trim())
                .isActive(req.getIsActive())
                .remarks(req.getRemarks())
                // .creatorId(LoginUserHolder.getUserId()) // 如果需要记录创建者
                .build();

        return prohibitedItemKeywordRepository.insert(keyword);
    }

    @Override
    @Transactional
    public boolean updateKeyword(ProhibitedItemKeywordUpdateReq req) {
        // 检查记录是否存在
        ProhibitedItemKeyword existingKeyword = prohibitedItemKeywordRepository.findById(req.getId())
                .orElseThrow(() -> new ServiceException("ID 为 " + req.getId() + " 的关键词不存在"));

        // 如果要修改关键词，检查新关键词是否与其它记录冲突
        if (req.getKeyword() != null && !req.getKeyword().trim().equalsIgnoreCase(existingKeyword.getKeyword())) {
            prohibitedItemKeywordRepository.findByKeyword(req.getKeyword().trim()).ifPresent(k -> {
                if (!k.getId().equals(req.getId())) { // 确保不是自身
                    throw new ServiceException("关键词 '" + req.getKeyword() + "' 已被其他记录使用");
                }
            });
            existingKeyword.setKeyword(req.getKeyword().trim());
        }

        // 更新其他允许修改的字段
        if (req.getIsActive() != null) {
            existingKeyword.setIsActive(req.getIsActive());
        }
        if (req.getRemarks() != null) {
            existingKeyword.setRemarks(req.getRemarks());
        }
        // existingKeyword.setUpdaterId(LoginUserHolder.getUserId()); // 如果需要记录更新者

        return prohibitedItemKeywordRepository.updateById(existingKeyword);
    }

    @Override
    @Transactional
    public boolean deleteKeyword(Long id) {
        // 检查记录是否存在
        prohibitedItemKeywordRepository.findById(id)
                .orElseThrow(() -> new ServiceException("ID 为 " + id + " 的关键词不存在"));
        return prohibitedItemKeywordRepository.deleteById(id);
    }

    @Override
    public ProhibitedItemKeywordDTO getKeywordById(Long id) {
        return prohibitedItemKeywordRepository.findById(id)
                .map(entity -> BeanUtil.copyProperties(entity, ProhibitedItemKeywordDTO.class)) // 使用 BeanUtil 或 MapStruct 转换
                .orElse(null);
    }

    @Override
    public PageResult<ProhibitedItemKeywordDTO> pageKeywords(ProhibitedItemKeywordPageReq req) {
        PageResult<ProhibitedItemKeyword> entityPageResult = prohibitedItemKeywordRepository.page(req);

        // 检查分页结果是否为空 (根据 PageResult 定义调整)
        if (entityPageResult == null || entityPageResult.getList() == null || entityPageResult.getList().isEmpty()) {
            return PageResult.empty(); // 使用无参 empty 方法
        }

        List<ProhibitedItemKeywordDTO> dtoList = entityPageResult.getList().stream()
                .map(entity -> BeanUtil.copyProperties(entity, ProhibitedItemKeywordDTO.class))
                .collect(Collectors.toList());

        // 使用正确的构造函数 (List<T> list, Long total)
        return new PageResult<>(dtoList, entityPageResult.getTotal());
    }

    @Override
    public List<String> listActiveKeywords() {
        List<ProhibitedItemKeyword> activeKeywords = prohibitedItemKeywordRepository.listActiveKeywords();
        if (activeKeywords.isEmpty()) {
            return Collections.emptyList();
        }
        return activeKeywords.stream()
                .map(ProhibitedItemKeyword::getKeyword)
                .collect(Collectors.toList());
    }

    @Override
    public String checkItemName(String itemName) {
        if (itemName == null || itemName.trim().isEmpty()) {
            return null;
        }
        // 优化：可以考虑缓存 listActiveKeywords() 的结果
        List<String> keywords = listActiveKeywords();
        if (keywords.isEmpty()) {
            return null;
        }
        // 转换为小写进行不区分大小写的比较
        String lowerCaseItemName = itemName.toLowerCase();
        for (String keyword : keywords) {
            // 简单包含检查，可以根据需要实现更复杂的匹配逻辑 (如：正则、分词)
            if (lowerCaseItemName.contains(keyword.toLowerCase())) {
                log.warn("物品名称 '{}' 包含违禁词 '{}'", itemName, keyword);
                return keyword; // 返回第一个匹配到的违禁词
            }
        }
        return null; // 没有找到违禁词
    }
} 