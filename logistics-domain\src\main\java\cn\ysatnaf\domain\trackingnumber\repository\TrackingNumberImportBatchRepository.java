package cn.ysatnaf.domain.trackingnumber.repository;

import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberImportBatch;
import cn.ysatnaf.types.common.PageResult;

import java.time.LocalDateTime;

/**
 * 单号导入批次记录仓库接口
 * <AUTHOR>
 */
public interface TrackingNumberImportBatchRepository {

    /**
     * 保存导入批次记录
     * @param batch 导入批次实体
     * @return 保存后的实体（包含生成的ID）
     */
    TrackingNumberImportBatch save(TrackingNumberImportBatch batch);

    /**
     * 根据ID查询导入批次记录
     * @param id 批次ID
     * @return 导入批次实体，如果不存在则返回null
     */
    TrackingNumberImportBatch findById(Long id);
    
    /**
     * 分页查询导入批次记录
     * @param uploaderId 上传者ID (可选)
     * @param channelId 渠道ID (可选)
     * @param startTime 导入开始时间 (可选)
     * @param endTime 导入结束时间 (可选)
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    PageResult<TrackingNumberImportBatch> pageQuery(Long uploaderId,
                                                   Long channelId,
                                                   LocalDateTime startTime,
                                                   LocalDateTime endTime,
                                                   int pageNo,
                                                   int pageSize);

} 