package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.address.model.entity.AreaEntity;
import cn.ysatnaf.infrastructure.persistent.po.AreaPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * AreaConverter
 *
 * <AUTHOR>
 * @date 2023/12/22 11:36
 */
@Mapper
public interface AreaConverter {

    AreaConverter INSTANTCE = Mappers.getMapper(AreaConverter.class);

    AreaEntity po2entity(AreaPO po);
}
