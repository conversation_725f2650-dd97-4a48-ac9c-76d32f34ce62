<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ysatnaf.infrastructure.persistent.dao.ManifestDao">

    <select id="selectContainsItemName" resultType="cn.ysatnaf.infrastructure.persistent.po.ManifestPO">
        SELECT m.*
        FROM tb_manifest m
        JOIN tb_manifest_item mi ON m.id = mi.manifest_id
        <where>
            mi.name = #{itemName}
            <if test="status != null">
                AND m.status = #{status}
            </if>
            <if test="userId != null">
                AND m.user_id = #{userId}
            </if>
            <if test="isDelete != null">
                AND m.is_delete = #{isDelete}
            </if>
            <if test="expressNumber != null">
                AND m.express_number = #{expressNumber}
            </if>
            <if test="sawagaNumber != null">
                AND m.sawaga_number = #{sawagaNumber}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND m.order_number = #{orderNo}
            </if>
            <if test="orderNo != null">
                AND m.order_no = #{orderNo}
            </if>
            <if test="receiverName != null and receiverName != ''">
                AND m.receiver_name = #{receiverName}
            </if>
            <if test="createTimeStart != null">
                AND m.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND m.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="pickUpTimeStart != null">
                AND m.pick_up_time &gt;= #{pickUpTimeStart}
            </if>
            <if test="pickUpTimeEnd != null">
                AND m.pick_up_time &lt;= #{pickUpTimeEnd}
            </if>
            <if test="shipmentTimeStart != null">
                AND m.shipment_time &gt;= #{shipmentTimeStart}
            </if>
            <if test="shipmentTimeEnd != null">
                AND m.shipment_time &lt;= #{shipmentTimeEnd}
            </if>
        </where>
        GROUP BY m.id
        ORDER BY m.id DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countContainsItemName" resultType="int">
        SELECT count(m.id)
        FROM tb_manifest m
        JOIN tb_manifest_item mi ON m.id = mi.manifest_id
        <where>
            mi.name = #{itemName}
            <if test="status != null">
                AND m.status = #{status}
            </if>
            <if test="userId != null">
                AND m.user_id = #{userId}
            </if>
            <if test="isDelete != null">
                AND m.is_delete = #{isDelete}
            </if>
            <if test="expressNumber != null">
                AND m.express_number = #{expressNumber}
            </if>
            <if test="sawagaNumber != null">
                AND m.sawaga_number = #{sawagaNumber}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND m.order_number = #{orderNo}
            </if>
            <if test="orderNo != null">
                AND m.order_no = #{orderNo}
            </if>
            <if test="receiverName != null and receiverName != ''">
                AND m.receiver_name = #{receiverName}
            </if>
            <if test="createTimeStart != null">
                AND m.create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND m.create_time &lt;= #{createTimeEnd}
            </if>
            <if test="pickUpTimeStart != null">
                AND m.pick_up_time &gt;= #{pickUpTimeStart}
            </if>
            <if test="pickUpTimeEnd != null">
                AND m.pick_up_time &lt;= #{pickUpTimeEnd}
            </if>
            <if test="shipmentTimeStart != null">
                AND m.shipment_time &gt;= #{shipmentTimeStart}
            </if>
            <if test="shipmentTimeEnd != null">
                AND m.shipment_time &lt;= #{shipmentTimeEnd}
            </if>
        </where>
    </select>

    <select id="selectCompanyRanking" resultType="cn.ysatnaf.domain.statistics.model.dto.CompanyRankingDTO">
        SELECT
        u.nickname AS companyName,
        COUNT(m.id) AS quantity
        FROM tb_manifest m right join tb_user u on m.user_id = u.id
        WHERE
        m.is_delete = 0
        AND m.status >= 2
        <if test="startDate != null">
            AND m.pick_up_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND m.pick_up_time &lt; #{endDate}
        </if>
        GROUP BY m.user_id
        ORDER BY quantity DESC
        limit #{limit}
    </select>

    <select id="selectAmountDistribution" resultType="cn.ysatnaf.domain.statistics.model.dto.AmountDistributionDTO">
        SELECT IF(total_amount >= 100, '100+', CONCAT(FLOOR(total_amount / 10) * 10, '-', FLOOR(total_amount / 10) * 10 + 10)) AS `range`,
               COUNT(id)                                                                                                       AS quantity,
               SUM(total_amount)                                                                                               AS amount
        FROM (SELECT id,
                     COALESCE(cost, 0)
                         + COALESCE(over_length_surcharge, 0)
                         + COALESCE(remote_area_surcharge, 0)
                         + COALESCE(other_cost, 0) AS total_amount
              FROM tb_manifest
              WHERE is_delete = 0
                AND status >= 2) AS temp
        GROUP BY `range`
        ORDER BY IF(`range` = '100+', 1, 0);
    </select>

    <select id="selectTrendData" resultType="cn.ysatnaf.domain.statistics.model.dto.TrendDTO">
        SELECT
        date(date_sub(pick_up_time, interval #{startHour} hour)) AS date,
        COUNT(id) AS quantity,
        SUM(
        COALESCE(cost, 0)
        + COALESCE(over_length_surcharge, 0)
        + COALESCE(remote_area_surcharge, 0)
        + COALESCE(other_cost, 0)
        ) AS amount
        FROM tb_manifest
        WHERE
        is_delete = 0
        AND status >= 2
        AND pick_up_time >= #{startDate}
        AND pick_up_time &lt; #{endDate}
        GROUP BY date
        ORDER BY date ASC
    </select>

    <!-- 获取今日和昨日运单总数 -->
    <select id="getTodayYesterdayQuantitySummary" resultType="java.util.Map">
        SELECT
        (SELECT COUNT(*) FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{todayStart} AND pick_up_time &lt; #{todayEnd}) as today_count,
        (SELECT COUNT(*) FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{yesterdayStart} AND pick_up_time &lt; #{yesterdayEnd}) as yesterday_count,
        (SELECT COUNT(*) FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
          AND pick_up_time >= #{beforeYesterdayStart} AND pick_up_time &lt; #{beforeYesterdayEnd}) as before_yesterday_count
    </select>

    <!-- 获取今日各小时运单数量（按startHour顺序排列，并且用简洁的10:00格式） -->
    <select id="getTodayHourlyStats" resultType="java.util.Map">
        WITH all_hours AS (
        <!-- 生成与startHour对齐的24小时列表 -->
        SELECT MOD(#{startHour} + n, 24) as hour FROM (
        SELECT 0 as n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4
        UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9
        UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14
        UNION SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19
        UNION SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23
        ) hours
        ),
        hourly_stats AS (
        SELECT
        HOUR(pick_up_time) as hour,
        COUNT(*) as count
        FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{todayStart} AND pick_up_time &lt; #{todayEnd}
        GROUP BY HOUR(pick_up_time)
        )
        SELECT
        IFNULL(hs.count, 0) as count,
        ah.hour as original_hour
        FROM all_hours ah
        LEFT JOIN hourly_stats hs ON ah.hour = hs.hour
        ORDER BY
        CASE WHEN ah.hour >= #{startHour} THEN ah.hour - #{startHour}
        ELSE ah.hour - #{startHour} + 24
        END
    </select>

    <!-- 获取今日模板类型分布 -->
    <select id="getTemplateQuantityDistribution" resultType="java.util.Map">
        SELECT
        CASE
        WHEN shipping_fee_template_type IN (1, 4) THEN '普通模板'
        WHEN shipping_fee_template_type = 2 THEN '带电模板'
        WHEN shipping_fee_template_type IN (3, 5) THEN '投函模板'
        ELSE '其他模板'
        END as template_type,
        COUNT(*) as count
        FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{start} AND pick_up_time &lt; #{end}
        GROUP BY template_type
    </select>

    <!-- 获取今日、昨日和前日运单金额总数 -->
    <select id="getTodayYesterdayBeforeAmountSummary" resultType="java.util.Map">
        SELECT
        (SELECT IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{todayStart} AND pick_up_time &lt; #{todayEnd}) as today_amount,
        (SELECT IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{yesterdayStart} AND pick_up_time &lt; #{yesterdayEnd}) as yesterday_amount,
        (SELECT IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{beforeYesterdayStart} AND pick_up_time &lt; #{beforeYesterdayEnd}) as before_yesterday_amount
    </select>

    <!-- 获取今日各小时运单金额 -->
    <select id="getTodayHourlyAmountStats" resultType="java.util.Map">
        WITH all_hours AS (
        <!-- 生成与startHour对齐的24小时列表 -->
        SELECT MOD(#{startHour} + n, 24) as hour FROM (
        SELECT 0 as n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4
        UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9
        UNION SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14
        UNION SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19
        UNION SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23
        ) hours
        ),
        hourly_stats AS (
        SELECT
        HOUR(pick_up_time) as hour,
        SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)) as amount
        FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{todayStart} AND pick_up_time &lt; #{todayEnd}
        GROUP BY HOUR(pick_up_time)
        )
        SELECT
        CONCAT(LPAD(ah.hour, 2, '0'), ':00') as hour_label,
        IFNULL(hs.amount, 0) as amount,
        ah.hour as original_hour
        FROM all_hours ah
        LEFT JOIN hourly_stats hs ON ah.hour = hs.hour
        ORDER BY
        CASE WHEN ah.hour >= #{startHour} THEN ah.hour - #{startHour}
        ELSE ah.hour - #{startHour} + 24
        END
    </select>

    <!-- 获取今日模板类型金额分布 -->
    <select id="getTemplateAmountDistribution" resultType="java.util.Map">
        SELECT
        CASE
        WHEN shipping_fee_template_type IN (1, 4) THEN '普通模板'
        WHEN shipping_fee_template_type = 2 THEN '带电模板'
        WHEN shipping_fee_template_type IN (3, 5) THEN '投函模板'
        ELSE '其他模板'
        END as template_type,
        SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)) as amount
        FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{start} AND pick_up_time &lt; #{end}
        GROUP BY template_type
    </select>
    <!-- 获取时期的总发货数量和金额统计 -->
    <select id="getPeriodSummary" resultType="java.util.Map">
        SELECT
        COUNT(*) as total_quantity,
        IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as total_amount,
        COUNT(DISTINCT DATE(pick_up_time)) as total_days,
        IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)) / COUNT(DISTINCT DATE(pick_up_time)), 0) as avg_daily_amount,
        ifnull(COUNT(*) / COUNT(DISTINCT DATE(pick_up_time)), 0) as avg_daily_quantity
        FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
    </select>

    <!-- 获取趋势数据（支持自定义时间范围和不同粒度） -->
    <select id="getTrendData" resultType="java.util.Map">
        <choose>
            <!-- 查询年时，按月分组 -->
            <when test="unit == 'year'">
                SELECT
                MONTH(pick_up_time) as time_unit,
                DATE_FORMAT(pick_up_time, '%m月') as time_label,
                COUNT(*) as quantity,
                IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                FROM tb_manifest
                WHERE is_delete = 0 AND status >= 2
                AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                GROUP BY time_unit, time_label
                ORDER BY time_unit
            </when>

            <!-- 查询季度时，按月分组 -->
            <when test="unit == 'quarter'">
                SELECT
                MONTH(pick_up_time) as time_unit,
                DATE_FORMAT(pick_up_time, '%m月') as time_label,
                COUNT(*) as quantity,
                IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                FROM tb_manifest
                WHERE is_delete = 0 AND status >= 2
                AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                GROUP BY time_unit, time_label
                ORDER BY time_unit
            </when>

            <!-- 查询月时，按日分组 -->
            <when test="unit == 'month'">
                SELECT
                DAY(pick_up_time) as time_unit,
                DATE_FORMAT(pick_up_time, '%d日') as time_label,
                COUNT(*) as quantity,
                IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                FROM tb_manifest
                WHERE is_delete = 0 AND status >= 2
                AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                GROUP BY time_unit, time_label
                ORDER BY time_unit
            </when>

            <!-- 查询周时，按星期几分组 -->
            <when test="unit == 'week'">
                SELECT
                WEEKDAY(pick_up_time) as time_unit,
                CASE WEEKDAY(pick_up_time)
                WHEN 0 THEN '周一'
                WHEN 1 THEN '周二'
                WHEN 2 THEN '周三'
                WHEN 3 THEN '周四'
                WHEN 4 THEN '周五'
                WHEN 5 THEN '周六'
                WHEN 6 THEN '周日'
                END as time_label,
                COUNT(*) as quantity,
                IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                FROM tb_manifest
                WHERE is_delete = 0 AND status >= 2
                AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                GROUP BY time_unit, time_label
                ORDER BY time_unit
            </when>

            <!-- 查询日时，按小时分组 -->
            <when test="unit == 'day'">
                SELECT
                HOUR(pick_up_time) as time_unit,
                CONCAT(LPAD(HOUR(pick_up_time), 2, '0'), ':00') as time_label,
                COUNT(*) as quantity,
                IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                FROM tb_manifest
                WHERE is_delete = 0 AND status >= 2
                AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                GROUP BY time_unit, time_label
                ORDER BY time_unit
            </when>

            <!-- 自定义时间范围，根据时间跨度自动选择粒度 -->
            <otherwise>
                <choose>
                    <!-- 超过180天按月分组 -->
                    <when test="daysBetween &gt; 180">
                        SELECT
                        DATE_FORMAT(pick_up_time, '%Y-%m') as time_unit,
                        DATE_FORMAT(pick_up_time, '%Y-%m') as time_label,
                        COUNT(*) as quantity,
                        IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                        FROM tb_manifest
                        WHERE is_delete = 0 AND status >= 2
                        AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                        GROUP BY time_unit, time_label
                        ORDER BY time_unit
                    </when>

                    <!-- 30-180天按周分组 -->
                    <when test="daysBetween &gt; 30">
                        SELECT
                        CONCAT(YEAR(pick_up_time), '-', WEEK(pick_up_time)) as time_unit,
                        CONCAT(YEAR(pick_up_time), '年第', WEEK(pick_up_time), '周') as time_label,
                        COUNT(*) as quantity,
                        IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                        FROM tb_manifest
                        WHERE is_delete = 0 AND status >= 2
                        AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                        GROUP BY time_unit, time_label
                        ORDER BY time_unit
                    </when>

                    <!-- 7-30天按日分组 -->
                    <when test="daysBetween &gt; 7">
                        SELECT
                        DATE(pick_up_time) as time_unit,
                        DATE_FORMAT(pick_up_time, '%m-%d') as time_label,
                        COUNT(*) as quantity,
                        IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                        FROM tb_manifest
                        WHERE is_delete = 0 AND status >= 2
                        AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                        GROUP BY time_unit, time_label
                        ORDER BY time_unit
                    </when>

                    <!-- 2-7天按12小时分组 -->
                    <when test="daysBetween &gt; 2">
                        SELECT
                        CONCAT(DATE(pick_up_time), ' ', FLOOR(HOUR(pick_up_time)/12)*12) as time_unit,
                        CONCAT(DATE_FORMAT(pick_up_time, '%m-%d '),
                        CASE WHEN HOUR(pick_up_time) &lt; 12 THEN '上午' ELSE '下午' END) as time_label,
                        COUNT(*) as quantity,
                        IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                        FROM tb_manifest
                        WHERE is_delete = 0 AND status >= 2
                        AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                        GROUP BY time_unit, time_label
                        ORDER BY time_unit
                    </when>

                    <!-- 1-2天按小时分组 -->
                    <otherwise>
                        SELECT
                        CONCAT(DATE(pick_up_time), ' ', HOUR(pick_up_time)) as time_unit,
                        CONCAT(DATE_FORMAT(pick_up_time, '%m-%d '),
                        LPAD(HOUR(pick_up_time), 2, '0'), ':00') as time_label,
                        COUNT(*) as quantity,
                        IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
                        FROM tb_manifest
                        WHERE is_delete = 0 AND status >= 2
                        AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
                        GROUP BY time_unit, time_label
                        ORDER BY time_unit
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>

    <!-- 获取模板类型分布数据 -->
    <select id="getTemplateDistribution" resultType="java.util.Map">
        SELECT
        CASE
        WHEN shipping_fee_template_type IN (1, 4) THEN '普通模板'
        WHEN shipping_fee_template_type = 2 THEN '带电模板'
        WHEN shipping_fee_template_type IN (3, 5) THEN '投函模板'
        ELSE '其他模板'
        END as template_type,
        COUNT(*) as quantity,
        IFNULL(SUM(cost + IFNULL(over_length_surcharge, 0) + IFNULL(remote_area_surcharge, 0) + IFNULL(other_cost, 0)), 0) as amount
        FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
        GROUP BY template_type
    </select>

    <!-- 获取热力图数据 -->
    <select id="getHeatmapData" resultType="java.util.Map">
        SELECT
        HOUR(pick_up_time) as hour,
        WEEKDAY(pick_up_time) as day_of_week,
        COUNT(*) as quantity
        FROM tb_manifest
        WHERE is_delete = 0 AND status >= 2
        AND pick_up_time >= #{startTime} AND pick_up_time &lt; #{endTime}
        GROUP BY hour, day_of_week
        ORDER BY day_of_week, hour
    </select>

    <!-- 更新运单的主提单关联 -->
    <update id="updateMasterBill">
        UPDATE tb_manifest
        SET master_bill_id = #{masterBillId},
            master_bill_number = #{masterBillNumber},
            update_time = NOW()
        WHERE id = #{id}
    </update>
</mapper>