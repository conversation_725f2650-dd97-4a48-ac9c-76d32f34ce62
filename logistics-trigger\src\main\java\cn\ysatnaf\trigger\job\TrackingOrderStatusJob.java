package cn.ysatnaf.trigger.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.Tracking;
import cn.ysatnaf.domain.manifest.model.valobj.ManifestStatus;
import cn.ysatnaf.domain.manifest.model.valobj.TrackingStatus;
import cn.ysatnaf.domain.manifest.repository.TrackingRepository;
import cn.ysatnaf.domain.manifest.service.ManifestService;
import cn.ysatnaf.domain.manifest.service.TrackingService;
import cn.ysatnaf.domain.problem.model.po.ProblemManifestTicketPO;
import cn.ysatnaf.domain.problem.repository.ProblemManifestTicketRepository;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.domain.tracking.model.dto.TrackingDTO;
import cn.ysatnaf.domain.tracking.model.valobj.NekoposuLogisticsJourney;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hang
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TrackingOrderStatusJob {

    private final ManifestService manifestService;

    private final TrackingService trackingService;

    private final TrackingRepository trackingRepository;

    private final ProblemManifestTicketRepository problemManifestTicketRepository;

    //    @Scheduled(cron = "0 0 0,6,12,18 * * *")
    @Transactional(rollbackFor = Exception.class)
    public void trackingOrderStatus() {
        // 查询已发货的运单
        List<Manifest> manifestList = manifestService.findShippedManifests();
        if (CollUtil.isEmpty(manifestList)) {
            log.info("没有已发货未送达的订单");
            return;
        }

        List<List<Manifest>> partition = Lists.partition(manifestList, 10);
        partition.forEach(list -> {
            try {
                // 查询日本海关
                trackingService.queryJpCustomsTracking(list);
            } catch (Exception e) {
                log.error("查询运单状态失败", e);
            }
        });

        // 更新运单
        manifestService.updateBatchByIds(manifestList);


        // 按照轨迹状态分组
//        Map<Integer, List<Manifest>> trackingStatusGroupMap = manifestList.stream().collect(Collectors.groupingBy(Manifest::getTrackingStatus));
//
//        // 每次更新完，重新分组，因为可能一连更新好几种状态
//        // 已发货的，查询是否已起飞或降落
//        List<Manifest> shippedManifest = trackingStatusGroupMap.get(TrackingStatus.SHIPPED.getValue());
//        if (CollUtil.isNotEmpty(shippedManifest)) {
//
//        }

        // 已起飞的，查询是否已清关

        // 已清关的，查询是否已派送或送达
    }

    @Scheduled(cron = "0 0/30 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void trackingSawagaStatus() {
        log.info("开始查询佐川物流数据");
        // 查询已发货的运单
        List<Manifest> manifestList = manifestService.findShippedManifestsByShippingFeeTemplateTypes(Lists.newArrayList(
                ShippingFeeTemplateTypeEnum.GENERAL.getCode(),
                ShippingFeeTemplateTypeEnum.ELECTRONICS.getCode(),
                ShippingFeeTemplateTypeEnum.GENERAL_OSAKA.getCode()));
        log.info("有{}条物流需要查询物流状态", manifestList.size());
        if (CollUtil.isEmpty(manifestList)) {
            log.info("没有已发货未送达的订单");
            return;
        }

        List<List<Manifest>> partition = Lists.partition(manifestList, 10);
        List<Tracking> trackingToSave = new ArrayList<>();
        List<Manifest> manifestToUpdate = new ArrayList<>();
        List<ProblemManifestTicketPO> problemTicketsToSave = new ArrayList<>();

        partition.forEach(list -> {
            List<String> sawagaNumbers = list.stream()
                    .map(Manifest::getTrackingNumber)
                    .collect(Collectors.toList());
            List<String> trackingList;
            try {
                // 查询佐川系统
                log.info("开始查询佐川物流信息");
                trackingList = trackingService.querySawagaTracking(sawagaNumbers);
                if (CollUtil.isEmpty(trackingList)) {
                    log.info("查询佐川物流信息返回为空");
                    return;
                }
            } catch (Exception e) {
                log.error("查询运单状态失败", e);
                return;
            }
            log.info("查询到佐川物流信息{}条", trackingList.size());
            for (int i = 0; i < list.size(); i++) {
                Manifest manifest = list.get(i);
                manifest.setIsOnline(true);
                String tracking = trackingList.get(i);
                log.info("第{}条轨迹:{}", i + 1, tracking);
                if (tracking.startsWith("Picked up")) {
                    if (manifest.getTrackingStatus().equals(TrackingStatus.PICKUP.getValue())) {
                        continue;
                    }
                    manifest.setTrackingStatus(TrackingStatus.PICKUP.getValue());
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTransferredTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(TrackingStatus.PICKUP.getValue())
                            .track(TrackingStatus.PICKUP.getDescription())
                            .time(new Date())
                            .createTime(LocalDateTime.now())
                            .build());
                } else if (tracking.startsWith("Delivered")) {
                    if (manifest.getTrackingStatus().equals(TrackingStatus.DELIVERED.getValue())) {
                        continue;
                    }
                    manifest.setStatus(ManifestStatus.DELIVERED.getCode());
                    manifest.setTrackingStatus(TrackingStatus.DELIVERED.getValue());
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifest.setDeliveredTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(TrackingStatus.DELIVERED.getValue())
                            .track(TrackingStatus.DELIVERED.getDescription())
                            .time(new Date())
                            .createTime(LocalDateTime.now())
                            .build());
                    // 配送完成，删除该运单相关的所有问题工单
                    int deletedCount = problemManifestTicketRepository.deleteByManifestId(manifest.getId());
                    if (deletedCount > 0) {
                        log.info("运单{}配送完成，已删除{}条相关问题工单", manifest.getTrackingNumber(), deletedCount);
                    }
                } else if (tracking.startsWith("In Transit")) {
                    if (manifest.getTrackingStatus().equals(TrackingStatus.DELIVERY.getValue())) {
                        continue;
                    }
                    manifest.setTrackingStatus(TrackingStatus.DELIVERY.getValue());
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(TrackingStatus.DELIVERY.getValue())
                            .track(TrackingStatus.DELIVERY.getDescription())
                            .time(new Date())
                            .createTime(LocalDateTime.now())
                            .build());
                } else if (tracking.startsWith("Held")) {
                    if (manifest.getTrackingStatus().equals(TrackingStatus.HELD.getValue())) {
                        continue;
                    }
                    manifest.setTrackingStatus(TrackingStatus.HELD.getValue());
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(TrackingStatus.HELD.getValue())
                            .track(TrackingStatus.HELD.getDescription())
                            .time(new Date())
                            .createTime(LocalDateTime.now())
                            .build());
                } else if (tracking.startsWith("Attempted")) {
                    if (manifest.getTrackingStatus().equals(TrackingStatus.ATTEMPTED.getValue())) {
                        continue;
                    }
                    manifest.setTrackingStatus(TrackingStatus.ATTEMPTED.getValue());
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(TrackingStatus.ATTEMPTED.getValue())
                            .track(TrackingStatus.ATTEMPTED.getDescription())
                            .time(new Date())
                            .createTime(LocalDateTime.now())
                            .build());
                } else if (tracking.startsWith("Delivery completed")) {
                    if (manifest.getTrackingStatus().equals(TrackingStatus.DELIVERY_COMPLETED.getValue())) {
                        continue;
                    }
                    manifest.setDeliveredTime(LocalDateTime.now());
                    manifest.setStatus(ManifestStatus.DELIVERED.getCode());
                    manifest.setTrackingStatus(TrackingStatus.DELIVERY_COMPLETED.getValue());
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(TrackingStatus.DELIVERY_COMPLETED.getValue())
                            .track(TrackingStatus.DELIVERY_COMPLETED.getDescription())
                            .time(new Date())
                            .createTime(LocalDateTime.now())
                            .build());
                    // 配送完成，删除该运单相关的所有问题工单
                    int deletedCount = problemManifestTicketRepository.deleteByManifestId(manifest.getId());
                    if (deletedCount > 0) {
                        log.info("运单{}配送完成，已删除{}条相关问题工单", manifest.getTrackingNumber(), deletedCount);
                    }
                } else if (tracking.startsWith(TrackingStatus.ON_VEHICLE.getKeyword())) {
                    if (manifest.getTrackingStatus().equals(TrackingStatus.ON_VEHICLE.getValue())) {
                        continue;
                    }
                    manifest.setTrackingStatus(TrackingStatus.ON_VEHICLE.getValue());
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(TrackingStatus.ON_VEHICLE.getValue())
                            .track(TrackingStatus.ON_VEHICLE.getDescription())
                            .time(new Date())
                            .createTime(LocalDateTime.now())
                            .build());
                } else if (tracking.equals(TrackingStatus.EXCEPTION.getKeyword())) {
                    if (manifest.getTrackingStatus().equals(TrackingStatus.EXCEPTION.getValue())) {
                        continue;
                    }
                    manifest.setTrackingStatus(TrackingStatus.EXCEPTION.getValue());
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(TrackingStatus.EXCEPTION.getValue())
                            .track(TrackingStatus.EXCEPTION.getDescription())
                            .time(new Date())
                            .createTime(LocalDateTime.now())
                            .build());
                    problemTicketsToSave.add(ProblemManifestTicketPO.builder()
                            .manifestId(manifest.getId())
                            .trackingNumber(manifest.getTrackingNumber())
                            .customerAccountId(manifest.getUserId())
                            .problemTypeCode("BRANCH_INQUIRY")
                            .problemDescription("営業所へお問い合わせください。")
                            .status("PENDING")
                            .build());
                } else if (tracking.startsWith(TrackingStatus.ON_VEHICLE_BACK_TO_BRANCH.getKeyword())) {
                    if (manifest.getTrackingStatus().equals(TrackingStatus.ON_VEHICLE_BACK_TO_BRANCH.getValue())) {
                        continue;
                    }
                    manifest.setTrackingStatus(TrackingStatus.ON_VEHICLE_BACK_TO_BRANCH.getValue());
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(TrackingStatus.ON_VEHICLE_BACK_TO_BRANCH.getValue())
                            .track(TrackingStatus.ON_VEHICLE_BACK_TO_BRANCH.getDescription())
                            .time(new Date())
                            .createTime(LocalDateTime.now())
                            .build());
                    problemTicketsToSave.add(ProblemManifestTicketPO.builder()
                            .manifestId(manifest.getId())
                            .trackingNumber(manifest.getTrackingNumber())
                            .customerAccountId(manifest.getUserId())
                            .problemTypeCode("DELIVERY_FAILED")
                            .problemDescription("持戻り。")
                            .status("PENDING")
                            .build());
                } else if (tracking.startsWith("No Record")) {
                    continue;
                } else {
                    TrackingStatus trackingStatus = TrackingStatus.matchByKeyword(tracking);
                    if (trackingStatus.equals(TrackingStatus.NONE)) {
                        List<Tracking> tracks = trackingService.getByManifestId(manifest.getId());
                        Tracking track = tracks.get(tracks.size() - 1);
                        if (track.getTrack().equals(tracking)) {
                            continue;
                        }
                        manifest.setTrackingStatus(TrackingStatus.UNDEFINED.getValue());
                        manifest.setTrackingUpdateTime(LocalDateTime.now());
                        manifestToUpdate.add(manifest);
                        trackingToSave.add(Tracking.builder()
                                .trackingNumber(manifest.getTrackingNumber())
                                .manifestId(manifest.getId())
                                .operatorId(0L)
                                .status(TrackingStatus.UNDEFINED.getValue())
                                .track(tracking)
                                .time(new Date())
                                .createTime(LocalDateTime.now())
                                .build());
                    } else {
                        if (manifest.getTrackingStatus().equals(trackingStatus.getValue())) {
                            continue;
                        }
                        manifest.setTrackingStatus(trackingStatus.getValue());
                        manifest.setTrackingUpdateTime(LocalDateTime.now());
                        manifestToUpdate.add(manifest);
                        trackingToSave.add(Tracking.builder()
                                .trackingNumber(manifest.getTrackingNumber())
                                .manifestId(manifest.getId())
                                .operatorId(0L)
                                .status(trackingStatus.getValue())
                                .track(trackingStatus.getDescription())
                                .time(new Date())
                                .createTime(LocalDateTime.now())
                                .build());
                    }
                }
            }
        });

        // 更新运单
        if (CollUtil.isEmpty(manifestToUpdate)) {
            log.info("没有需要更新的物流轨迹");
            return;
        }
        List<List<Manifest>> lists = Lists.partition(manifestToUpdate, 100);
        for (List<Manifest> list : lists) {
            manifestService.updateBatchByIds(list);
        }
        log.info("更新物流状态完成，共更新{}条", manifestToUpdate.size());
        // 插入轨迹
        if (CollUtil.isNotEmpty(trackingToSave)) {
            log.info("准备更新物流轨迹，总共{}条", trackingToSave.size());
            for (List<Tracking> trackings : Lists.partition(trackingToSave, 100)) {
                trackingService.insertBatch(trackings);
            }
        }

        // 保存问题工单
        if (CollUtil.isNotEmpty(problemTicketsToSave)) {
            log.info("准备保存问题运单工单，总共{}条", problemTicketsToSave.size());
            for (List<ProblemManifestTicketPO> tickets : Lists.partition(problemTicketsToSave, 100)) {
                for (ProblemManifestTicketPO ticket : tickets) {
                    problemManifestTicketRepository.save(ticket);
                }
            }
            log.info("问题运单工单保存完成");
        }

        // 按照轨迹状态分组
//        Map<Integer, List<Manifest>> trackingStatusGroupMap = manifestList.stream().collect(Collectors.groupingBy(Manifest::getTrackingStatus));
//
//        // 每次更新完，重新分组，因为可能一连更新好几种状态
//        // 已发货的，查询是否已起飞或降落
//        List<Manifest> shippedManifest = trackingStatusGroupMap.get(TrackingStatus.SHIPPED.getValue());
//        if (CollUtil.isNotEmpty(shippedManifest)) {
//
//        }

        // 已起飞的，查询是否已清关

        // 已清关的，查询是否已派送或送达
    }

    @Scheduled(cron = "0 1/30 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void trackNekoposuLogisticsJourney() {
        log.info("开始查询黑猫物流数据");
        // 查询已发货的投函运单
        List<Manifest> manifestList = manifestService.findShippedManifestsByShippingFeeTemplateTypes(Lists.newArrayList(ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode()));
        log.info("有{}条投函运单需要查询物流状态", manifestList.size());
        if (CollUtil.isEmpty(manifestList)) {
            log.info("没有已发货未送达的投函运单");
            return;
        }
        List<List<Manifest>> partition = Lists.partition(manifestList, 10);
        List<Tracking> trackingToSave = new ArrayList<>();
        List<Manifest> manifestToUpdate = new ArrayList<>();
        partition.forEach(list -> {
            List<String> expressNumbers = new ArrayList<>();
            List<Long> manifestIds = new ArrayList<>();
            Map<String, Manifest> expressNumberManifestMap = new HashMap<>();
            for (Manifest manifest : list) {
                String expressNumber = manifest.getTrackingNumber();
                manifestIds.add(manifest.getId());
                expressNumbers.add(expressNumber);
                expressNumberManifestMap.put(expressNumber, manifest);
            }
            // 查询黑猫系统
            Map<String, List<TrackingDTO>> trackingDTOMap = new HashMap<>();
            try {
                log.info("开始查询黑猫物流轨迹, 单号:{}", String.join(",", expressNumbers));
                trackingDTOMap = trackingService.queryNekoposuLogisticsJourney(expressNumbers);
            } catch (Exception e) {
                log.error("查询黑猫物流错误。单号:{}, 原因:", String.join(",", expressNumbers), e);
            }
            // 查询物流轨迹
            Map<Long, List<Tracking>> trackingListMap = new HashMap<>(list.size());
            List<Tracking> trackingList = trackingService.getByManifestIds(manifestIds);
            if (CollUtil.isNotEmpty(trackingList)) {
                trackingListMap = trackingList.stream().collect(Collectors.groupingBy(Tracking::getManifestId));
            }
            log.info("查询黑猫物流轨迹完毕");
            Map<Long, List<Tracking>> finalTrackingListMap = trackingListMap;
            trackingDTOMap.forEach((expressNumber, dtoList) -> {
                if (CollUtil.isEmpty(dtoList)) {
                    return;
                }
                Manifest manifest = expressNumberManifestMap.get(expressNumber);
                List<Tracking> trackings = finalTrackingListMap.get(manifest.getId());
                for (TrackingDTO trackingDTO : dtoList) {
                    boolean noneMatch = trackings.stream().noneMatch(tracking -> tracking.getTrack().equals(trackingDTO.getTracking()) && tracking.getTime().equals(trackingDTO.getTime()));
                    if (!noneMatch) {
                        continue;
                    }

                    Tracking tracking = Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .status(100)
                            .track(trackingDTO.getTracking())
                            .place(trackingDTO.getPlace())
                            .time(trackingDTO.getTime())
                            .operatorId(0L)
                            .operatorName("系统").build();
                    trackingToSave.add(tracking);

                    if (trackingDTO.getTracking().contains(NekoposuLogisticsJourney.DELIVERY_COMPLETED.getText())) {
                        manifest.setStatus(ManifestStatus.DELIVERED.getCode());
                        manifest.setDeliveredTime(tracking.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                        // 黑猫投函配送完成，删除该运单相关的所有问题工单
                        int deletedCount = problemManifestTicketRepository.deleteByManifestId(manifest.getId());
                        if (deletedCount > 0) {
                            log.info("黑猫投函运单{}配送完成，已删除{}条相关问题工单", manifest.getTrackingNumber(), deletedCount);
                        }
                    }
                    manifest.setIsOnline(true);
                    manifestToUpdate.add(manifest);
                }
            });
        });
        // 更新运单
        if (CollUtil.isEmpty(manifestToUpdate)) {
            log.info("没有需要更新的黑猫物流轨迹");
            return;
        }
        List<List<Manifest>> lists = Lists.partition(manifestToUpdate, 100);
        for (List<Manifest> list : lists) {
            manifestService.updateBatchByIds(list);
        }
        log.info("更新黑猫物流状态完成，共更新{}条", manifestToUpdate.size());
        // 插入轨迹
        if (CollUtil.isNotEmpty(trackingToSave)) {
            log.info("准备更新黑猫物流轨迹，总共{}条", trackingToSave.size());
            for (List<Tracking> tracking : Lists.partition(trackingToSave, 100)) {
                trackingService.insertBatch(tracking);
            }
        }
    }

    @Scheduled(cron = "0 0/32 * * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void trackingJapanPostStatus() {
        log.info("开始查询日邮物流数据");
        // 查询已发货的投函运单
        List<Manifest> manifestList = manifestService.findShippedManifestsByShippingFeeTemplateTypes(Lists.newArrayList(ShippingFeeTemplateTypeEnum.SMALL_PARCEL_OSAKA.getCode()));
        log.info("有{}条投函运单需要查询物流状态", manifestList.size());
        if (CollUtil.isEmpty(manifestList)) {
            log.info("没有已发货未送达的投函运单");
            return;
        }

        List<List<Manifest>> partition = Lists.partition(manifestList, 10);
        List<Tracking> trackingToSave = new ArrayList<>();
        List<Manifest> manifestToUpdate = new ArrayList<>();
        partition.forEach(list -> {
            List<String> expressNumbers = list.stream().map(Manifest::getTrackingNumber).collect(Collectors.toList());
            List<TrackingDTO> trackingList;
            try {
                // 查询佐川系统
                log.info("开始查询日邮物流信息");
                trackingList = trackingService.queryJapanPostTracking(expressNumbers);
                if (CollUtil.isEmpty(trackingList)) {
                    log.info("查询日邮物流信息返回为空");
                    return;
                }
            } catch (Exception e) {
                log.error("查询运单状态失败", e);
                return;
            }
            log.info("查询到日邮物流信息{}条", trackingList.size());
            Map<String, TrackingDTO> expressNumberTrackingDTOMap = trackingList.stream().collect(Collectors.toMap(TrackingDTO::getExpressNumber, dto -> dto));
            for (Manifest manifest : list) {
                try {
                    TrackingDTO tracking = expressNumberTrackingDTOMap.get(manifest.getTrackingNumber());
                    if (tracking == null) {
                        continue;
                    }
                    List<Tracking> tracks = trackingRepository.getByManifestId(manifest.getId());
                    if (CollUtil.isNotEmpty(tracks)) {
                        Tracking track = tracks.get(0);
                        if (track.getTrack().replaceAll(" 【日本邮政】", "").equals(tracking.getTracking())) {
                            continue;
                        }
                    }

                    if (tracking.getTracking().equals("お届け先にお届け済み")) {
                        manifest.setStatus(ManifestStatus.DELIVERED.getCode());
                        manifest.setTrackingStatus(TrackingStatus.DELIVERED.getValue());
                        manifest.setDeliveredTime(LocalDateTime.now());
                        // 日邮配送完成，删除该运单相关的所有问题工单
                        int deletedCount = problemManifestTicketRepository.deleteByManifestId(manifest.getId());
                        if (deletedCount > 0) {
                            log.info("日邮运单{}配送完成，已删除{}条相关问题工单", manifest.getTrackingNumber(), deletedCount);
                        }
                    } else {
                        manifest.setTrackingStatus(TrackingStatus.DELIVERY.getValue());
                    }
                    manifest.setIsOnline(true);
                    manifest.setTrackingUpdateTime(LocalDateTime.now());
                    manifestToUpdate.add(manifest);
                    trackingToSave.add(Tracking.builder()
                            .trackingNumber(manifest.getTrackingNumber())
                            .manifestId(manifest.getId())
                            .operatorId(0L)
                            .status(manifest.getTrackingStatus())
                            .track(tracking.getTracking() + " 【日本邮政】")
                            .place(tracking.getPlace())
                            .time(tracking.getTime())
                            .createTime(LocalDateTime.now())
                            .build());
                } catch (Exception e) {
                    log.error("抓取日邮物流信息错误, 单号:{}, 错误:{}", manifest.getTrackingNumber(), e.getMessage());
                }
            }
        });

        // 更新运单
        if (CollUtil.isEmpty(manifestToUpdate)) {
            log.info("没有需要更新的物流轨迹");
            return;
        }
        List<List<Manifest>> lists = Lists.partition(manifestToUpdate, 100);
        for (List<Manifest> list : lists) {
            manifestService.updateBatchByIds(list);
        }
        log.info("更新物流状态完成，共更新{}条", manifestToUpdate.size());
        // 插入轨迹
        if (CollUtil.isNotEmpty(trackingToSave)) {
            log.info("准备更新物流轨迹，总共{}条", trackingToSave.size());
            for (List<Tracking> trackings : Lists.partition(trackingToSave, 100)) {
                trackingService.insertBatch(trackings);
            }
        }
    }
}
