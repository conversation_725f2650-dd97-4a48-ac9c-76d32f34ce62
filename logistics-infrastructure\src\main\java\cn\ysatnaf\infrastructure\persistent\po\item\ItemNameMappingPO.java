package cn.ysatnaf.infrastructure.persistent.po.item;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物品名称映射表 持久化对象
 */
@Data
@TableName("item_name_mappings")
public class ItemNameMappingPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 原始物品名称 (包含违禁词, 作为查找键)
     */
    @TableField("original_name")
    private String originalName;

    /**
     * 映射后的合规物品名称
     */
    @TableField("mapped_name")
    private String mappedName;

    /**
     * 此映射是否启用 (TRUE: 启用, FALSE: 禁用)
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 此映射被应用的次数
     */
    @TableField("usage_count")
    private Integer usageCount;

    /**
     * 最后应用时间
     */
    @TableField("last_used_time")
    private LocalDateTime lastUsedTime;

    /**
     * 备注信息
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建者ID
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 最后更新者ID
     */
    @TableField(value = "updater_id", fill = FieldFill.INSERT_UPDATE)
    private Long updaterId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

} 