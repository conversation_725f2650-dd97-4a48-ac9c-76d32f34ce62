package cn.ysatnaf.domain.manifest.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.fund.service.FundAccountService;
import cn.ysatnaf.domain.log.model.entity.OperationLogEntity;
import cn.ysatnaf.domain.log.model.valobj.OperationType;
import cn.ysatnaf.domain.log.service.OperationLogService;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.Tracking;
import cn.ysatnaf.domain.manifest.model.message.ShipmentMessage;
import cn.ysatnaf.domain.manifest.model.valobj.ManifestStatus;
import cn.ysatnaf.domain.manifest.model.valobj.TrackingStatus;
import cn.ysatnaf.domain.manifest.repository.ManifestRepository;
import cn.ysatnaf.domain.manifest.service.OperateManifestService;
import cn.ysatnaf.domain.manifest.service.TrackingService;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RequiredArgsConstructor
@Service
public class OperateManifestServiceImpl implements OperateManifestService {

    private final UserService userService;
    private final RabbitTemplate rabbitTemplate;
    private final ManifestRepository manifestRepository;
    private final TrackingService trackingService;
    private final OperationLogService operationLogService;

    public void ship(List<Manifest> manifests) {
        userService.validateAdmin();
        FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
        List<OperationLogEntity> operationLogEntities = new ArrayList<>();
        List<Tracking> trackingList = new ArrayList<>();
        manifests.forEach(manifest -> {
            if (manifest.ifShipped()) {
                return;
            }

            manifest.setStatus(ManifestStatus.SHIPPED.getCode());
            manifest.setShipmentTime(LocalDateTime.now());
            manifest.setTrackingStatus(TrackingStatus.SHIPPED.getValue());
            manifest.setTrackingUpdateTime(LocalDateTime.now());
            // 扣费操作
            BigDecimal totalCost = manifest.getTotalCost();
            if (!fundAccountService.cost(manifest.getUserId(), totalCost, manifest.getId())) {
                UserEntity userEntity = userService.getById(manifest.getUserId());
                throw new ServiceException("用户【" + userEntity.getNickname() + "】余额不足，导出发货失败");
            }
            // 发送消息到队列，告知已发货
            ShipmentMessage message = ShipmentMessage.builder()
                    .trackingNumber(manifest.getExpressNumber())
                    .shippingFee(totalCost).build();
            rabbitTemplate.convertAndSend("logistics.info", "ship", message);

            // 记录轨迹
            trackingList.add(Tracking.builder()
                    .trackingNumber(manifest.getTrackingNumber())
                    .manifestId(manifest.getId())
                    .operatorId(LoginUserHolder.getLoginUser().getId())
                    .status(TrackingStatus.SHIPPED.getValue())
                    .time(new Date())
                    .track(TrackingStatus.SHIPPED.getDescription()).build());

            // 记录日志
            operationLogEntities.add(OperationLogEntity.builder()
                    .operatorId(LoginUserHolder.getLoginUser().getId())
                    .operationType(OperationType.MANIFEST_SHIPPING.getValue())
                    .operation(OperationType.MANIFEST_SHIPPING.getDescription())
                    .manifestId(manifest.getId())
                    .build());
        });


        if (CollUtil.isNotEmpty(manifests)) {
            manifestRepository.updateBatchByIds(manifests);
        }

        if (CollUtil.isNotEmpty(trackingList)) {
            trackingService.insertBatch(trackingList);
        }

        if (CollUtil.isNotEmpty(operationLogEntities)) {
            operationLogService.logBatch(operationLogEntities);
        }
    }
}
