package cn.ysatnaf.domain.problem.model.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 问题运单处理工单表 PO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("problem_manifest_tickets")
public class ProblemManifestTicketPO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long manifestId;

    private String trackingNumber;

    private Long customerAccountId;

    private String problemTypeCode;

    private String problemDescription;

    private String status;

    private Integer priority;

    private Long assignedToUserId;

    private String remarks;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private LocalDateTime resolvedTime;

} 