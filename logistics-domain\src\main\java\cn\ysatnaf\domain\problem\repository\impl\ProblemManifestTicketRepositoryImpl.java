package cn.ysatnaf.domain.problem.repository.impl;

import cn.ysatnaf.domain.problem.model.po.ProblemManifestTicketPO;
import cn.ysatnaf.domain.problem.repository.ProblemManifestTicketRepository;
import cn.ysatnaf.domain.problem.repository.mapper.ProblemManifestTicketMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * 问题运单处理工单表 Repository 实现类
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class ProblemManifestTicketRepositoryImpl implements ProblemManifestTicketRepository {

    private final ProblemManifestTicketMapper problemManifestTicketMapper;

    @Override
    public void save(ProblemManifestTicketPO ticket) {
        problemManifestTicketMapper.insert(ticket);
    }

    @Override
    public int deleteByManifestId(Long manifestId) {
        LambdaQueryWrapper<ProblemManifestTicketPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProblemManifestTicketPO::getManifestId, manifestId);
        return problemManifestTicketMapper.delete(wrapper);
    }
} 