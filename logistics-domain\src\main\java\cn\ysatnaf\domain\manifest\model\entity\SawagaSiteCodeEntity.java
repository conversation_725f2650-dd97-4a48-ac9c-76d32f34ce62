package cn.ysatnaf.domain.manifest.model.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

/**
 * <AUTHOR> Hang
 */
@Data
public class SawagaSiteCodeEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 编码第一部分
     */
    private String codeOne;

    /**
     * 编码第二部分
     */
    private String codeTwo;

    public String acquireCode() {
        return codeOne + "-" + codeTwo;
    }

    public String acquireBarcodeCode() {
        return "c" + (StrUtil.padPre(codeOne, 4, "$") + codeTwo)
                .replaceAll("A", "+")
                .replaceAll("B", "-")
                .replaceAll("-", "") + "d";
    }

    public static String acquireBarcodeCode(String code) {
        String[] split = code.split("-");
        String codeOne = split[0];
        String codeTwo = split[1];
        return "c" + (StrUtil.padPre(codeOne, 4, "$") + codeTwo)
                .replaceAll("A", "+")
                .replaceAll("B", "-")
                .replaceAll("-", "") + "d";
    }
}
