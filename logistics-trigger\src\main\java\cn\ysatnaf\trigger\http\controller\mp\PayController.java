package cn.ysatnaf.trigger.http.controller.mp;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ManaFestController
 *
 * <AUTHOR>
 * @date 2023/12/22 15:14
 */
@Tag(name = "支付相关")
@RequestMapping("/mp/pay")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class PayController {

}
