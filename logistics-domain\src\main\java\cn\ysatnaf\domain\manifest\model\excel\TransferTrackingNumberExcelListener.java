package cn.ysatnaf.domain.manifest.model.excel;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Data
public class TransferTrackingNumberExcelListener implements ReadListener<TransferTrackingNumberRow> {
    private List<TransferTrackingNumberRow> parsedData = new ArrayList<>();

    @Override
    public void invoke(TransferTrackingNumberRow transferTrackingNumberRow, AnalysisContext analysisContext) {
        log.info("批量转单号解析到上传数据。原单号-[{}], 转单号-[{}]", transferTrackingNumberRow.getOriginalTrackingNumber(), transferTrackingNumberRow.getTransferredTrackingNumber());
        if (!StrUtil.isAllNotBlank(transferTrackingNumberRow.getOriginalTrackingNumber(), transferTrackingNumberRow.getTransferredTrackingNumber())) {
            return;
        }

        // 收集数据
        // 去除分号
        transferTrackingNumberRow.setOriginalTrackingNumber(transferTrackingNumberRow.getOriginalTrackingNumber().replace("'", ""));
        parsedData.add(transferTrackingNumberRow);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
