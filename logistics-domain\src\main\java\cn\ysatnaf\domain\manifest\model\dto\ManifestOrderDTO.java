package cn.ysatnaf.domain.manifest.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "物流订单信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ManifestOrderDTO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "快递单号")
    private String expressNumber;

    @Schema(description = "佐川号码")
    private String sawagaNumber;

    @Schema(description = "转单号")
    private String transferredTrackingNumber;

    @Schema(description = "商店订单号（客户自定）")
    private String orderNumber;

    @Schema(description = "系统订单号")
    private String orderNo;

    @Schema(description = "邮编")
    private String receiverZipCode;

    @Schema(description = "收件人")
    private String receiverName;

    @Schema(description = "收件地址")
    private String receiverAddress;

    @Schema(description = "收件人电话")
    private String receiverPhone;

    @Schema(description = "运单物品列表")
    private List<ManifestItemOrderDTO> manifestItems;

    @Schema(description = "费用")
    private BigDecimal cost;

    @Schema(description = "超长费")
    private BigDecimal overLengthSurcharge;

    @Schema(description = "偏远费")
    private BigDecimal remoteAreaSurcharge;

    @Schema(description = "其他费用名称")
    private String otherCostName;

    @Schema(description = "其他费用")
    private BigDecimal otherCost;

    @Schema(description = "重量")
    private BigDecimal weight;

    @Schema(description = "长")
    private BigDecimal length;

    @Schema(description = "宽")
    private BigDecimal width;

    @Schema(description = "高")
    private BigDecimal height;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "体积重量")
    private BigDecimal dimensionWeight;

    @Schema(description = "提单ID")
    private Long masterBillId;

    @Schema(description = "提单号")
    private String masterBillNumber;

    @Schema(description = "运费模板类型")
    private Integer shippingFeeTemplateType;

    @Schema(description = "状态: 1-待揽件 2-已揽件 3-已发货 4-已送达")
    private Integer status;

    @Schema(description = "所属用户ID")
    private Long userId;

    @Schema(description = "所属用户昵称")
    private String userNickname;

    @Schema(description = "预报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "目的地ID")
    private Long locationId;

    @Schema(description = "货物类型ID")
    private Long shipmentTypeId;

}
