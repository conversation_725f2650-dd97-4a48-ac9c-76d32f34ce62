package cn.ysatnaf.infrastructure.persistent.repository.trackingnumber;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberAllocationBatch;
import cn.ysatnaf.domain.trackingnumber.repository.TrackingNumberAllocationBatchRepository;
import cn.ysatnaf.infrastructure.persistent.converter.trackingnumber.TrackingNumberAllocationBatchConverter;
import cn.ysatnaf.infrastructure.persistent.dao.trackingnumber.TrackingNumberAllocationBatchDao;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberAllocationBatchPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 单号分配批次记录仓库实现
 * <AUTHOR>
 */
@Repository
public class TrackingNumberAllocationBatchRepositoryImpl extends ServiceImpl<TrackingNumberAllocationBatchDao, TrackingNumberAllocationBatchPO> implements TrackingNumberAllocationBatchRepository {

    private final TrackingNumberAllocationBatchConverter converter = TrackingNumberAllocationBatchConverter.INSTANCE;

    @Override
    public TrackingNumberAllocationBatch save(TrackingNumberAllocationBatch batch) {
        TrackingNumberAllocationBatchPO batchPO = converter.toPO(batch);
        saveOrUpdate(batchPO);
        return converter.toEntity(batchPO);
    }

    @Override
    public TrackingNumberAllocationBatch findById(Long id) {
        TrackingNumberAllocationBatchPO batchPO = getById(id);
        return converter.toEntity(batchPO);
    }

    @Override
    public PageResult<TrackingNumberAllocationBatch> pageQuery(Long allocatorId,
                                                             Long requestedLocationId,
                                                             Long requestedShipmentTypeId,
                                                             LocalDateTime startTime,
                                                             LocalDateTime endTime,
                                                             int pageNo,
                                                             int pageSize) {
        Page<TrackingNumberAllocationBatchPO> page = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<TrackingNumberAllocationBatchPO> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.eq(allocatorId != null, TrackingNumberAllocationBatchPO::getAllocatorId, allocatorId)
                    .eq(requestedLocationId != null, TrackingNumberAllocationBatchPO::getRequestedLocationId, requestedLocationId)
                    .eq(requestedShipmentTypeId != null, TrackingNumberAllocationBatchPO::getRequestedShipmentTypeId, requestedShipmentTypeId)
                    .ge(startTime != null, TrackingNumberAllocationBatchPO::getAllocationTime, startTime)
                    .le(endTime != null, TrackingNumberAllocationBatchPO::getAllocationTime, endTime)
                    .orderByDesc(TrackingNumberAllocationBatchPO::getAllocationTime); // Order by allocation time

        Page<TrackingNumberAllocationBatchPO> poPage = page(page, queryWrapper);

        List<TrackingNumberAllocationBatchPO> records = poPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return PageResult.empty(poPage.getTotal());
        }

        List<TrackingNumberAllocationBatch> entityList = converter.toEntityList(records);
        return new PageResult<>(entityList, poPage.getTotal());
    }

    @Override
    public int updateExportedStatus(Long id, boolean isExported) {
        if (id == null) return 0;
        LambdaUpdateWrapper<TrackingNumberAllocationBatchPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(TrackingNumberAllocationBatchPO::getId, id)
                     .set(TrackingNumberAllocationBatchPO::getIsExported, isExported);
        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public int updatePrintedStatus(Long id, boolean isPrinted) {
        if (id == null) return 0;
        LambdaUpdateWrapper<TrackingNumberAllocationBatchPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(TrackingNumberAllocationBatchPO::getId, id)
                     .set(TrackingNumberAllocationBatchPO::getIsPrinted, isPrinted);
        return baseMapper.update(null, updateWrapper);
    }
} 