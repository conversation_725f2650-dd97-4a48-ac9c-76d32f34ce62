# 问题运单工单功能说明

## 功能概述

当物流轨迹出现异常时，系统会自动创建问题运单工单，便于客服人员及时跟进处理问题运单，提高客户满意度和运营效率。

## 主要实现内容

### 1. 数据库设计

创建`problem_manifest_tickets`表，用于存储问题运单工单信息。主要字段包括：

- `id`: 工单主键 ID
- `manifest_id`: 关联的运单 ID
- `tracking_number`: 关联运单的物流单号（冗余字段）
- `customer_account_id`: 关联运单所属的客户 ID（冗余字段）
- `problem_type_code`: 问题类型代码
- `problem_description`: 问题描述
- `status`: 工单处理状态
- `priority`: 问题优先级
- `assigned_to_user_id`: 处理人 ID
- `remarks`: 处理备注
- `create_time`: 创建时间
- `update_time`: 更新时间
- `resolved_time`: 解决时间

### 2. 实体类设计

- `ProblemManifestTicket`: 问题运单工单领域实体
- `ProblemManifestTicketPO`: 持久化对象
- `ProblemManifestTicketRepository`: 仓库接口
- `ProblemManifestTicketRepositoryImpl`: 仓库实现类
- `ProblemManifestTicketMapper`: Mapper 接口

### 3. 业务逻辑实现

在`TrackingOrderStatusJob`中，当检测到物流轨迹异常时：

1. 检查该运单是否已存在未解决的工单（避免重复创建）
2. 如不存在，创建新的问题运单工单，设置为"待处理"状态
3. 记录问题类型、描述、物流单号及相关信息

### 4. 冗余字段设计

添加了两个冗余字段，提高查询效率和便于数据分析：

- `tracking_number`: 冗余存储运单号，方便直接根据运单号查询问题工单
- `customer_account_id`: 冗余存储客户 ID，便于查询和统计某客户的问题工单情况

## 问题类型

系统预设了以下问题类型：

- `TRACKING_EXCEPTION`: 轨迹异常
- `VALIDATION_ERROR`: 验证错误
- `DELIVERY_DELAY`: 配送延迟

## 工单状态流转

工单状态包括：

- `PENDING_ACTION`: 待处理
- `IN_PROGRESS`: 处理中
- `RESOLVED`: 已解决
- `CLOSED_UNRESOLVED`: 关闭（未解决）

## 后续扩展

1. 工单管理界面
2. 工单分配功能
3. 工单处理流程
4. 客户通知机制
5. 统计分析报表
