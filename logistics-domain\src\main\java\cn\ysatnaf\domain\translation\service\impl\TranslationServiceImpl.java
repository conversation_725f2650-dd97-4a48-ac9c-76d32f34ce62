package cn.ysatnaf.domain.translation.service.impl;

import cn.ysatnaf.domain.translation.service.TranslationService;
import cn.ysatnaf.types.util.TranslationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class TranslationServiceImpl implements TranslationService {

    private final TranslationUtil translationUtil;

    @Override
    public String translateFromChineseToEnglish(String itemChineseName) {

        return translationUtil.translateFromChineseToEnglish(itemChineseName);
    }
}
