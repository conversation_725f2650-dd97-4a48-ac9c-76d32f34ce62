package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.infrastructure.persistent.dataobject.MasterBillDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 主提单数据库实体Mapper接口
 * <AUTHOR>
 */
public interface MasterBillDOMapper extends BaseMapper<MasterBillDO> {
    
    /**
     * 分页查询主提单
     * @param page 分页参数
     * @param masterBillNumber 提单号（支持模糊查询）
     * @param departureDateStart 起飞日期起始
     * @param departureDateEnd 起飞日期截止
     * @param arrivalDateStart 到达日期起始
     * @param arrivalDateEnd 到达日期截止
     * @param origin 始发地
     * @param destination 目的地
     * @param carrierCode 承运商代码
     * @param status 提单状态
     * @return 分页结果
     */
    IPage<MasterBillDO> selectPage(
            Page<MasterBillDO> page,
            @Param("masterBillNumber") String masterBillNumber,
            @Param("departureDateStart") LocalDateTime departureDateStart,
            @Param("departureDateEnd") LocalDateTime departureDateEnd,
            @Param("arrivalDateStart") LocalDateTime arrivalDateStart,
            @Param("arrivalDateEnd") LocalDateTime arrivalDateEnd,
            @Param("origin") String origin,
            @Param("destination") String destination,
            @Param("carrierCode") String carrierCode,
            @Param("status") Integer status
    );
    
    /**
     * 更新主提单的统计信息
     * @param id 主提单ID
     * @return 更新的记录数
     */
    int updateStatistics(@Param("id") Long id);
} 