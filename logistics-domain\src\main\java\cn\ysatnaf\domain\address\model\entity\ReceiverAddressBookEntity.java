package cn.ysatnaf.domain.address.model.entity;

import lombok.Data;

/**
 * ReceiverAddressBookEntity
 *
 * <AUTHOR>
 * @date 2023/12/22 15:23
 */
@Data
public class ReceiverAddressBookEntity {

    private Long id;

    private Long receiverAreaId;

    private String openid;
    /**
     * 日本都道府县级名称
     */
    private String prefectureName;
    /**
     * 日本市区町村级名称
     */
    private String municipalName;
    /**
     * 日本丁目级名称
     */
    private String localitiesName;

    /**
     * 日本都道府县级名称(英文)
     */
    private String prefectureEnName;
    /**
     * 日本市区町村级名称(英文)
     */
    private String municipalEnName;
    /**
     * 日本丁目级名称(英文)
     */
    private String localitiesEnName;

    private String addressDetail;

    private String name;

    private String phone;

    private Boolean isDefault;

}
