package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.parcelsorting.model.entity.ParcelSortingRecord;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingRecordPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 装箱记录转换器
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ParcelSortingRecordConverter {

    ParcelSortingRecordConverter INSTANCE = Mappers.getMapper(ParcelSortingRecordConverter.class);

    /**
     * 领域实体转换为持久化对象
     * @param parcelSortingRecord 领域实体
     * @return 持久化对象
     */
    ParcelSortingRecordPO toPo(ParcelSortingRecord parcelSortingRecord);

    /**
     * 持久化对象转换为领域实体
     * @param parcelSortingRecordPO 持久化对象
     * @return 领域实体
     */
    ParcelSortingRecord toEntity(ParcelSortingRecordPO parcelSortingRecordPO);

} 