package cn.ysatnaf.types.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * FullWidthToHalfWidthUtil
 *
 * <AUTHOR> Hang
 * @date 2024/3/8 16:03
 */
public class StringUtil {
    /**
     * 将全角字符转换为半角字符
     *
     * @param fullWidthStr 全角字符串
     * @return 转换后的半角字符串
     */
    public static String toHalfWidth(String fullWidthStr) {
        if (fullWidthStr == null || fullWidthStr.isEmpty()) {
            return fullWidthStr;
        }

        char[] charArray = fullWidthStr.toCharArray();

        // 遍历字符串的每个字符
        for (int i = 0; i < charArray.length; i++) {
            // 如果是全角空格
            if (charArray[i] == 0x3000) {
                charArray[i] = 0x0020;
            }
            // 如果是其他全角字符（全角字符 - 0xFEE0 = 半角字符）
            else if (charArray[i] >= 0xFF01 && charArray[i] <= 0xFF5E) {
                charArray[i] -= 0xFEE0;
            }
        }

        return new String(charArray);
    }

    public static String extractNumbersAndHyphens(String input) {
        input = toHalfWidth(input);
        // 如果包含类似“西旗町 6-6 ,唐津市,,佐賀県,JAPAN,田中 明憲”的，先去掉“,JAPAN”及后面的内容
        input = input.replaceAll("[^\\d\\w&]+JAPAN.*", "");
        // 正则表达式匹配数字、英文字母和&
        Pattern pattern = Pattern.compile("[a-zA-Z0-9&]+");
        Matcher matcher = pattern.matcher(input);

        StringBuilder cleaned = new StringBuilder();
        boolean lastWasDash = true; // 用于跟踪上一个字符是否是'-'

        while (matcher.find()) {
            if (!lastWasDash) {
                cleaned.append('-');
            }
            cleaned.append(matcher.group());
            lastWasDash = false;
        }

        // 如果结果以'-'开始，则移除开头的'-'
        if (cleaned.length() > 0 && cleaned.charAt(0) == '-') {
            cleaned.deleteCharAt(0);
        }


        return cleaned.toString();
    }
}
