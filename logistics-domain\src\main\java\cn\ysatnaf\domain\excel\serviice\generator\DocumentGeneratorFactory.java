package cn.ysatnaf.domain.excel.serviice.generator;

import cn.ysatnaf.domain.manifest.model.valobj.Destination;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@RequiredArgsConstructor
public class DocumentGeneratorFactory {

    private final Map<CustomsDocumentType, JapanCustomsDocumentGenerator> generators = new ConcurrentHashMap<>();

    @Autowired
    public DocumentGeneratorFactory(
            TokyoCustomsDocumentGenerator tokyoCustomsDocumentGenerator,
            OsakaCustomsDocumentGenerator osakaCustomsDocumentGenerator) {
        generators.put(CustomsDocumentType.TOKYO, tokyoCustomsDocumentGenerator);
        generators.put(CustomsDocumentType.OSAKA, osakaCustomsDocumentGenerator);
    }

    public JapanCustomsDocumentGenerator getGeneratorByDestination(Destination destination) {
        CustomsDocumentType type = CustomsDocumentType.getByValue(destination.getCode());
        JapanCustomsDocumentGenerator japanCustomsDocumentGenerator = generators.get(type);
        if (japanCustomsDocumentGenerator == null) {
            throw new RuntimeException("找不到对应的生成器");
        }
        return japanCustomsDocumentGenerator;
    }
}
