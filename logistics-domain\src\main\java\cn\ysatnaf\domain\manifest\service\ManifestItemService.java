package cn.ysatnaf.domain.manifest.service;

import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
public interface ManifestItemService {
    void insertBatch(List<ManifestItem> manifestItems);

    void deleteByManifestId(Long manifestId);

    List<ManifestItem> listByManifestId(Long manifestId);

    List<ManifestItem> listBatchByManifestIds(Collection<Long> manifestIds);

    void deleteByManifestIds(List<Long> deleteItemManifestIds);
}
