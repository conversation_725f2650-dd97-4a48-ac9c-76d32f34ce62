package cn.ysatnaf.trigger.http;

import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordAddReq;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordDTO;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordPageReq;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordUpdateReq;
import cn.ysatnaf.domain.item.service.ProhibitedItemKeywordService;
import cn.ysatnaf.types.common.CommonResult; // 假设统一响应结果类
import cn.ysatnaf.types.common.PageResult; // 假设分页结果类
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 违禁关键词管理 API
 */
@Tag(name = "管理端 - 违禁关键词管理")
@RestController
@RequestMapping("/api/prohibited-keywords") // 假设管理端路径前缀
@RequiredArgsConstructor
@Validated // 开启方法级别校验
public class ProhibitedItemKeywordController {

    private final ProhibitedItemKeywordService prohibitedItemKeywordService;

    @PostMapping
    @Operation(summary = "添加违禁关键词")
    // @PreAuthorize("@ss.hasPermi('item:keyword:add')") // 如果有权限控制
    public CommonResult<Long> addKeyword(@Valid @RequestBody ProhibitedItemKeywordAddReq req) {
        Long id = prohibitedItemKeywordService.addKeyword(req);
        return CommonResult.success(id);
    }

    @PutMapping
    @Operation(summary = "更新违禁关键词")
    // @PreAuthorize("@ss.hasPermi('item:keyword:update')")
    public CommonResult<Boolean> updateKeyword(@Valid @RequestBody ProhibitedItemKeywordUpdateReq req) {
        boolean success = prohibitedItemKeywordService.updateKeyword(req);
        return CommonResult.success(success);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除违禁关键词")
    @Parameter(name = "id", description = "关键词ID", required = true, example = "1")
    // @PreAuthorize("@ss.hasPermi('item:keyword:delete')")
    public CommonResult<Boolean> deleteKeyword(@PathVariable("id") Long id) {
        boolean success = prohibitedItemKeywordService.deleteKeyword(id);
        return CommonResult.success(success);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取违禁关键词详情")
    @Parameter(name = "id", description = "关键词ID", required = true, example = "1")
    // @PreAuthorize("@ss.hasPermi('item:keyword:query')")
    public CommonResult<ProhibitedItemKeywordDTO> getKeywordById(@PathVariable("id") Long id) {
        ProhibitedItemKeywordDTO dto = prohibitedItemKeywordService.getKeywordById(id);
        return CommonResult.success(dto);
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询违禁关键词")
    // @PreAuthorize("@ss.hasPermi('item:keyword:list')")
    public CommonResult<PageResult<ProhibitedItemKeywordDTO>> pageKeywords(@Valid ProhibitedItemKeywordPageReq req) {
        PageResult<ProhibitedItemKeywordDTO> pageResult = prohibitedItemKeywordService.pageKeywords(req);
        return CommonResult.success(pageResult);
    }

     // 注意：listActiveKeywords 和 checkItemName 通常由后端内部调用，
     // 不一定需要暴露为API。如果需要，可以取消注释或单独创建API。
    /*
    @GetMapping("/active-list")
    @Operation(summary = "获取所有启用的违禁关键词列表")
    public CommonResult<List<String>> listActiveKeywords() {
        List<String> keywords = prohibitedItemKeywordService.listActiveKeywords();
        return CommonResult.success(keywords);
    }
    */

    @GetMapping("/check")
    @Operation(summary = "检查物品名称是否包含违禁词")
    @Parameter(name = "itemName", description = "要检查的物品名称", required = true)
    public CommonResult<String> checkItemName(@RequestParam("itemName") String itemName) {
        String prohibitedKeyword = prohibitedItemKeywordService.checkItemName(itemName);
        // 根据需要决定返回格式，这里返回第一个匹配到的词或null
        return CommonResult.success(prohibitedKeyword);
    }

} 