package cn.ysatnaf.infrastructure.config;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
 
public class UpdateBatchMethod extends AbstractMethod {
    protected UpdateBatchMethod(String methodName) {
        super(methodName);
    }

    /**
      update user set
        name=
        (CASE
                WHEN id=1 THEN '张三'
                WHEN id=2 THEN '李四'
        end),
        age =
        (CASE
                WHEN id=1 THEN '2'
                WHEN id=2 THEN '2'
        end) where id in (1,2);
       <script>
       update user 
       <trim prefix="set" suffixOverrides=",">
        <trim prefix="name =(case" suffix="end),">
          <foreach collection="list" item="item" >
            <if test="item.name!=null">
              when id=#{item.id} then #{item.name}
            </if>
          </foreach>
          else name
        </trim>
        <trim prefix="age =(case" suffix="end),">
          <foreach collection="list" item="item" >
            <if test="item.age!=null">
              when id=#{item.id} then #{item.age}
            </if>
          </foreach>
          else age</trim>
        </trim>
        where id in 
         <foreach collection="list" item="item" separator="," open="(" close=")">
             #{item.id} 
         </foreach> 
       </script>
     */
    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        final String sql = "<script>\n update %s %s \n where id in \n <foreach collection=\"list\" item=\"item\" separator=\",\" open=\"(\" close=\")\">\n #{item.id} </foreach> \n </script>";
        final String valueSql = prepareValuesSql(tableInfo);
        final String sqlResult = String.format(sql, tableInfo.getTableName(), valueSql);
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sqlResult, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, "updateBatchById", sqlSource);
    }
 
    private String prepareValuesSql(TableInfo tableInfo) {
        final StringBuilder valueSql = new StringBuilder();
        valueSql.append("<trim prefix=\"set\" suffixOverrides=\",\">\n");
        tableInfo.getFieldList().forEach(x -> {
            valueSql.append("<trim prefix=\"").append(x.getColumn()).append(" =(case \" suffix=\"end),\">\n");
            valueSql.append("<foreach collection=\"list\" item=\"item\" >\n");
            valueSql.append("when id=#{item.id} then ifnull(#{item.").append(x.getProperty()).append("},").append(x.getColumn()).append(")\n");
            valueSql.append("</foreach>\n");
            valueSql.append("else ").append(x.getColumn());
            valueSql.append("</trim>\n");
        });
        valueSql.append("</trim>\n");
        return valueSql.toString();
    }
}