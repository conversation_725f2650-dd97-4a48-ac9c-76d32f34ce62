package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.site.model.po.SiteCodePO;
import cn.ysatnaf.domain.site.repository.SiteCodeRepository;
import cn.ysatnaf.infrastructure.persistent.dao.SiteCodeDao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class SiteCodeRepositoryImpl implements SiteCodeRepository {
    private final SiteCodeDao siteCodeDao;

    @Override
    public SiteCodePO getByZipCodeAndType(String receiverZipCode, int siteType) {
        LambdaQueryWrapper<SiteCodePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SiteCodePO::getZipCode, receiverZipCode);
        wrapper.eq(SiteCodePO::getSiteType, siteType);
        return siteCodeDao.selectOne(wrapper);
    }
}
