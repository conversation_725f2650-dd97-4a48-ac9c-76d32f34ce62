package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.ysatnaf.domain.fund.model.entity.BalanceDetailEntity;
import cn.ysatnaf.domain.fund.repository.BalanceDetailRepository;
import cn.ysatnaf.infrastructure.persistent.converter.BalanceDetailConverter;
import cn.ysatnaf.infrastructure.persistent.dao.BalanceDetailDao;
import cn.ysatnaf.infrastructure.persistent.po.BalanceDetailPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class BalanceDetailRepositoryImpl implements BalanceDetailRepository {
    
    private final BalanceDetailDao balanceDetailDao;

    @Override
    public PageResult<BalanceDetailEntity> pageByAccountId(Long accountId, Integer pageNo, Integer pageSize) {

        LambdaQueryWrapper<BalanceDetailPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BalanceDetailPO::getAccountId, accountId);
        wrapper.orderByDesc(BalanceDetailPO::getCreateTime);
        Page<BalanceDetailPO> balanceDetailPOPage = balanceDetailDao.selectPage(new Page<>(pageNo, pageSize), wrapper);
        if (CollUtil.isEmpty(balanceDetailPOPage.getRecords())) {
            return PageResult.empty(balanceDetailPOPage.getTotal());
        }
        List<BalanceDetailEntity> balanceDetailEntities = BalanceDetailConverter.INSTANCE.toEntityList(balanceDetailPOPage.getRecords());
        return new PageResult<>(balanceDetailEntities, balanceDetailPOPage.getTotal());
    }

    @Override
    public void insert(BalanceDetailEntity balanceDetailEntity) {
        balanceDetailDao.insert(BalanceDetailConverter.INSTANCE.toPO(balanceDetailEntity));
    }

    @Override
    public List<BalanceDetailEntity> listByAccountId(Long accountId, Date startTime, Date endTime) {
        LambdaQueryWrapper<BalanceDetailPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BalanceDetailPO::getAccountId, accountId);
        wrapper.ge(ObjUtil.isNotNull(startTime), BalanceDetailPO::getCreateTime, startTime);
        wrapper.le(ObjUtil.isNotNull(endTime), BalanceDetailPO::getCreateTime, endTime);
        List<BalanceDetailPO> balanceDetailPOS = balanceDetailDao.selectList(wrapper);
        return BalanceDetailConverter.INSTANCE.toEntityList(balanceDetailPOS);
    }
}
