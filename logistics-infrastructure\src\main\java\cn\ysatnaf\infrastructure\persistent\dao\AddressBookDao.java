package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.domain.address.model.entity.AddressBookEntity;
import cn.ysatnaf.infrastructure.persistent.po.AddressBookPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AddressBookDao
 *
 * <AUTHOR>
 * @date 2023/12/22 10:48
 */
@Mapper
public interface AddressBookDao extends BaseMapper<AddressBookPO> {
    void updateBatchById(@Param("list") List<AddressBookPO> list);

}
