package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingRecordPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface ParcelSortingRecordDao extends BaseMapper<ParcelSortingRecordPO> {

    /**
     * 增加指定装箱记录的已装箱件数
     * @param recordId 记录ID
     * @param count 增加的数量
     * @return 影响的行数
     */
    int incrementItemsPackedCount(@Param("recordId") Long recordId, @Param("count") int count);

}
