package cn.ysatnaf.domain.manifest.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 提单状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MasterBillStatus {

    /**
     * 0-全部
     * 1-待起飞
     * 2-运输中
     * 3-已到达
     * 4-已完成
     */
    ALL(0, "全部"),
    PENDING_DEPARTURE(1, "待起飞"),
    IN_TRANSIT(2, "运输中"),
    ARRIVED(3, "已到达"),
    COMPLETED(4, "已完成");

    private final Integer code;
    private final String message;

    /**
     * 根据状态码获取状态枚举
     * @param code 状态码
     * @return 状态枚举
     */
    public static MasterBillStatus getByCode(Integer code) {
        for (MasterBillStatus status : MasterBillStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
} 