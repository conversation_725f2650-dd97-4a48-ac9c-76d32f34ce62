package cn.ysatnaf.domain.excel.serviice;

import cn.ysatnaf.domain.manifest.model.aggregate.ManifestAggregate;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ExcelGenerationService
 *
 * <AUTHOR>
 * @date 2024/3/25 14:40
 */
public interface ChineseCustomsDocumentGenerationService {

    void generate(String packageNumber, List<ManifestAggregate> manifestAggregates, HttpServletResponse httpServletResponse);
    void generate(List<ManifestAggregate> manifestAggregates, String recordName, HttpServletResponse httpServletResponse);
}
