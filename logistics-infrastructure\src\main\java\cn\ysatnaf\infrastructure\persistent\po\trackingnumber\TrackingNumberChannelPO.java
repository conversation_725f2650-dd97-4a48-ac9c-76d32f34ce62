package cn.ysatnaf.infrastructure.persistent.po.trackingnumber;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 运单号渠道定义持久化对象
 * <AUTHOR>
 */
@Data
@TableName("tracking_number_channels")
public class TrackingNumberChannelPO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private Long carrierId;
    private Long locationId;
    private Long shipmentTypeId;
    private String channelCode;
    private String channelName;
    private String description;
    private Boolean isActive; // 对应 is_active
    private Long creatorId;
    private Long updaterId;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

} 