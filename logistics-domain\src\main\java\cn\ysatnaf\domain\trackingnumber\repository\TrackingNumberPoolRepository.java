package cn.ysatnaf.domain.trackingnumber.repository;

import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberPool;
import cn.ysatnaf.domain.trackingnumber.model.valobj.TrackingNumberStatus;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * 预报单号池仓库接口
 * <AUTHOR>
 */
public interface TrackingNumberPoolRepository {

    /**
     * 批量保存单号
     * @param trackingNumbers 单号实体列表
     * @return 成功插入的数量
     */
    int saveBatch(List<TrackingNumberPool> trackingNumbers);

    /**
     * 检查指定渠道的单号是否存在
     * @param channelId 渠道ID
     * @param trackingNumber 单号
     * @return 如果存在则返回true，否则返回false
     */
    boolean exists(Long channelId, String trackingNumber);

    /**
     * 查询指定渠道下可用单号的数量
     * @param channelId 渠道ID
     * @return 可用单号数量
     */
    long countAvailableByChannel(Long channelId);

    /**
     * 批量查询指定渠道列表的可用单号数量
     * @param channelIds 渠道ID集合
     * @return Map<ChannelId, AvailableCount>
     */
    Map<Long, Long> countAvailableByChannelIds(Collection<Long> channelIds);

    /**
     * 查询指定渠道下指定数量的可用单号
     * @param channelId 渠道ID
     * @param limit 查询数量
     * @return 可用单号实体列表
     */
    List<TrackingNumberPool> findAvailableByChannel(Long channelId, int limit);

    /**
     * 根据ID批量更新单号状态和分配批次ID
     * @param ids 要更新的单号ID列表
     * @param status 新的状态
     * @param allocationBatchId 分配批次ID
     * @return 更新的记录数
     */
    int updateStatusAndAllocationBatchByIds(List<Long> ids, TrackingNumberStatus status, Long allocationBatchId);
    
    /**
     * 根据分配批次ID查询单号列表
     * @param allocationBatchId 分配批次ID
     * @return 单号实体列表
     */
    List<TrackingNumberPool> findByAllocationBatchId(Long allocationBatchId);

    /**
     * 根据渠道ID和单号列表，查询已存在的单号列表
     * @param channelId 渠道ID
     * @param trackingNumbers 单号列表
     * @return 已存在于数据库中的单号列表 (Set for efficient lookup)
     */
    Set<String> findExistingNumbers(@Param("channelId") Long channelId, @Param("trackingNumbers") List<String> trackingNumbers);

    /**
     * 根据单号字符串查询单号池记录
     * 假设一个单号字符串在池中是唯一的，或者返回找到的第一个
     * @param trackingNumber 单号字符串
     * @return Optional 包装的单号池实体
     */
    Optional<TrackingNumberPool> findByTrackingNumber(String trackingNumber);
} 