package cn.ysatnaf.domain.address.repository;

import cn.ysatnaf.domain.address.model.entity.ReceiverAddressBookEntity;
import cn.ysatnaf.types.common.PageResult;

import java.util.List;

/**
 * ReceiverAddressRepository
 *
 * <AUTHOR>
 * @date 2023/12/22 15:34
 */
public interface ReceiverAddressBookRepository {
    void insert(ReceiverAddressBookEntity receiverAddressBookEntity);

    void delete(Long id);

    void update(ReceiverAddressBookEntity receiverAddressBookEntity);

    PageResult<ReceiverAddressBookEntity> page(String openid, Integer pageNo, Integer pageSize);

    ReceiverAddressBookEntity getById(Long receiverAddressBookId);

    ReceiverAddressBookEntity getDefaultByOpenid(String openid);

    List<ReceiverAddressBookEntity> listByOpenid(String openid);

    void updateBatchById(List<ReceiverAddressBookEntity> receiverAddressBookEntityList);
}
