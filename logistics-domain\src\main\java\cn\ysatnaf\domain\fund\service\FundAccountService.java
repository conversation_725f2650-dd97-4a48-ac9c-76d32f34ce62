package cn.ysatnaf.domain.fund.service;

import cn.ysatnaf.domain.fund.model.dto.ModifyBalanceDTO;
import cn.ysatnaf.domain.fund.model.entity.FundAccountEntity;
import cn.ysatnaf.domain.fund.model.req.FundAccountGetReq;
import cn.ysatnaf.domain.fund.model.req.FundAccountRechargeReq;
import cn.ysatnaf.domain.fund.model.req.FundAccountUpdateReq;
import cn.ysatnaf.domain.fund.model.res.FundAccountGetRes;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
public interface FundAccountService {

    FundAccountGetRes get(FundAccountGetReq req);

    FundAccountGetRes getByUserId(Long userId);

    void init(Long userId);

    void deleteByUserId(Long userId);

    Boolean cost(Long userId, BigDecimal cost, Long id);

    Boolean change(ModifyBalanceDTO modifyBalanceDTO);

    Boolean recharge(FundAccountRechargeReq req);

    List<FundAccountEntity> listByUserIds(List<Long> userIds);
}
