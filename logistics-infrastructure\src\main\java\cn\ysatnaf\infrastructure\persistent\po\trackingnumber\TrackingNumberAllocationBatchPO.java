package cn.ysatnaf.infrastructure.persistent.po.trackingnumber;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 单号分配批次记录持久化对象
 * <AUTHOR>
 */
@Data
@TableName("tracking_number_allocation_batch")
public class TrackingNumberAllocationBatchPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分配操作员ID
     */
    private Long allocatorId;

    /**
     * 请求分配的地点ID (FK -> locations.id)
     */
    private Long requestedLocationId;

    /**
     * 请求分配的货物类型ID (FK -> shipment_types.id)
     */
    private Long requestedShipmentTypeId;

    /**
     * 分配目标客户账户ID (对应 customer_account_id 列)
     */
    private Long customerAccountId;

    /**
     * 请求分配的数量
     */
    private Integer quantityRequested;

    /**
     * 实际分配的数量
     */
    private Integer quantityAllocated;

    /**
     * 分配时间
     */
    private LocalDateTime allocationTime;

    /**
     * 创建时间
     */
    @TableField(fill = com.baomidou.mybatisplus.annotation.FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = com.baomidou.mybatisplus.annotation.FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否已导出 (0:否, 1:是)
     */
    private Boolean isExported;

    /**
     * 是否已打印 (0:否, 1:是)
     */
    private Boolean isPrinted;
} 