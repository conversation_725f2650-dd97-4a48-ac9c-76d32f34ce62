package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.fund.model.entity.BalanceDetailEntity;
import cn.ysatnaf.infrastructure.persistent.po.BalanceDetailPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Mapper
public interface BalanceDetailConverter {
    BalanceDetailConverter INSTANCE = Mappers.getMapper(BalanceDetailConverter.class);

    BalanceDetailPO toPO(BalanceDetailEntity entity);

    BalanceDetailEntity toEntity(BalanceDetailPO po);

    List<BalanceDetailEntity> toEntityList(List<BalanceDetailPO> poList);
}
