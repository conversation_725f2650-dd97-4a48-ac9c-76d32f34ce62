package cn.ysatnaf.domain.manifest.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "上传物流文件返回参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UploadManifestRes {
    @Schema(description = "上传成功数量")
    private Integer successCount;

    @Schema(description = "上传失败数量")
    private Integer failCount;

    @Schema(description = "错误信息，仅当success为false的时候有内容")
    private List<String> errors;
}
