package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.address.model.entity.ReceiverAddressBookEntity;
import cn.ysatnaf.domain.address.repository.ReceiverAddressBookRepository;
import cn.ysatnaf.infrastructure.persistent.converter.ReceiverAddressBookConverter;
import cn.ysatnaf.infrastructure.persistent.dao.ReceiverAddressBookDao;
import cn.ysatnaf.infrastructure.persistent.po.ReceiverAddressBookPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * ReceiverAddressRepositoryImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 15:43
 */
@Repository
@RequiredArgsConstructor
public class ReceiverAddressBookRepositoryImpl implements ReceiverAddressBookRepository {

    private final ReceiverAddressBookDao receiverAddressBookDao;

    @Override
    public void insert(ReceiverAddressBookEntity receiverAddressBookEntity) {
        receiverAddressBookDao.insert(ReceiverAddressBookConverter.INSTANCE.entity2po(receiverAddressBookEntity));
    }

    @Override
    public void delete(Long id) {
        receiverAddressBookDao.deleteById(id);
    }

    @Override
    public void update(ReceiverAddressBookEntity receiverAddressBookEntity) {
        receiverAddressBookDao.updateById(ReceiverAddressBookConverter.INSTANCE.entity2po(receiverAddressBookEntity));
    }

    @Override
    public PageResult<ReceiverAddressBookEntity> page(String openid, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<ReceiverAddressBookPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReceiverAddressBookPO::getOpenid, openid);
        wrapper.orderByDesc(ReceiverAddressBookPO::getIsDefault);
        Page<ReceiverAddressBookPO> addressBookPOPage = receiverAddressBookDao.selectPage(new Page<>(pageNo, pageSize), wrapper);
        if (addressBookPOPage.getSize() == 0) {
            return PageResult.empty(addressBookPOPage.getTotal());
        }
        List<ReceiverAddressBookEntity> records = addressBookPOPage
                .getRecords().stream().map(ReceiverAddressBookConverter.INSTANCE::po2entity)
                .collect(Collectors.toList());
        return new PageResult<>(records, addressBookPOPage.getTotal());
    }

    @Override
    public ReceiverAddressBookEntity getById(Long id) {
        return ReceiverAddressBookConverter.INSTANCE.po2entity(receiverAddressBookDao.selectById(id));
    }

    @Override
    public ReceiverAddressBookEntity getDefaultByOpenid(String openid) {
        LambdaQueryWrapper<ReceiverAddressBookPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReceiverAddressBookPO::getOpenid, openid);
        wrapper.eq(ReceiverAddressBookPO::getIsDefault, true);
        return ReceiverAddressBookConverter.INSTANCE.po2entity(receiverAddressBookDao.selectOne(wrapper));
    }

    @Override
    public List<ReceiverAddressBookEntity> listByOpenid(String openid) {
        LambdaQueryWrapper<ReceiverAddressBookPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReceiverAddressBookPO::getOpenid, openid);
        return ReceiverAddressBookConverter.INSTANCE.po2entityList(receiverAddressBookDao.selectList(wrapper));
    }

    @Override
    public void updateBatchById(List<ReceiverAddressBookEntity> receiverAddressBookEntityList) {
        receiverAddressBookDao.updateBatchById(ReceiverAddressBookConverter.INSTANCE.entity2poList(receiverAddressBookEntityList));
    }
}
