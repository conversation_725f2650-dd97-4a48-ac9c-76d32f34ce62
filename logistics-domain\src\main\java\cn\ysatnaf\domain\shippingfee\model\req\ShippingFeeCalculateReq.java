package cn.ysatnaf.domain.shippingfee.model.req;

import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "计算运费入参")
@Data
public class ShippingFeeCalculateReq {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "运费模板ID，管理员操作时可传")
    private Long templateId;

    /**
     * {@link ShippingFeeTemplateTypeEnum#getCode()}
     */
    @Schema(description = "运费模板类型：1-普通模板；2-带电模板；3-投函模板")
    private Integer type;

    @Schema(description = "重量（单位：千克）")
    private BigDecimal weight;

    @Schema(description = "长（单位：厘米")
    private BigDecimal length;

    @Schema(description = "宽（单位：厘米")
    private BigDecimal width;

    @Schema(description = "高（单位：厘米")
    private BigDecimal height;
}
