package cn.ysatnaf.domain.statistics.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class TemplateDailyVO {
    private String templateType;  // 模板类型（normal/battery/letter）
    private Long quantity;        // 数量
    private BigDecimal amount;    // 金额
    private BigDecimal quantityRate; // 数量增长率
    private BigDecimal amountRate;   // 金额增长率
}