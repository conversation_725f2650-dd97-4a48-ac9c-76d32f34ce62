package cn.ysatnaf.domain.manifest.model.req;

import cn.ysatnaf.domain.manifest.model.dto.ManifestItemOrderDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Data
public class ManifestAddReq {

    @Schema(description = "快递单号")
    @NotBlank(message = "快递单号不能为空")
    private String expressNumber;

    @Schema(description = "商店订单号（客户自定）")
    @NotBlank(message = "商店订单号不能为空")
    @Length(max = 64, message = "商品订单号长度不能超过64位")
    private String orderNumber;

    @Schema(description = "邮编")
    @NotBlank(message = "邮编不能为空")
    private String zipCode;

    @Schema(description = "收件人")
    @NotBlank(message = "收件人不能为空")
    private String receiverName;

    @Schema(description = "收件地址")
    @NotBlank(message = "收件地址不能为空")
    private String receiverAddress;

    @Schema(description = "收件人电话")
    @NotBlank(message = "收件人电话不能为空")
    private String receiverPhone;

    @Schema(description = "物品列表")
    @NotEmpty(message = "物品列表不能为空")
    @Valid
    private List<ManifestItemOrderDTO> items;

    @Schema(description = "运单所属用户ID，只有当操作者为管理员时必填，表示帮助其他用户导入")
    private Long userId;

}
