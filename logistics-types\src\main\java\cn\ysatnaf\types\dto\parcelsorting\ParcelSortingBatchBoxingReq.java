package cn.ysatnaf.types.dto.parcelsorting;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 批量装箱请求 DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "批量装箱请求")
public class ParcelSortingBatchBoxingReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "箱子ID", required = true)
    @NotNull(message = "箱子ID不能为空")
    private Long boxId;

    @Schema(description = "包裹列表", required = true)
    @NotEmpty(message = "包裹列表不能为空")
    @Valid
    private List<PackageItem> packages;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "包裹信息")
    public static class PackageItem implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(description = "运单ID")
        private Long manifestId;

        @Schema(description = "订单号")
        private String orderNo;

        @Schema(description = "快递单号")
        private String expressNumber;

        @Schema(description = "收件人姓名")
        private String receiverName;
    }
} 