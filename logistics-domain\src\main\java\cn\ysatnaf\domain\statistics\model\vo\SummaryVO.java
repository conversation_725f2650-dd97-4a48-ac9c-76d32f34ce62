package cn.ysatnaf.domain.statistics.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class SummaryVO {
    // 总数据
    private TotalData total;
    // 本月数据
    private CurrentMonthData currentMonth;

    @Data
    public static class TotalData {
        private Long quantity;
        private BigDecimal amount;
    }

    @Data
    public static class CurrentMonthData {
        private Long quantity;
        private BigDecimal amount;
        private BigDecimal quantityRate;
        private BigDecimal amountRate;
    }
}