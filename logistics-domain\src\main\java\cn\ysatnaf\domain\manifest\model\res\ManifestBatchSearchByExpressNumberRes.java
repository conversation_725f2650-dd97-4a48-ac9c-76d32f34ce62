package cn.ysatnaf.domain.manifest.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * ManifestBatchSearchByExpressNumberRes
 *
 * <AUTHOR> Hang
 * @date 2024/3/20 15:45
 */
@Schema(description = "根据运单号批量追踪订单返回值")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManifestBatchSearchByExpressNumberRes {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "运单号")
    private String expressNumber;

    @Schema(description = "商家订单号")
    private String orderNumber;

    @Schema(description = "系统订单号")
    private String orderNo;

    @Schema(description = "目的地")
    private String receiverAddress;

    @Schema(description = "收件人")
    private String receiverName;

    @Schema(description = "揽件时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pickUpTime;

    @Schema(description = "收件时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deliveredTime;

    @Schema(description = "发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime trackingUpdateTime;

    @Schema(description = "发生地点")
    private String place;

    @Schema(description = "最新状态")
    private String trackingDesc;
}
