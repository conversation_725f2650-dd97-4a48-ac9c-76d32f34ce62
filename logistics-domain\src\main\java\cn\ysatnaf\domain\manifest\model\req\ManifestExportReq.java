package cn.ysatnaf.domain.manifest.model.req;

import cn.ysatnaf.domain.manifest.model.valobj.Destination;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.cglib.core.Local;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "导出运单入参")
@Data
public class ManifestExportReq {

    @Schema(description = "所选ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> ids;

    @Schema(description = "揽件时间范围开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickUpTimeFrom;

    @Schema(description = "揽件时间范围结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickUpTimeTo;

    @Schema(description = "目的地")
    private Destination destination;

    @Schema(description = "运费模板类型")
    private List<Integer> shippingFeeTemplateTypes;

    @Schema(description = "航班日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate flightDate;

    @Schema(description = "航班号", defaultValue = "MF809")
    private String flightNumber = "MF809";

    @Schema(description = "提单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String ladingBill;
}
