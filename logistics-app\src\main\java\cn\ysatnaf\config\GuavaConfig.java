package cn.ysatnaf.config;

import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class GuavaConfig {

    @Bean(name = "receiverAreaCache")
    public Cache<String, List<ReceiverAreaEntity>> cache() {
        return CacheBuilder.newBuilder().build();
    }

}
