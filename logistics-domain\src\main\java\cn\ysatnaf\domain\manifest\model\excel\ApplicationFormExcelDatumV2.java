package cn.ysatnaf.domain.manifest.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationFormExcelDatumV2 {

    @ExcelProperty("序号")
    private Integer number;

    @ExcelProperty("袋号")
    private String packageNumber;

    @ExcelProperty("订单编号")
    private String orderNo;

    @ExcelProperty("服务商单号")
    private String orderNumber;

    @ExcelProperty("收件人姓名")
    private String receiverName;

    @ExcelProperty("收件人地址")
    private String receiverAddress;

    @ExcelProperty("币制")
    private String currencySystem = "人民币";

    @ExcelProperty("重量")
    private BigDecimal weight;

    @ExcelProperty("件数")
    private Integer quantity;

    @ExcelProperty("商品名称中文")
    private String goodsDescription;

    @ExcelProperty("商品名称英文")
    private String goodsDescriptionEn;

    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    @ExcelProperty("总价")
    private BigDecimal totalPrice;

    @ExcelProperty("目的国")
    private String destination;

    @ExcelProperty("方数")
    private BigDecimal squareNumber;

    @ExcelProperty("长(CM)")
    private BigDecimal length;

    @ExcelProperty("宽(CM)")
    private BigDecimal width;

    @ExcelProperty("高(CM)")
    private BigDecimal height;
}
