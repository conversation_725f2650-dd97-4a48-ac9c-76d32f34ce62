package cn.ysatnaf.domain.manifest.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PreReportPreviewType {
    /**
     * 0-全部
     * 1-未重复数据
     * 2-重复数据
     */
    ALL(0, "全部"),
    NOT_PRE_REPORTED(1, "未预报数据"),
    PRE_REPORTED(2, "已预报数据");

    private final Integer code;
    private final String desc;

    public static PreReportPreviewType getUploadPreviewType(Integer code) {
        for (PreReportPreviewType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
