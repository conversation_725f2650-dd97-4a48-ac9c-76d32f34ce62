package cn.ysatnaf.domain.manifest.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Data
public class ManifestUpdateByIdDTO {
    @Schema(description = "运单ID")
    private Long id;

    @Schema(description = "快递单号，如果是网络用户，快递单号为国内快递单号，其他用户快递单号就是佐川单号")
    @NotBlank(message = "快递单号不能为空")
    private String expressNumber;

    @Schema(description = "佐川单号")
    @NotBlank(message = "佐川单号不能为空")
    private String sawagaNumber;

    @Schema(description = "商店订单号（客户自定）")
    @NotBlank(message = "商店订单号不能为空")
    private String orderNumber;

    private String transferredTrackingNumber;

    @Schema(description = "邮编")
    @NotBlank(message = "邮编不能为空")
    private String receiverZipCode;

    @Schema(description = "收件人")
    @NotBlank(message = "收件人不能为空")
    private String receiverName;

    @Schema(description = "收件地址")
    @NotBlank(message = "收件地址不能为空")
    private String receiverAddress;

    @Schema(description = "收件人电话")
    @NotBlank(message = "收件人电话不能为空")
    private String receiverPhone;

    @Schema(description = "长（厘米）, 打印面单第三步填的，如果没有可不传")
    private BigDecimal length;

    @Schema(description = "宽（厘米），打印面单第三步填的，如果没有可不传")
    private BigDecimal width;

    @Schema(description = "高（厘米）, 打印面单第三步填的，如果没有可不传")
    private BigDecimal height;

    @Schema(description = "重量（kg），打印面单第三步填的", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private BigDecimal weight;

    @Schema(description = "体积重量（kg），打印面单第三步通过价格模板计算出来的，如果没有体积则该值为空")
    private BigDecimal dimensionalWeight;

    @Schema(description = "所需费用，打印面单第三步通过价格模板计算出来的")
    @NotNull
    private BigDecimal cost;

    @Schema(description = "超长费")
    private BigDecimal overLengthSurcharge;

    @Schema(description = "偏远费")
    private BigDecimal remoteAreaSurcharge;

    @Schema(description = "其他费用名称")
    private String otherCostName;

    @Schema(description = "其他费用")
    private BigDecimal otherCost;

    @Schema(description = "运费模板类型")
    @NotNull(message = "运费模板类型不能为空")
    private Integer shippingFeeTemplateType;

    @Schema(description = "物品列表")
    @NotEmpty(message = "物品列表不能为空")
    @Valid
    private List<ManifestItemOrderDTO> manifestItems;

    private Integer status;

    /**
     * 揽件时间
     */
    private LocalDateTime pickUpTime;

    private Integer trackingStatus;

    private LocalDateTime trackingUpdateTime;

    private Long masterBillId;

    private String masterBillNumber;
}
