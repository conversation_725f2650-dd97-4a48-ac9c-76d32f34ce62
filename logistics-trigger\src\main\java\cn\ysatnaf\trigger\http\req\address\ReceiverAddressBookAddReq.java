package cn.ysatnaf.trigger.http.req.address;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * AddressBookAddReq
 *
 * <AUTHOR>
 * @date 2023/12/22 10:20
 */
@Schema(description = "新增收件人地址 入参")
@Data
public class ReceiverAddressBookAddReq {

    @Schema(description = "用户OPENID")
    private String openid;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮编")
    private Long receiverAreaId;

    @Schema(description = "详细地址")
    private String addressDetail;

    @Schema(description = "是否默认地址")
    private Boolean isDefault;
}
