package cn.ysatnaf.types.dto.parcelsorting;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 装箱检测请求 DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "装箱检测请求")
public class ParcelSortingCheckReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "系统单号", required = true)
    @NotBlank(message = "系统单号不能为空")
    private String orderNo;

    @Schema(description = "主提单ID", required = true)
    @NotNull(message = "主提单ID不能为空")
    private Long masterBillId;

} 