package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import cn.ysatnaf.infrastructure.persistent.po.ManifestItemPO;
import cn.ysatnaf.infrastructure.persistent.po.ManifestPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ManifestItemConverter {
    ManifestItemConverter INSTANCE = Mappers.getMapper(ManifestItemConverter.class);

    ManifestPO toPO(ManifestItem manifestItem);
    List<ManifestItemPO> toPOList(List<ManifestItem> manifestItems);

    ManifestItem toEntity(ManifestItemPO po);

    List<ManifestItem> toEntityList(List<ManifestItemPO> poList);
}
