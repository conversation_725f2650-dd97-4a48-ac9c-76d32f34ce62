package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;
import cn.ysatnaf.domain.address.repository.ReceiverAreaRepository;
import cn.ysatnaf.infrastructure.persistent.converter.ReceiverAreaConverter;
import cn.ysatnaf.infrastructure.persistent.dao.ReceiverAreaDao;
import cn.ysatnaf.infrastructure.persistent.po.ReceiverAreaPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ReceiverAreaRepositoryImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 14:04
 */
@Repository
@RequiredArgsConstructor
public class ReceiverAreaRepositoryImpl implements ReceiverAreaRepository {

    private final ReceiverAreaDao receiverAreaDao;

    @Override
    public List<ReceiverAreaEntity> getByZipCode(String zipCode) {
        LambdaQueryWrapper<ReceiverAreaPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ReceiverAreaPO::getZipCode, zipCode);
        List<ReceiverAreaPO> areaPOList = receiverAreaDao.selectList(wrapper);
        return ReceiverAreaConverter.INSTANCE.convert(areaPOList);
    }

    @Override
    public List<ReceiverAreaEntity> findAll() {
        return ReceiverAreaConverter.INSTANCE.convert(receiverAreaDao.selectList(null));
    }

    @Override
    public ReceiverAreaEntity getById(Long receiverAreaId) {
        return ReceiverAreaConverter.INSTANCE.convert(receiverAreaDao.selectById(receiverAreaId));
    }
}
