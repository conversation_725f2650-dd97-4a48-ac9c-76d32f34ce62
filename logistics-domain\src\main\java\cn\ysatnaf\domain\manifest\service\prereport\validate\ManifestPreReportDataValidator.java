package cn.ysatnaf.domain.manifest.service.prereport.validate;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import cn.ysatnaf.domain.manifest.service.ManifestDataValidateService;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportContext;
import com.google.common.collect.Sets;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Set;

/**
 * 预报订单数据校验器
 */
@Data
public class ManifestPreReportDataValidator {

    /**
     * 禁止预报邮编的黑名单
     */
    private static final Set<String> BLACK_LIST = Sets.newHashSet("3060608");

    /**
     * 行数据
     */
    private final ManifestPreReportRow rowData;

    /**
     * 错误信息构造器
     */
    private final ErrorMsgBuilder errorMsgBuilder;

    /**
     * 用于校验电话、邮编格式的服务
     */
    private final ManifestDataValidateService manifestDataValidateService;

    public ManifestPreReportDataValidator(ManifestPreReportRow rowData,
                                          Integer rowNumber,
                                          ManifestDataValidateService manifestDataValidateService) {
        this.rowData = rowData;
        this.errorMsgBuilder = new ErrorMsgBuilder(rowNumber);
        this.manifestDataValidateService = manifestDataValidateService;
    }

    /**
     * 校验
     *
     * @param context 整个校验检测过程的上下文对象
     */
    public void validate(ManifestPreReportContext context) {
        // 校验快递单号是否为空
        validateExpressNumber();

        // 校验是否重复上传
        boolean duplicate = validateDuplicate(context);

        // 如果重复了，那么直接结束校验，后续数据不需要再校验了
        if (duplicate) {
            return;
        }

        // 校验商家订单号
        validateMerchantOrderNumber();

        // 校验重量
        validateWeight();

        // 校验数量
        validateQuantity();

        // 校验价值
        validatePrice();

        // 校验邮编
        validateZipCode();

        // 校验收件人
        validateReceiverName();

        // 校验电话
        validateReceiverPhone();

        // 校验物品中文名
        validateItemName();
    }

    /**
     * 校验是否通过
     */
    public boolean isValid() {
        return StrUtil.isBlank(errorMsgBuilder.getErrorMsg());
    }

    /**
     * 校验物品中文名
     */
    private void validateItemName() {
        if (StrUtil.isNotBlank(rowData.getItemName())) {
            return;
        }
        errorMsgBuilder.itemNameEmpty();
    }

    /**
     * 校验收件人电话
     */
    private void validateReceiverPhone() {
        String receiverPhone = rowData.getReceiverPhone();
        // 校验电话是否为空
        if (StrUtil.isBlank(receiverPhone)) {
            errorMsgBuilder.receiverPhoneEmpty();
            return;
        }

        // 校验电话号码并且格式化
        String formattedPhone = manifestDataValidateService.formatAndValidateJpMobile(receiverPhone);
        if (ObjUtil.isNull(formattedPhone)) {
            // 电话号码格式错误
            errorMsgBuilder.receiverPhoneFormatError(receiverPhone);
            return;
        }
        rowData.setReceiverPhone(formattedPhone);
    }

    /**
     * 校验收件人
     */
    private void validateReceiverName() {
        if (StrUtil.isNotBlank(rowData.getReceiverName())) {
            return;
        }
        errorMsgBuilder.receiverNameEmpty();
    }

    /**
     * 校验邮编
     */
    private void validateZipCode() {
        String receiverZipCode = rowData.getReceiverZipCode();
        // 邮编不能为空
        if (receiverZipCode == null) {
            errorMsgBuilder.zipCodeEmpty();
        } else {
            // 格式化邮编，去掉空格和连字符
            receiverZipCode = receiverZipCode.replaceAll(" ", "").replaceAll("-", "");
            rowData.setReceiverZipCode(receiverZipCode);
        }

        // 去掉空格连字符后为空
        if (StrUtil.isBlank(receiverZipCode)) {
            errorMsgBuilder.zipCodeEmpty();
            return;
        }

        // 校验邮编是否在被禁止预报
        if (BLACK_LIST.contains(receiverZipCode)) {
            errorMsgBuilder.zipCodeForbidden(receiverZipCode);
        }

        // 邮编不为空，校验邮编是否存在
        if (!manifestDataValidateService.validateJpZipCode(receiverZipCode)) {
            errorMsgBuilder.zipCodeNotFound(receiverZipCode);
        }
    }

    /**
     * 校验价值
     */
    private void validatePrice() {
        // 价值不能为空
        if (rowData.getPrice() == null) {
            errorMsgBuilder.priceEmpty();
        }
    }

    /**
     * 校验数量
     */
    private void validateQuantity() {
        // 数量为空
        if (rowData.getQuantity() == null) {
            errorMsgBuilder.quantityEmpty();
        }
        // 数量必须大于0
        if (rowData.getQuantity() <= 0) {
            errorMsgBuilder.quantityNotPositive();
        }
    }

    /**
     * 校验重量
     */
    private void validateWeight() {
        // 重量为空
        if (rowData.getWeight() == null) {
            errorMsgBuilder.weightEmpty();
        }
        // 重量必须大于0
        if (rowData.getWeight().compareTo(BigDecimal.ZERO) <= 0) {
            errorMsgBuilder.weightNotPositive();
        }
    }

    /**
     * 校验商家订单号
     */
    private void validateMerchantOrderNumber() {
        if (StrUtil.isNotBlank(rowData.getOrderNumber())) {
            return;
        }
        errorMsgBuilder.merchantOrderNumberEmpty();
    }

    /**
     * 校验是否重复上传
     *
     * @param context 上下文对象
     * @return 是否重复，true为重复，false为不重复
     */
    private boolean validateDuplicate(ManifestPreReportContext context) {
        boolean duplicate = false;
        Set<String> pickedUpExpressNumbers = context.getPickedUpExpressNumbers();
        Set<String> pickedUpOrderNumbers = context.getPickedUpOrderNumbers();
        if (CollUtil.isNotEmpty(pickedUpExpressNumbers) && pickedUpExpressNumbers.contains(rowData.getExpressNumber())) {
            duplicate = true;
            errorMsgBuilder.expressNumberDuplicate(rowData.getExpressNumber());
        }
        if (CollUtil.isNotEmpty(pickedUpOrderNumbers) && pickedUpOrderNumbers.contains(rowData.getOrderNumber())) {
            duplicate = true;
            errorMsgBuilder.orderDuplicate(rowData.getOrderNumber());
        }
        return duplicate;
    }

    /**
     * 校验快递单号是否为空
     */
    private void validateExpressNumber() {
        if (StrUtil.isNotBlank(rowData.getExpressNumber())) {
            return;
        }
        errorMsgBuilder.expressNumberEmpty();
    }
}
