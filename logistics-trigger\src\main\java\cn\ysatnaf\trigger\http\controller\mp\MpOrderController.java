package cn.ysatnaf.trigger.http.controller.mp;

import cn.ysatnaf.domain.order.model.req.OrderCancelReq;
import cn.ysatnaf.domain.order.model.req.OrderCreateReq;
import cn.ysatnaf.domain.order.model.req.OrderPickUpReq;
import cn.ysatnaf.domain.order.model.req.MpOrderSearchReq;
import cn.ysatnaf.domain.order.model.res.OrderDetailRes;
import cn.ysatnaf.domain.order.model.res.OrderSearchRes;
import cn.ysatnaf.domain.order.service.OrderService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;

/**
 * MpOrderController
 * 小程序订单控制器
 * <AUTHOR> Hang
 * @date 2023/12/22 15:14
 */
@Tag(name = "小程序订单相关")
@RequestMapping("/mp/order")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class MpOrderController {

    private final OrderService orderService;

    @PostMapping("/create")
    @PermitAll
    @Operation(summary = "创建订单")
    public CommonResult<String> create(@RequestBody @Validated OrderCreateReq req) {
        return CommonResult.success(orderService.create(req));
    }

    @PostMapping("/cancel")
    @Operation(summary = "取消订单")
    public CommonResult<Boolean> cancel(@RequestBody @Validated OrderCancelReq req) {
        return CommonResult.success(orderService.cancel(req));
    }

    @PostMapping("/search")
    @PermitAll
    @Operation(summary = "查询订单")
    public CommonResult<PageResult<OrderSearchRes>> update(@RequestBody @Validated MpOrderSearchReq req) {
        return CommonResult.success(orderService.search(req));
    }

    @GetMapping("/getById/{id}")
    @Operation(summary = "根据ID获取订单详情")
    public CommonResult<OrderDetailRes> getById(@PathVariable("id") Long id) {
        return CommonResult.success(orderService.getById(id));
    }

    @PostMapping("/pickUp")
    @Operation(summary = "取件")
    public CommonResult<Boolean> pickUp(@RequestBody @Validated OrderPickUpReq req) {
        return CommonResult.success(orderService.pickUp(req));
    }

    // todo 支付订单，将会生成一份运单数据
}
