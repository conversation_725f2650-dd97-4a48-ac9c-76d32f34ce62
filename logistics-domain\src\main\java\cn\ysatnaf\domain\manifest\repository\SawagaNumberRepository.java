package cn.ysatnaf.domain.manifest.repository;

import cn.ysatnaf.domain.manifest.model.entity.SawagaNumberEntity;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
public interface SawagaNumberRepository {
    void insertBatch(List<SawagaNumberEntity> numbers);

    SawagaNumberEntity getOne();

    List<SawagaNumberEntity> getBatch(Integer count);

    void updateById(SawagaNumberEntity expressNumberEntity);

    List<SawagaNumberEntity> getBatchSawagaNumberUnused(int size);

    void updateBatchById(List<SawagaNumberEntity> batchSawagaNumber);

    Integer countUsable();

    SawagaNumberEntity getByExpressNo(String expressNo);
}
