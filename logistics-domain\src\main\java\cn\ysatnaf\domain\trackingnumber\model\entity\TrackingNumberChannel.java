package cn.ysatnaf.domain.trackingnumber.model.entity;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 运单号渠道定义领域实体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingNumberChannel {

    private Long id;
    private Long carrierId;
    private Long locationId;
    private Long shipmentTypeId;
    private String channelCode;
    private String channelName;
    private String description;
    private Boolean isActive;
    private Long creatorId;
    private Long updaterId;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    // 可以添加业务方法，例如检查是否启用等
    public boolean isChannelActive() {
        return this.isActive != null && this.isActive;
    }
} 