package cn.ysatnaf.domain.manifest.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("sort_code_mapping")
public class SortCode {
    @TableId(value = "postal_code", type = IdType.INPUT)
    private String postalCode;
    
    @TableField("sort_code")
    private String sortCode;
}