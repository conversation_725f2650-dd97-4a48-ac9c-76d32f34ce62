package cn.ysatnaf.trigger.http.controller.mp;

import cn.ysatnaf.domain.auth.req.BindCourierReq;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.ErrorCode;
import cn.ysatnaf.types.exception.ServiceException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;
import java.net.URLEncoder;

/**
 * AuthController
 *
 * <AUTHOR> Hang
 * @date 2023/12/21 11:33
 */
@Controller
@Tag(name = "公众号 - 认证")
@RequestMapping("/auth")
@Validated
@Slf4j
@RequiredArgsConstructor
public class MpAuthController {

    private final WxMpService wxMpService;

    private final UserService userService;

    @GetMapping("/weChatOfficialAccount/login")
    @PermitAll
    @Operation(summary = "微信公众号网页授权")
    @Parameter(name = "returnUrl", description = "微信用户授权网页后要跳转的页面", required = true)
    public String wechatMpAuthorize(@RequestParam("returnUrl") String returnUrl) {
        String url = "https://banma365.cn/zebraExpressHub/auth/weChatOfficialAccount/userInfo";
        String redirectUrl = wxMpService.getOAuth2Service().buildAuthorizationUrl(url, WxConsts.OAuth2Scope.SNSAPI_USERINFO, URLEncoder.encode(returnUrl));
        return "redirect:" + redirectUrl;
    }

    @GetMapping("/weChatOfficialAccount/userInfo")
    @PermitAll
    @Operation(summary = "微信公众号获取用户基本信息")
    @Parameter(name = "code", description = "微信用户授权网页返回的code", required = true)
    public String wechatMpLogin(@RequestParam("code") String code, @RequestParam("state") String returnUrl) {
        WxOAuth2AccessToken accessToken;
        WxOAuth2UserInfo userInfo;
        try {
            accessToken = wxMpService.getOAuth2Service().getAccessToken(code);
            userInfo = wxMpService.getOAuth2Service().getUserInfo(accessToken, null);
            // todo whether 保存用户信息到数据库中
//            userService.saveByMp(userInfo);
        } catch (WxErrorException e) {
            log.error("[微信公众号获取用户信息错误]: ", e);
            throw new ServiceException(new ErrorCode(e.getError().getErrorCode(), e.getError().getErrorMsg()));
        }
        return "redirect:" + returnUrl + "?openid=" + userInfo.getOpenid();
    }

    @Operation(summary = "快递员绑定手机号")
    @PostMapping("/bindCourier")
    public CommonResult<Boolean> bindCourier(@RequestBody BindCourierReq req) {
        userService.bindCourier(req.getOpenid(), req.getMobileNumber());
        return CommonResult.success(true);
    }
}
