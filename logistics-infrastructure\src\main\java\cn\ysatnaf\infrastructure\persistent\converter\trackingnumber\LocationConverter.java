package cn.ysatnaf.infrastructure.persistent.converter.trackingnumber;

import cn.ysatnaf.domain.trackingnumber.model.entity.Location;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.LocationPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 地点/区域对象转换器
 * <AUTHOR>
 */
@Mapper
public interface LocationConverter {

    LocationConverter INSTANCE = Mappers.getMapper(LocationConverter.class);

    LocationPO toPO(Location location);

    Location toEntity(LocationPO locationPO);
    
    List<Location> toEntityList(List<LocationPO> locationPOs);
    
    List<LocationPO> toPOList(List<Location> locations);

} 