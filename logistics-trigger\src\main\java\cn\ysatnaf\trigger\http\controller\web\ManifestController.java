package cn.ysatnaf.trigger.http.controller.web;

import cn.hutool.core.util.ObjUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.manifest.model.dto.ManifestOrderDTO;
import cn.ysatnaf.domain.manifest.model.entity.ManifestSearchDTO;
import cn.ysatnaf.domain.manifest.model.req.ManifestSearchPickedUpReq;
import cn.ysatnaf.domain.manifest.model.req.ManifestFuzzySearchReq;
import cn.ysatnaf.domain.manifest.model.req.*;
import cn.ysatnaf.domain.manifest.model.res.*;
import cn.ysatnaf.domain.manifest.service.ManifestService;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Tag(name = "运单管理")
@RequestMapping("/web/manifest")
@Slf4j
@RestController
@RequiredArgsConstructor
public class ManifestController {

    private final ManifestService manifestService;

    private final ManifestPreReportService manifestPreReportService;

    @PostMapping("/getByExpressNumber")
    @PermitAll
    @Operation(summary = "根据运单号查询订单，可用于打印面单步骤一")
    public CommonResult<ManifestOrderDTO> getByExpressNumber(@Validated @RequestBody ManifestGetByExpressNumberReq req) {
        return CommonResult.success(manifestService.getByExpressNumber(req.getExpressNumber()));
    }

    @GetMapping("/getOrderById/{id}")
    @PermitAll
    @Operation(summary = "根据id获取订单")
    public CommonResult<ManifestOrderDTO> getOrderById(@PathVariable("id") Long id) {
        return CommonResult.success(manifestService.getOrderById(id));
    }

    @PostMapping("/updateById")
    @PermitAll
    @Operation(summary = "根据ID更新")
    public CommonResult<Boolean> updateById(@Validated @RequestBody ManifestUpdateByIdReq req) {
        return CommonResult.success(manifestService.updateById(req));
    }

    @PostMapping("/transfer")
    @PermitAll
    @Operation(summary = "转单")
    public CommonResult<Boolean> transfer(@Validated @RequestBody ManifestTransferReq req) {
        return CommonResult.success(manifestService.transfer(req));
    }

    /**
     * 批量转单号（上传Excel文件，批量转单号）
     * @param file MultipartFile
     * @return
     */
    @PostMapping("/batchTransfer")
    @PermitAll
    @Operation(summary = "批量转单号（上传Excel文件，批量转单号）")
    public CommonResult<Boolean> batchTransfer(@RequestParam("file") MultipartFile file) {
        return CommonResult.success(manifestService.batchTransfer(file));
    }

    @PostMapping("/remark")
    @PermitAll
    @Operation(summary = "修改备注")
    public CommonResult<Boolean> remark(@Validated @RequestBody ManifestRemarkReq req) {
        return CommonResult.success(manifestService.remark(req));
    }

    @PostMapping("/pickup")
    @PermitAll
    @Operation(summary = "揽件")
    public CommonResult<Boolean> pickup(@Validated @RequestBody ManifestPickupReq req) {
        return CommonResult.success(manifestService.pickup(req));
    }

    @Operation(summary = "根据填写信息生成物流面单")
    @PermitAll
    @PostMapping("/genWayBillImage")
    public void generateWayBillImage(@Validated @RequestBody ManifestGenWayBillImageReq req, HttpServletResponse httpServletResponse) {
        manifestService.generateWayBillImage(req, httpServletResponse);
    }

    @Operation(summary = "根据ID生成物流面单")
    @PermitAll
    @PostMapping("/genWayBillImageById")
    public void genWayBillImageById(@Validated @RequestBody ManifestGenWayBillImageByIdReq req, HttpServletResponse httpServletResponse) {
        manifestService.genWayBillImageById(req, httpServletResponse);
    }

    @PostMapping("/fuzzySearchExpressNumber")
    @PermitAll
    @Operation(summary = "模糊搜索运单号")
    public CommonResult<List<String>> fuzzySearchExpressNumber(@RequestBody ManifestFuzzySearchReq req) {
        return CommonResult.success(manifestService.fuzzySearchExpressNumber(req));
    }

    @PostMapping("/searchOrder")
    @PermitAll
    @Operation(summary = "查询订单")
    public CommonResult<PageResult<ManifestOrderDTO>> searchOrder(@RequestBody ManifestOrderSearchReq req) {
        return CommonResult.success(manifestService.searchOrder(req));
    }

    @PostMapping("/exportOrder")
    @PermitAll
    @Operation(summary = "导出订单")
    public void exportOrder(@RequestBody ManifestOrderExportReq req, HttpServletResponse response) {
        manifestService.exportOrder(req, response);
    }

    @PostMapping("/exportPreReportInfo")
    @PermitAll
    @Operation(summary = "导出预报信息")
    public void exportPreReportInfo(@RequestBody ManifestExportPreReportInfoReq req, HttpServletResponse response) {
        manifestService.exportPreReportInfo(req, response);
    }

    @PostMapping("/add")
    @PermitAll
    @Operation(summary = "手动录入运单")
    public CommonResult<Boolean> add(@Validated @RequestBody ManifestAddReq req) {
        if (ObjUtil.isNull(req.getUserId())) {
            req.setUserId(LoginUserHolder.getLoginUser().getId());
        }
        return CommonResult.success(manifestService.add(req));
    }

    @PostMapping("/addWithAllocatedExpress")
    @PermitAll
    @Operation(summary = "外部系统录入运单 - 自动分配单号", 
              description = "供外部系统调用的接口，不需要传入expressNumber，系统会根据目的地和货物类型自动分配单号")
    public CommonResult<Boolean> addWithAllocatedExpress(@Validated @RequestBody ManifestAddWithAllocatedExpressReq req) {
        if (ObjUtil.isNull(req.getUserId())) {
            req.setUserId(LoginUserHolder.getLoginUser().getId());
        }
        return CommonResult.success(manifestService.addWithAllocatedExpress(req));
    }

    @PostMapping("/preReportUpload")
    @PermitAll
    @Operation(summary = "运单预报文件上传")
    public CommonResult<ManifestPreReportUploadRes> preReportUpload(
            @RequestParam("file") MultipartFile file,
            @Schema(description = "管理员上传的时候需要填写，要给谁（userId）上传") Long userId) {
        if (ObjUtil.isNull(userId)) {
            userId = LoginUserHolder.getLoginUser().getId();
        }
        return CommonResult.success(manifestPreReportService.preReportUpload(file, userId));
    }

    @PostMapping("/preReportPreview")
    @PermitAll
    @Operation(summary = "运单预报预览")
    public CommonResult<ManifestPreReportPreviewRes> preReportPreview(@RequestBody ManifestPreReportPreviewReq req) {
        return CommonResult.success(manifestPreReportService.preReportPreview(req.getBatchId(), req.getPreviewType(), req.getPageNo(), req.getPageSize()));
    }

    @PostMapping("/confirmPreReport")
    @PermitAll
    @Operation(summary = "确认预报")
    public CommonResult<ManifestConfirmPreReportRes> confirmPreReport(@RequestBody ManifestConfirmPreReportReq req) {
        return CommonResult.success(manifestPreReportService.confirmPreReport(req));
    }

    @Operation(summary = "查询运单")
    @PermitAll
    @PostMapping("/search")
    public CommonResult<PageResult<ManifestSearchDTO>> search(@RequestBody ManifestSearchReq req) {
        return CommonResult.success(manifestService.searchManifest(req));
    }

    @Operation(summary = "根据运单号批量追踪订单")
    @PostMapping("/batchSearchByExpressNumbers")
    public CommonResult<List<ManifestBatchSearchByExpressNumberRes>> batchSearchByExpressNumbers(@RequestBody ManifestBatchSearchByExpressNumberReq req) {
        return CommonResult.success(manifestService.batchSearchByExpressNumbers(req.getExpressNumbers()));
    }


    @Operation(summary = "根据id获取运单")
    @PermitAll
    @GetMapping("/getById/{id}")
    public CommonResult<ManifestSearchDTO> getById(@PathVariable("id") Long id) {
        return CommonResult.success(manifestService.getById(id));
    }

    @Operation(summary = "导出运单")
    @PermitAll
    @PostMapping("/exportManifest")
    public void exportManifest(@Validated @RequestBody ManifestExportReq req, HttpServletResponse httpServletResponse) {
        manifestService.exportManifest(req, httpServletResponse);
    }

    @Operation(summary = "导出中国海关文件")
    @PermitAll
    @PostMapping("/exportApplicationForm")
    public void exportApplicationForm(@Validated @RequestBody ApplicationFormExportReq req, HttpServletResponse httpServletResponse) {
        manifestService.exportApplicationForm(req, httpServletResponse);
    }

    @Operation(summary = "下载导入模板")
    @PermitAll
    @PostMapping("/downloadImportTemplate")
    public void downloadImportTemplate(HttpServletResponse httpServletResponse) {
        manifestService.downloadImportTemplate(httpServletResponse);
    }

    @Operation(summary = "废弃订单")
    @PermitAll
    @PostMapping("/abandon")
    public CommonResult<Boolean> abandon(@RequestBody ManifestAbandonReq req) {
        return CommonResult.success(manifestService.abandon(req.getManifestIds()));
    }

    @Operation(summary = "恢复订单")
    @PermitAll
    @PostMapping("/restore")
    public CommonResult<Boolean> restore(@RequestBody ManifestAbandonReq req) {
        return CommonResult.success(manifestService.restore(req.getManifestIds()));
    }

    @Operation(summary = "修改运费")
    @PermitAll
    @PostMapping("/updateFreightCost")
    public CommonResult<Boolean> updateFreightCost(@RequestBody ManifestUpdateFreightCostReq req) {
        return CommonResult.success(manifestService.updateFreightCost(req));
    }

    @Operation(summary = "统计")
    @PermitAll
    @PostMapping("/statistics")
    public CommonResult<ManifestStatisticsRes> statistics(@RequestBody ManifestStatisticsReq req) {
        return CommonResult.success(manifestService.statistics(req));
    }

    @Operation(summary = "归属划转")
    @PostMapping("/changeOwner")
    public CommonResult<Boolean> changeOwner(@RequestBody ManifestChangeOwnerReq req) {
        return CommonResult.success(manifestService.changeOwner(req));
    }

    @Operation(summary = "打印简易面单")
    @PostMapping("/printSimpleWaybill")
    public void printSimpleWaybill(@RequestBody ManifestPrintSimpleWaybillReq req, HttpServletResponse httpServletResponse) {
        manifestService.printSimpleWaybill(req.getManifestIds(), httpServletResponse);
    }

    @Operation(summary = "生成条形码PDF")
    @PostMapping("/generateBarcodePdf")
    public void generateBarcodePdf(@RequestBody ManifestGenerateBarcodePdfReq req, HttpServletResponse httpServletResponse) {
        manifestService.generateBarcodePdf(req, httpServletResponse);
    }

    @Operation(summary = "查询揽件记录")
    @PermitAll
    @PostMapping("/searchPickedUp")
    public CommonResult<PageResult<ManifestSearchPickedUpRes>> searchPickedUpOrder(@RequestBody ManifestSearchPickedUpReq req) {
        log.info("高级查询运单请求: {}", JSON.toJSONString(req));
        return CommonResult.success(manifestService.searchPickedUp(req));
    }
}
