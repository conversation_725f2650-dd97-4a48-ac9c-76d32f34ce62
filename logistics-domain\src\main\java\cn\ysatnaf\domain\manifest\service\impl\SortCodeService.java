package cn.ysatnaf.domain.manifest.service.impl;

import cn.ysatnaf.domain.manifest.mapper.SortCodeMapper;
import cn.ysatnaf.domain.manifest.model.entity.SortCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SortCodeService {
    @Autowired
    private SortCodeMapper mapper;

    public String getSortCode(String postalCode) {
        SortCode entity = mapper.selectById(postalCode);
        return entity != null ? entity.getSortCode() : null;
    }
}