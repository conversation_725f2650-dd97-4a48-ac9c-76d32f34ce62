package cn.ysatnaf.domain.manifest.service.prereport;

import cn.ysatnaf.domain.manifest.model.req.ManifestConfirmPreReportReq;
import cn.ysatnaf.domain.manifest.model.res.ManifestConfirmPreReportRes;
import cn.ysatnaf.domain.manifest.model.res.ManifestPreReportPreviewRes;
import cn.ysatnaf.domain.manifest.model.res.ManifestPreReportUploadRes;
import org.springframework.web.multipart.MultipartFile;

public interface ManifestPreReportService {

    /**
     * 预报上传
     *
     * @param file   文件
     * @param userId 文件所属用户ID
     * @return 上传文件检测结果
     */
    ManifestPreReportUploadRes preReportUpload(MultipartFile file, Long userId);

    /**
     * 预报预览
     * <p>
     * 通过上传的文件解析到里面的内容，然后返回给用户哪些内容是没上传过的，哪些是上传过的，然后用户来决定是否需要覆盖已经上传过的数据。
     * 首先判断是否有批次号，如果有那么走缓存，如果没有，那么读取并分析文件
     * 1. 存储上传文件，用于后续排查。
     * 2. 解析上传文件
     * 3. 缓存上传文件内容，用于用户翻页查看上传内容的时候以及真正确认要插入数据的时候使用，避免多次上传解析
     * 4. 返回给用户哪些内容是没上传过的，哪些是上传过的
     *
     * @param batchId     上传操作的批次号，用于缓存相关
     * @param previewType 预览类型，1：预览未上传过的，2：预览已上传过的
     * @return 上传文件检测结果
     */
    ManifestPreReportPreviewRes preReportPreview(String batchId,
                                                 Integer previewType,
                                                 Integer pageNumber,
                                                 Integer pageSize);

    /**
     * 确认预报
     *
     * @param req 确认预报请求参数
     * @return 确认预报结果
     */
    ManifestConfirmPreReportRes confirmPreReport(ManifestConfirmPreReportReq req);
}
