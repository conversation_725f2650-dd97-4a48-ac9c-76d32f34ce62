package cn.ysatnaf.domain.trackingnumber.model.dto;

import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberAllocationBatch;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberPool;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 单号分配结果 DTO
 * <AUTHOR>
 */
@Data
@Builder
public class TrackingNumberAllocationResultDTO {

    /**
     * 分配批次信息
     */
    private TrackingNumberAllocationBatch allocationBatch;

    // 移除分配的单号列表
    /*
    private List<TrackingNumberPool> allocatedNumbers;
    */

} 