package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplateUserPO;
import cn.ysatnaf.domain.shippingfeetemplate.repository.ShippingFeeTemplateUserRepository;
import cn.ysatnaf.infrastructure.persistent.dao.ShippingFeeTemplateUserDao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class ShippingFeeTemplateUserRepositoryImpl implements ShippingFeeTemplateUserRepository {

    private final ShippingFeeTemplateUserDao shippingFeeTemplateUserDao;

    @Override
    public ShippingFeeTemplateUserPO getByUserIdAndType(Long userId, Integer type) {
        LambdaQueryWrapper<ShippingFeeTemplateUserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShippingFeeTemplateUserPO::getUserId, userId);
        wrapper.eq(ShippingFeeTemplateUserPO::getType, type);
        return shippingFeeTemplateUserDao.selectOne(wrapper);
    }

    @Override
    public void insert(ShippingFeeTemplateUserPO templateUserPO) {
        shippingFeeTemplateUserDao.insert(templateUserPO);
    }

    @Override
    public void updateById(ShippingFeeTemplateUserPO templateUserPO) {
        shippingFeeTemplateUserDao.updateById(templateUserPO);
    }

    @Override
    public List<ShippingFeeTemplateUserPO> getByUserIds(List<Long> userIds) {
        LambdaQueryWrapper<ShippingFeeTemplateUserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ShippingFeeTemplateUserPO::getUserId, userIds);
        return shippingFeeTemplateUserDao.selectList(wrapper);
    }
}
