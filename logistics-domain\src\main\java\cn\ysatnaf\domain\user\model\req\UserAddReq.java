package cn.ysatnaf.domain.user.model.req;

import cn.ysatnaf.types.validation.Phone;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 */
@Schema(description = "创建用户入参")
@Data
public class UserAddReq {

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "yudaoyuanma")
    @NotEmpty(message = "登录账号不能为空")
    @Length(min = 4, max = 16, message = "账号长度为 4-16 位")
    @Pattern(regexp = "^[A-Za-z0-9]+$", message = "账号格式为数字以及字母")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "buzhidao")
    @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
    private String password = "banma123456";

    @Schema(description = "昵称")
    @NotBlank(message = "昵称不能为空")
    @Length(min = 2, max = 16)
    private String nickname;

    @Schema(description = "角色ID，1-管理员；2-普通用户")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @Schema(description = "操作者自己的密码")
    @NotBlank(message = "密码不能为空")
    private String ownPassword;

    @Schema(description = "手机号码（当创建用户为“快递员”时必填")
    @Phone
    private String mobileNumber;


}
