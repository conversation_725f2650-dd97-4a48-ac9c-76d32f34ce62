package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplatePO;
import cn.ysatnaf.domain.shippingfeetemplate.repository.ShippingFeeTemplateRepository;
import cn.ysatnaf.infrastructure.persistent.dao.ShippingFeeTemplateDao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@Repository
public class ShippingFeeTemplateRepositoryImpl implements ShippingFeeTemplateRepository {

    private final ShippingFeeTemplateDao shippingFeeTemplateDao;

    @Override
    public void insert(ShippingFeeTemplatePO shippingFeeTemplatePO) {
        shippingFeeTemplateDao.insert(shippingFeeTemplatePO);
    }

    @Override
    public void updateById(ShippingFeeTemplatePO shippingFeeTemplatePO) {
        shippingFeeTemplateDao.updateById(shippingFeeTemplatePO);
    }

    @Override
    public void deleteById(Long id) {
        shippingFeeTemplateDao.deleteById(id);
    }

    @Override
    public List<ShippingFeeTemplatePO> list(Integer type) {
        LambdaQueryWrapper<ShippingFeeTemplatePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ShippingFeeTemplatePO::getType, type);
        return shippingFeeTemplateDao.selectList(wrapper);
    }

    @Override
    public ShippingFeeTemplatePO getById(Long templateId) {
        return shippingFeeTemplateDao.selectById(templateId);
    }

    @Override
    public List<ShippingFeeTemplatePO> getByIds(List<Long> templateIds) {
        return shippingFeeTemplateDao.selectBatchIds(templateIds);
    }
}
