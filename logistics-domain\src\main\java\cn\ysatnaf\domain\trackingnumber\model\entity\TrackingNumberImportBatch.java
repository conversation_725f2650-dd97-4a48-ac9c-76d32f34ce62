package cn.ysatnaf.domain.trackingnumber.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 单号导入批次记录领域实体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingNumberImportBatch {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 上传操作员ID
     */
    private Long uploaderId;

    /**
     * 本次导入的目标渠道ID (FK -> tracking_number_channels.id)
     */
    private Long channelId;

    /**
     * 原始文件名
     */
    private String originalFilename;

    /**
     * 总导入数量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failedCount;

    /**
     * 导入时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime importTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 上传者姓名 (非持久化字段，查询时填充)
     */
    private String uploaderName;

    /**
     * 渠道代码 (冗余字段，查询时填充)
     */
    private String channelCode;

    /**
     * 渠道名称 (冗余字段，查询时填充)
     */
    private String channelName;
} 