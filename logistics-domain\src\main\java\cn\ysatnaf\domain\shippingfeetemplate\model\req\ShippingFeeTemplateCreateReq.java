package cn.ysatnaf.domain.shippingfeetemplate.model.req;

import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "创建运费模板入参")
@Data
public class ShippingFeeTemplateCreateReq {

    @Schema(description = "模板名字")
    @NotBlank
    @Length(min = 1, max = 32)
    private String name;

    @Schema(description = "首重价格")
    @NotNull
    @Min(0)
    private BigDecimal firstWeightPrice;

    @Schema(description = "首重范围")
    @Min(0)
    @NotNull
    private BigDecimal firstWeightRange;

    @Schema(description = "续重价格")
    @Min(0)
    @NotNull
    private BigDecimal continuedWeightPrice;

    @Schema(description = "续重区间大小")
    @Min(0)
    @NotNull
    private BigDecimal continuedWeightInterval;

    @Schema(description = "轻抛系数，当模板类型为普通模板或者带电模板的时候需要填写")
    @Min(0)
        private Integer bulkCoefficient;

    @Schema(description = "三边和超过该值开始计算体积重量")
    @Min(0)
    private BigDecimal threeSidesStart;

    /**
     * 模板类型 {@link ShippingFeeTemplateTypeEnum#getCode()}
     */
    @Schema(description = "模板类型：1-普通模板；2-带电模板；3-投函模板")
    @NotNull
    private Integer type;
}
