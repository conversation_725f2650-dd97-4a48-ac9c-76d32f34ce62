package cn.ysatnaf.domain.order.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * OrderAssignReq
 *
 * <AUTHOR>
 * @date 2024/2/7 16:08
 */
@Schema(description = "分配订单 入参")
@Data
public class OrderAssignReq {

    @Schema(description = "订单ID")
    @NotNull
    private Long orderId;

    @Schema(description = "快递员ID")
    @NotNull
    private Long courierId;
}
