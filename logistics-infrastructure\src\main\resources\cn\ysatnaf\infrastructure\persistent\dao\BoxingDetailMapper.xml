<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ysatnaf.infrastructure.persistent.dao.BoxingDetailDao">

    <!-- 批量插入包裹信息 -->
    <insert id="insertBatchSomeColumn">
        INSERT INTO tb_parcel_sorting_package
          (box_id, manifest_id, order_no, express_number, receiver_name, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.boxId}, #{item.manifestId}, #{item.orderNo}, #{item.expressNumber}, #{item.receiverName}, NOW(), NOW())
        </foreach>
    </insert>

</mapper> 