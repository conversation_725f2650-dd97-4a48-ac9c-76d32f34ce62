package cn.ysatnaf.domain.item.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Schema(description = "添加物品名称映射请求")
public class ItemNameMappingAddReq {

    @NotBlank(message = "原始名称不能为空")
    @Size(max = 255, message = "原始名称长度不能超过255个字符")
    @Schema(description = "原始物品名称", required = true, example = "包含锂电池的玩具")
    private String originalName;

    @NotBlank(message = "映射后名称不能为空")
    @Size(max = 255, message = "映射后名称长度不能超过255个字符")
    @Schema(description = "映射后的合规物品名称", required = true, example = "玩具(不含电池)")
    private String mappedName;

    @NotNull(message = "启用状态不能为空")
    @Schema(description = "是否启用", required = true, defaultValue = "true")
    private Boolean isActive = true;

    @Schema(description = "备注信息", example = "根据最新规定修改")
    private String remarks;
} 