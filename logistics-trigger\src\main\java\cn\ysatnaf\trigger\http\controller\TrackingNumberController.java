package cn.ysatnaf.trigger.http.controller;

import cn.ysatnaf.domain.trackingnumber.model.dto.ChannelAvailableCountDTO;
import cn.ysatnaf.domain.trackingnumber.model.dto.TrackingNumberAllocationResultDTO;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberAllocationBatch;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberImportBatch;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberPool;
import cn.ysatnaf.domain.trackingnumber.model.req.AllocateTrackingNumberReq;
import cn.ysatnaf.domain.trackingnumber.service.TrackingNumberService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 预报单号管理 Controller
 * <AUTHOR>
 */
@Tag(name = "TrackingNumberController", description = "预报单号管理")
@RestController
@RequestMapping("/api/tracking-numbers")
@RequiredArgsConstructor
@Validated
public class TrackingNumberController {

    private final TrackingNumberService trackingNumberService;

    @Operation(summary = "导入单号到指定渠道")
    @PostMapping("/import")
    public CommonResult<TrackingNumberImportBatch> importTrackingNumbers(
            @Schema(description = "单号Excel文件", required = true) @RequestParam("file") MultipartFile file,
            @Schema(description = "目的地ID", required = true) @RequestParam("locationId") @NotNull(message = "目的地ID不能为空") Long locationId,
            @Schema(description = "货物类型ID", required = true) @RequestParam("shipmentTypeId") @NotNull(message = "货物类型ID不能为空") Long shipmentTypeId) {
        TrackingNumberImportBatch importBatch = trackingNumberService.importTrackingNumbers(file, locationId, shipmentTypeId);
        return CommonResult.success(importBatch);
    }

    @Operation(summary = "根据目的地、货物类型和客户分配单号")
    @PostMapping("/allocate")
    public CommonResult<TrackingNumberAllocationResultDTO> allocateTrackingNumbers(
            @Schema(description = "分配请求参数(包含 locationId, shipmentTypeId, customerAccountId 和 quantity)", required = true) @Valid @RequestBody AllocateTrackingNumberReq req) {
        TrackingNumberAllocationResultDTO result = trackingNumberService.allocateTrackingNumbers(req);
        return CommonResult.success(result);
    }

    @Operation(summary = "分页查询导入批次")
    @GetMapping("/import-batches")
    public CommonResult<PageResult<TrackingNumberImportBatch>> pageQueryImportBatches(
            @Parameter(description = "上传者用户ID") @RequestParam(required = false) Long uploaderId,
            @Parameter(description = "渠道ID") @RequestParam(required = false) Long channelId,
            @Parameter(description = "导入开始时间 (yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "导入结束时间 (yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNo,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<TrackingNumberImportBatch> pageResult = trackingNumberService.pageQueryImportBatches(
                uploaderId, channelId, startTime, endTime, pageNo, pageSize);
        return CommonResult.success(pageResult);
    }

    @Operation(summary = "分页查询分配批次")
    @GetMapping("/allocation-batches")
    public CommonResult<PageResult<TrackingNumberAllocationBatch>> pageQueryAllocationBatches(
            @Parameter(description = "分配者用户ID") @RequestParam(required = false) Long allocatorId,
            @Parameter(description = "请求地点ID") @RequestParam(required = false) Long requestedLocationId,
            @Parameter(description = "请求货物类型ID") @RequestParam(required = false) Long requestedShipmentTypeId,
            @Parameter(description = "分配开始时间 (yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "分配结束时间 (yyyy-MM-dd HH:mm:ss)") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNo,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize) {
        PageResult<TrackingNumberAllocationBatch> pageResult = trackingNumberService.pageQueryAllocationBatches(
                allocatorId, requestedLocationId, requestedShipmentTypeId, startTime, endTime, pageNo, pageSize);
        return CommonResult.success(pageResult);
    }

    @Operation(summary = "查询指定分配批次的单号列表")
    @GetMapping("/allocation-batches/{allocationBatchId}/numbers")
    public CommonResult<List<TrackingNumberPool>> findAllocatedNumbersByBatchId(
             @Parameter(description = "分配批次ID", required = true) @PathVariable @NotNull Long allocationBatchId) {
        List<TrackingNumberPool> numbers = trackingNumberService.findAllocatedNumbersByBatchId(allocationBatchId);
        return CommonResult.success(numbers);
    }

    @Operation(summary = "导出指定分配批次的单号Excel")
    @GetMapping("/allocation-batches/{allocationBatchId}/export")
    public void exportAllocatedNumbers(
             @Parameter(description = "分配批次ID", required = true) @PathVariable @NotNull Long allocationBatchId,
             HttpServletResponse response) {
        trackingNumberService.exportAllocatedNumbers(allocationBatchId, response);
    }

    @Operation(summary = "打印指定分配批次的单号")
    @GetMapping("/allocation-batches/{allocationBatchId}/print")
    public void printAllocatedNumbers(
             @Parameter(description = "分配批次ID", required = true) @PathVariable @NotNull Long allocationBatchId,
             HttpServletResponse response) {
        trackingNumberService.printAllocatedNumbers(allocationBatchId, response);
    }

    @Operation(summary = "查询所有活动渠道的可用单号数量")
    @GetMapping("/available-counts")
    public CommonResult<List<ChannelAvailableCountDTO>> getAvailableCounts() {
        List<ChannelAvailableCountDTO> counts = trackingNumberService.getAvailableCounts();
        return CommonResult.success(counts);
    }
}