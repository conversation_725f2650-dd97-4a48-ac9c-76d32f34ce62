package cn.ysatnaf.domain.sawaga.service.impl;

import cn.ysatnaf.domain.manifest.model.entity.SawagaSiteCodeEntity;
import cn.ysatnaf.domain.manifest.repository.SawagaSiteCodeRepository;
import cn.ysatnaf.domain.sawaga.model.po.SawagaSitePO;
import cn.ysatnaf.domain.sawaga.repository.SawagaSiteRepository;
import cn.ysatnaf.domain.sawaga.service.SawagaSiteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class SawagaSiteServiceImpl implements SawagaSiteService {

    private final SawagaSiteRepository sawagaSiteRepository;

    private final SawagaSiteCodeRepository sawagaSiteCodeRepository;

    @Override
    public List<SawagaSiteCodeEntity> getByZipCode(String zipCode) {
        return sawagaSiteCodeRepository.getByZipCode(zipCode);
    }

    @Override
    public SawagaSitePO getSawagaSite(String prefectureName, String municipalName) {
        // 包含市的截取市前面
        if (municipalName.contains("市")) {
            municipalName = municipalName.substring(0, municipalName.lastIndexOf("市"));
        }
        SawagaSitePO sitePO = sawagaSiteRepository.getBySiteName(municipalName);
        if (sitePO != null) {
            return sitePO;
        }
        // 不包含市的，截掉最后一位
        municipalName = municipalName.substring(0, municipalName.length() - 1);
        sitePO = sawagaSiteRepository.getBySiteName(municipalName);
        if (sitePO != null) {
            return sitePO;
        }
        // 包含县的截取县前面
        if (prefectureName.contains("県")) {
            prefectureName = prefectureName.substring(0, prefectureName.lastIndexOf("県"));
        }
        sitePO = sawagaSiteRepository.getBySiteName(prefectureName);
        if (sitePO != null) {
            return sitePO;
        }
        // 不包含县的，截掉最后一位
        prefectureName = prefectureName.substring(0, prefectureName.length() - 1);
        return sawagaSiteRepository.getBySiteName(prefectureName);
    }
}
