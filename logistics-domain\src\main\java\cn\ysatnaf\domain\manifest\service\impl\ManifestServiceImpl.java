package cn.ysatnaf.domain.manifest.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;
import cn.ysatnaf.domain.address.service.ReceiverAreaService;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.domain.excel.serviice.generator.BatchInfo;
import cn.ysatnaf.domain.excel.serviice.generator.DocumentDataContext;
import cn.ysatnaf.domain.excel.serviice.generator.DocumentGeneratorFactory;
import cn.ysatnaf.domain.excel.serviice.generator.JapanCustomsDocumentGenerator;
import cn.ysatnaf.domain.excel.serviice.generator.row.TokyoCustomsDocumentExcelRow;
import cn.ysatnaf.domain.excel.serviice.impl.ChineseCustomsDocumentGenerationV3ServiceImpl;
import cn.ysatnaf.domain.fund.model.dto.ModifyBalanceDTO;
import cn.ysatnaf.domain.fund.service.FundAccountService;
import cn.ysatnaf.domain.image.service.WayBillImageService;
import cn.ysatnaf.domain.log.model.entity.OperationLogEntity;
import cn.ysatnaf.domain.log.model.valobj.OperationType;
import cn.ysatnaf.domain.log.service.OperationLogService;
import cn.ysatnaf.domain.manifest.adapter.ManifestAdapter;
import cn.ysatnaf.domain.manifest.model.aggregate.ManifestAggregate;
import cn.ysatnaf.domain.manifest.model.dto.*;
import cn.ysatnaf.domain.manifest.model.entity.*;
import cn.ysatnaf.domain.manifest.model.excel.*;
import cn.ysatnaf.domain.manifest.model.message.ShipmentMessage;
import cn.ysatnaf.domain.manifest.model.req.*;
import cn.ysatnaf.domain.manifest.model.res.ManifestSearchPickedUpRes;
import cn.ysatnaf.domain.manifest.model.res.ManifestBatchSearchByExpressNumberRes;
import cn.ysatnaf.domain.manifest.model.res.ManifestStatisticsRes;
import cn.ysatnaf.domain.manifest.model.res.MasterBillDetailRes;
import cn.ysatnaf.domain.manifest.model.valobj.*;
import cn.ysatnaf.domain.manifest.repository.ManifestRepository;
import cn.ysatnaf.domain.manifest.service.*;
import cn.ysatnaf.domain.pdf.service.BarcodePdfService;
import cn.ysatnaf.domain.sawaga.model.po.SawagaSitePO;
import cn.ysatnaf.domain.sawaga.service.SawagaSiteService;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberChannel;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberPool;
import cn.ysatnaf.domain.trackingnumber.repository.TrackingNumberChannelRepository;
import cn.ysatnaf.domain.trackingnumber.repository.TrackingNumberPoolRepository;
import cn.ysatnaf.domain.trackingnumber.service.TrackingNumberService;
import cn.ysatnaf.domain.trackingnumber.model.req.AllocateTrackingNumberReq;
import cn.ysatnaf.domain.trackingnumber.model.dto.TrackingNumberAllocationResultDTO;
import cn.ysatnaf.domain.item.service.ItemNameMappingService;
import cn.ysatnaf.domain.item.service.ProhibitedItemKeywordService;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.constants.CacheConstants;
import cn.ysatnaf.types.exception.ServiceException;
import cn.ysatnaf.types.util.BarcodeImageUtil;
import cn.ysatnaf.types.util.TranslationUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.draw.SolidLine;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.properties.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;
import java.util.Optional;

import cn.ysatnaf.domain.log.service.ManifestLogHelper; // 导入 ManifestLogHelper
import cn.hutool.core.bean.BeanUtil; // 导入 BeanUtil 用于克隆
import cn.hutool.core.util.ObjectUtil; // 导入 ObjectUtil 用于克隆

import java.util.Objects;

import cn.ysatnaf.domain.log.model.entity.ManifestOperationLog;
import cn.ysatnaf.domain.manifest.model.entity.SawagaSiteCodeEntity;

/**
 * ManifestServiceImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 16:05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ManifestServiceImpl implements ManifestService {

    private final ReceiverAreaService receiverAreaService;

    private final ManifestRepository manifestRepository;

    private final ManifestItemService manifestItemService;

    private final ExpressNumberService expressNumberService;

    private final ManifestDataValidateService manifestDataValidateService;

    private final SawagaSiteService sawagaSiteService;

    private final UserService userService;

    private final OperationLogService operationLogService;

    private final ManifestLogHelper manifestLogHelper; // 注入 ManifestLogHelper

    private final TrackingService trackingService;

    private final RedissonClient redissonClient;

    private final ChineseCustomsDocumentGenerationV3ServiceImpl chineseCustomsDocumentGenerationService;

    private final DocumentGeneratorFactory documentGeneratorFactory;

    private final WayBillImageService wayBillImageService;

    private final RabbitTemplate rabbitTemplate;

    private final TranslationUtil translationUtil;

    private final BarcodePdfService barcodePdfService;

    private final MasterBillService masterBillService;

    private final OperateManifestService operateManifestService;

    private final TrackingNumberPoolRepository trackingNumberPoolRepository;

    private final TrackingNumberChannelRepository trackingNumberChannelRepository;

    private final ItemNameMappingService itemNameMappingService;

    private final ProhibitedItemKeywordService prohibitedItemKeywordService;
    
    private final TrackingNumberService trackingNumberService;

    @Override
    public PageResult<ManifestSearchDTO> searchManifest(ManifestSearchReq req) {
        if (!LoginUserHolder.getLoginUser().ifAdmin()) {
            throw new ServiceException("没有查看权限");
        }
        PageResult<Manifest> pageResult = manifestRepository
                .searchManifest(
                        req.getExpressNumber(),
                        req.getSawagaNumber(),
                        req.getOrderNo(),
                        req.getCreateTimeStart(),
                        req.getCreateTimeEnd(),
                        req.getPageNo(),
                        req.getPageSize());
        List<Manifest> manifests = pageResult.getList();
        if (CollUtil.isEmpty(manifests)) {
            return new PageResult<>(pageResult.getTotal());
        }

        List<ManifestSearchDTO> searchDTOList = manifests.stream()
                .map(ManifestSearchDTO::of)
                .collect(Collectors.toList());
        return new PageResult<>(searchDTOList, pageResult.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportManifest(ManifestExportReq req, HttpServletResponse response) {
        userService.validateAdmin();
        // 查询对应运单，然后更新成已发货状态
        // 判断是否有ID入参，有ID按ID查，没ID按时间范围、运费模板类型查
        List<Manifest> manifests;
        if (CollUtil.isNotEmpty(req.getIds())) {
            manifests = manifestRepository.getByIds(req.getIds());
        } else {
            // 判断目的地
            if (req.getDestination() == null) {
                throw new ServiceException("请选择目的地");
            }
            List<Integer> shippingFeeTemplateTypes;
            if (req.getDestination().equals(Destination.OSAKA)) {
                shippingFeeTemplateTypes = new ArrayList<>();
                req.getShippingFeeTemplateTypes().forEach(shippingFeeTemplateType -> {
                    if (shippingFeeTemplateType.equals(ShippingFeeTemplateTypeEnum.GENERAL.getCode())) {
                        shippingFeeTemplateTypes.add(ShippingFeeTemplateTypeEnum.GENERAL_OSAKA.getCode());
                    } else if (shippingFeeTemplateType.equals(ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode())) {
                        shippingFeeTemplateTypes.add(ShippingFeeTemplateTypeEnum.SMALL_PARCEL_OSAKA.getCode());
                    }
                });
            } else if (req.getDestination().equals(Destination.TOKYO)) {
                shippingFeeTemplateTypes = req.getShippingFeeTemplateTypes();
                if (shippingFeeTemplateTypes.contains(ShippingFeeTemplateTypeEnum.GENERAL.getCode())) {
                    shippingFeeTemplateTypes.add(ShippingFeeTemplateTypeEnum.SPECIAL.getCode());
                }
            } else {
                throw new ServiceException("请选择正确的目的地");
            }
            manifests = manifestRepository.listByPickUpTimeAndShippingFeeTemplateType(req.getPickUpTimeFrom(), req.getPickUpTimeTo(), shippingFeeTemplateTypes);
        }
        if (CollUtil.isEmpty(manifests)) {
            throw new ServiceException("没有符合条件的订单可以导出");
        }
        ship(manifests);

        try {
            OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                    .operatorId(LoginUserHolder.getLoginUser().getId())
                    .operationType(OperationType.MANIFEST_EXPORT_JP_CUSTOMS_DOCUMENTS.getValue())
                    .operation(OperationType.MANIFEST_EXPORT_JP_CUSTOMS_DOCUMENTS.getDescription())
                    .newInfo(JSON.toJSONString(req))
                    .build();

            JapanCustomsDocumentGenerator generator = documentGeneratorFactory.getGeneratorByDestination(req.getDestination());
            operationLogService.log(operationLogEntity);
            BatchInfo batchInfo = new BatchInfo();
            batchInfo.setFlightDate(req.getFlightDate());
            batchInfo.setLadingBill(req.getLadingBill());
            batchInfo.setFlightNumber(req.getFlightNumber());
            DocumentDataContext documentDataContext = new DocumentDataContext(manifests, batchInfo, response);
            generator.generate(documentDataContext);
        } catch (IOException e) {
            log.error("导出文件失败: ", e);
            throw new ServiceException(9999, "导出文件失败");
        }
    }

    public void ship(List<Manifest> manifests) {
        userService.validateAdmin();
        FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
        List<OperationLogEntity> operationLogEntities = new ArrayList<>();
        List<Tracking> trackingList = new ArrayList<>();
        manifests.forEach(manifest -> {
            if (manifest.ifShipped()) {
                return;
            }

            manifest.setStatus(ManifestStatus.SHIPPED.getCode());
            manifest.setShipmentTime(LocalDateTime.now());
            manifest.setTrackingStatus(TrackingStatus.SHIPPED.getValue());
            manifest.setTrackingUpdateTime(LocalDateTime.now());
            // 扣费操作
            BigDecimal totalCost = manifest.getTotalCost();
            if (!fundAccountService.cost(manifest.getUserId(), totalCost, manifest.getId())) {
                UserEntity userEntity = userService.getById(manifest.getUserId());
                throw new ServiceException("用户【" + userEntity.getNickname() + "】余额不足，导出发货失败");
            }
            // 发送消息到队列，告知已发货
            ShipmentMessage message = ShipmentMessage.builder()
                    .trackingNumber(manifest.getExpressNumber())
                    .shippingFee(totalCost).build();
            rabbitTemplate.convertAndSend("logistics.info", "ship", message);

            // 记录轨迹
            trackingList.add(Tracking.builder()
                    .trackingNumber(manifest.getTrackingNumber())
                    .manifestId(manifest.getId())
                    .operatorId(LoginUserHolder.getLoginUser().getId())
                    .status(TrackingStatus.SHIPPED.getValue())
                    .time(new Date())
                    .track(TrackingStatus.SHIPPED.getDescription()).build());

            // 记录日志
            operationLogEntities.add(OperationLogEntity.builder()
                    .operatorId(LoginUserHolder.getLoginUser().getId())
                    .operationType(OperationType.MANIFEST_SHIPPING.getValue())
                    .operation(OperationType.MANIFEST_SHIPPING.getDescription())
                    .manifestId(manifest.getId())
                    .build());
        });

        // 更新运单数据库状态
        if (CollUtil.isNotEmpty(manifests)) {
            manifestRepository.updateBatchByIds(manifests);
        }

        // 记录物流轨迹
        if (CollUtil.isNotEmpty(trackingList)) {
            trackingService.insertBatch(trackingList);
        }

        // --- 开始：调用 ManifestLogHelper 进行批量发货日志记录 ---
        try {
            LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
            Long operatorId = loginUser.getId();
            String operatorName = loginUser.getNickname(); // 使用 getNickname
            String context = "后台管理-批量发货"; // 定义上下文
            String ipAddress = null; // IP 地址暂不获取

            manifestLogHelper.recordBatchShipmentLog(manifests, operatorId, operatorName, ipAddress, context);

        } catch (Exception logEx) {
            log.error("调用批量发货日志记录失败", logEx);
            // 根据需要处理日志记录本身的异常
        }
        // --- 结束：调用 ManifestLogHelper 进行批量发货日志记录 ---
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportApplicationForm(ApplicationFormExportReq req, HttpServletResponse httpServletResponse) {
        // 查询对应运单
        List<ManifestAggregate> manifestAggregates = searchManifestAggregate(req.getIds(), req.getPickUpTimeFrom(), req.getPickUpTimeTo(), null);
        // 更新成已发货
        List<Manifest> manifests = manifestAggregates.stream().map(ManifestAggregate::getManifest).collect(Collectors.toList());
        // 发货
        ship(manifests);
        chineseCustomsDocumentGenerationService.generate(req.getLadingBill(), manifestAggregates, httpServletResponse);
        List<OperationLogEntity> operationLogEntities = new ArrayList<>();
        for (Long id : req.getIds()) {
            // 记录日志
            OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                    .operatorId(LoginUserHolder.getLoginUser().getId())
                    .operationType(OperationType.MANIFEST_EXPORT_CN_CUSTOMS_DOCUMENTS.getValue())
                    .operation(OperationType.MANIFEST_EXPORT_CN_CUSTOMS_DOCUMENTS.getDescription())
                    .manifestId(id)
                    .build();
            operationLogEntities.add(operationLogEntity);
        }

        operationLogService.logBatch(operationLogEntities);
    }

    public List<ManifestAggregate> getManifestAggregateByExpressNumbers(List<String> expressNumbers) {
        List<Manifest> manifests = manifestRepository.getByExpressNumbers(expressNumbers);
        if (CollUtil.isEmpty(manifests)) {
            return Collections.emptyList();
        }
        List<ManifestItem> manifestItems = manifestItemService.listBatchByManifestIds(manifests.stream().map(Manifest::getId).collect(Collectors.toList()));
        Map<Long, List<ManifestItem>> manifestIdMapItem = manifestItems.stream().collect(Collectors.groupingBy(ManifestItem::getManifestId));

        return manifests.stream()
                .map(manifest -> ManifestAggregate
                        .builder()
                        .manifest(manifest)
                        .manifestItems(manifestIdMapItem.get(manifest.getId())).build())
                .collect(Collectors.toList());
    }

    public List<ManifestAggregate> searchManifestAggregate(List<Long> ids, Date pickUpTimeFrom, Date pickUpTimeTo, List<Integer> shippingFeeTemplateTypes) {
        List<Manifest> manifests;
        if (CollUtil.isNotEmpty(ids)) {
            manifests = manifestRepository.getByIds(ids);
        } else {
            manifests = manifestRepository.listByPickUpTimeAndShippingFeeTemplateType(pickUpTimeFrom, pickUpTimeTo, shippingFeeTemplateTypes);
        }
        if (CollUtil.isEmpty(manifests)) {
            return Collections.emptyList();
        }
        ids = manifests.stream().map(Manifest::getId).collect(Collectors.toList());
        List<ManifestItem> manifestItems = manifestItemService.listBatchByManifestIds(ids);
        Map<Long, List<ManifestItem>> manifestIdMapItem = manifestItems.stream().collect(Collectors.groupingBy(ManifestItem::getManifestId));

        return manifests.stream()
                .map(manifest -> ManifestAggregate
                        .builder()
                        .manifest(manifest)
                        .manifestItems(manifestIdMapItem.get(manifest.getId())).build())
                .collect(Collectors.toList());
    }

    private List<ManifestAggregate> getManifestAggregateByIdsSort(List<Long> ids) {
        List<Manifest> manifests = manifestRepository.getByIds(ids);
        if (CollUtil.isEmpty(manifests)) {
            return Collections.emptyList();
        }
        List<ManifestItem> manifestItems = manifestItemService.listBatchByManifestIds(ids);
        Map<Long, List<ManifestItem>> manifestIdMapItem = manifestItems.stream().collect(Collectors.groupingBy(ManifestItem::getManifestId));
        Comparator<Manifest> numericStringComparator = (o1, o2) -> {
            try {
                // Assuming the strings are valid numbers
                return Integer.compare(Integer.parseInt(o1.getOrderNumber()), Integer.parseInt(o2.getOrderNumber()));
            } catch (NumberFormatException e) {
                // Handle the case where strings are not valid numbers
                // You can decide how to handle this, e.g., consider them as 0 or throw an exception
                return 0;
            }
        };
        return manifests.stream()
                .sorted(numericStringComparator)
                .map(manifest -> ManifestAggregate
                        .builder()
                        .manifest(manifest)
                        .manifestItems(manifestIdMapItem.get(manifest.getId())).build())
                .collect(Collectors.toList());
    }

    @Override
    public void generateWayBillImage(ManifestGenWayBillImageReq req, HttpServletResponse httpServletResponse) {
        // 校验电话号码
        String phone = manifestDataValidateService.formatAndValidateJpMobile(req.getReceiverPhone());
        req.setReceiverPhone(phone);
        if (ObjUtil.isNull(phone)) {
            throw new ServiceException("录入失败：收件人电话号码格式错误");
        }
        // 格式化邮编
        String zipCode = req.getReceiverZipCode().replaceAll(" ", "").replaceAll("-", "");
        // 校验邮编
        if (!manifestDataValidateService.validateJpZipCode(zipCode)) {
            throw new ServiceException("录入失败：邮编错误");
        }

        // 根据邮编获取地址
        ReceiverAreaEntity receiverAreaEntity = receiverAreaService.getReceiverArea(zipCode, req.getReceiverAddress());
        SawagaSitePO sawagaSite = sawagaSiteService.getSawagaSite(receiverAreaEntity.getPrefectureName(), receiverAreaEntity.getMunicipalName());
        if (sawagaSite == null) {
            throw new ServiceException("未匹配到对应店番号");
        }
        // 获取英文地址
        String receiverEnAddress = receiverAreaService.getReceiverEnAddress(receiverAreaEntity, req.getReceiverAddress());

        // 先查询运单信息
        Manifest manifest = manifestRepository.getByExpressNumber(req.getExpressNumber());
        if (ObjUtil.isNull(manifest)) {
            throw new ServiceException("当前订单不存在");
        }
        List<ManifestItem> manifestItems = manifestItemService.listByManifestId(manifest.getId());
        WayBillImageGenDTO dto = WayBillImageGenDTO.builder()
                .shippingFeeTemplateType(req.getShippingFeeTemplateType())
                .sawagaNumber(manifest.getTrackingNumber())
                .orderNo(manifest.getOrderNo())
                .packageNo(sawagaSite.getSiteCode())
                .itemEnNames(manifestItems.stream().map(manifestItem -> translationUtil.translateFromChineseToEnglish(manifestItem.getName())).collect(Collectors.joining(",")))
                .receiverName(req.getReceiverName())
                .receiverAddress(req.getReceiverAddress())
                .receiverPhone(req.getReceiverPhone())
                .receiverZipCode(zipCode)
                .receiverEnAddress(receiverEnAddress).build();

        wayBillImageService.generateWayBillImage(httpServletResponse, dto);
    }

    @Override
    public ManifestOrderDTO getByExpressNumber(String expressNumber) {
        // 查询运单对象
        Manifest manifest = manifestRepository.getByExpressNumber(expressNumber);
        if (ObjUtil.isNull(manifest)) {
            return null;
        }
        if (manifest.getIsDelete()) {
            throw new ServiceException("该订单已被废弃");
        }
        return transferToOrderDTO(manifest);
    }

    @NotNull
    private ManifestOrderDTO transferToOrderDTO(Manifest manifest) {
        String masterBillNumber = "";
        if (manifest.getMasterBillId() != null) {
            MasterBillDetailRes detail = masterBillService.getDetail(manifest.getMasterBillId());
            if (detail != null) {
                masterBillNumber = detail.getMasterBillNumber();
            }
        }
        // 查询单号类别
        Long locationId = null;
        Long shipmentTypeId = null;
        if (StrUtil.isNotBlank(manifest.getExpressNumber())) {
            Optional<TrackingNumberPool> poolOptional = trackingNumberPoolRepository.findByTrackingNumber(manifest.getExpressNumber());
            if (poolOptional.isPresent()) {
                TrackingNumberPool poolEntry = poolOptional.get();
                Long channelId = poolEntry.getChannelId();
                if (channelId != null) {
                    Optional<TrackingNumberChannel> channelOptional = trackingNumberChannelRepository.findById(channelId);
                    if (channelOptional.isPresent()) {
                        TrackingNumberChannel channel = channelOptional.get();
                        locationId = channel.getLocationId();
                        shipmentTypeId = channel.getShipmentTypeId();
                    }
                }
            }
        }

        UserEntity user = userService.getById(manifest.getUserId());

        ManifestOrderDTO manifestOrderDTO = ManifestOrderDTO.builder()
                .id(manifest.getId())
                .expressNumber(manifest.getExpressNumber())
                .sawagaNumber(manifest.getSawagaNumber())
                .transferredTrackingNumber(manifest.getTransferredTrackingNumber())
                .orderNumber(manifest.getOrderNumber())
                .orderNo(manifest.getOrderNo())
                .receiverName(manifest.getReceiverName())
                .receiverAddress(manifest.getReceiverAddress())
                .receiverPhone(manifest.getReceiverPhone())
                .receiverZipCode(manifest.getReceiverZipCode())
                .cost(manifest.getCost())
                .overLengthSurcharge(manifest.getOverLengthSurcharge())
                .remoteAreaSurcharge(manifest.getRemoteAreaSurcharge())
                .otherCostName(manifest.getOtherCostName())
                .otherCost(manifest.getOtherCost())
                .weight(manifest.getWeight())
                .length(manifest.getLength())
                .width(manifest.getWidth())
                .height(manifest.getHeight())
                .dimensionWeight(manifest.getDimensionalWeight())
                .masterBillId(manifest.getMasterBillId())
                .masterBillNumber(masterBillNumber)
                .shippingFeeTemplateType(manifest.getShippingFeeTemplateType())
                .remark(manifest.getRemark())
                .userId(manifest.getUserId())
                .userNickname(user.getNickname())
                .status(manifest.getStatus())
                .locationId(locationId)
                .shipmentTypeId(shipmentTypeId)
                .build();

        // 查询对应物品
        List<ManifestItem> manifestItems = manifestItemService.listByManifestId(manifest.getId());

        // 处理物品列表，检查违禁词并查找映射
        List<ManifestItemOrderDTO> itemSimpleDTOList = manifestItems.stream()
                .map(item -> {
                    String originalItemName = item.getName(); // 获取原始名称
                    // 去除前后空格用于检查，但保留原始名称用于映射查找
                    String trimmedItemName = originalItemName != null ? originalItemName.trim() : "";

                    // 1. 检查违禁词 (使用 trim 后的名称)
                    String detectedKeyword = prohibitedItemKeywordService.checkItemName(trimmedItemName);
                    boolean isProhibited = detectedKeyword != null;
                    String mappedName = null;

                    // 2. 尝试查找映射 (使用未经 trim 的原始名称)
                    if (originalItemName != null) {
                        mappedName = itemNameMappingService.findAndUseActiveMappedName(originalItemName)
                                .orElse(null); // 如果没有找到映射，则为 null
                    }

                    // 3. 构建 DTO
                    return ManifestItemOrderDTO.builder()
                            .name(originalItemName) // 设置原始名称
                            .quantity(item.getQuantity())
                            .weight(item.getWeight())
                            .price(item.getPrice())
                            .isProhibited(isProhibited) // 设置是否违禁
                            .detectedKeyword(detectedKeyword) // 设置检测到的违禁词
                            .mappedName(mappedName) // 设置映射后的名称
                            .build();
                })
                .collect(Collectors.toList());

        manifestOrderDTO.setManifestItems(itemSimpleDTOList);
        return manifestOrderDTO;
    }

    @Override
    public List<String> fuzzySearchExpressNumber(ManifestFuzzySearchReq req) {
        return manifestRepository.fuzzySearchExpressNumber(req.getExpressNumber(), 1, 10);
    }

    @Override
    public PageResult<ManifestOrderDTO> searchOrder(ManifestOrderSearchReq req) {
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        Long userId = loginUser.ifAdmin() ? req.getUserId() : loginUser.getId();
        PageResult<Manifest> manifestPageResult = manifestRepository.searchOrder(
                req.getKeyword(), req.getExpressNumber(), req.getSawagaNumber(), req.getOrderNumber(),
                req.getOrderNo(), req.getReceiverName(), req.getItemName(), req.getStatus(), userId,
                req.getCreateTimeStart(), req.getCreateTimeEnd(), req.getPickUpTimeStart(), req.getPickUpTimeEnd(),
                req.getShipmentTimeStart(), req.getShipmentTimeEnd(), req.getPageNo(), req.getPageSize(), req.getIsDelete()
        );
        List<Manifest> manifests = manifestPageResult.getList();
        if (CollUtil.isEmpty(manifests)) {
            // 使用正确的构造函数
            return new PageResult<>(manifestPageResult.getTotal() != null ? manifestPageResult.getTotal() : 0L);
        }
        List<Long> manifestIds = new ArrayList<>();
        Set<Long> userIds = new HashSet<>();
        manifests.forEach(manifest -> {
            manifestIds.add(manifest.getId());
            userIds.add(manifest.getUserId());
        });
        List<ManifestItem> manifestItems = manifestItemService.listBatchByManifestIds(manifestIds);
        List<UserEntity> userEntities = userService.listBatchByIds(userIds);
        Map<Long, String> idNicknameMap = userEntities.stream().collect(Collectors.toMap(UserEntity::getId, UserEntity::getNickname));
        Map<Long, List<ManifestItem>> manifestIdToManifestItemMap = manifestItems.stream()
                .collect(Collectors.groupingBy(ManifestItem::getManifestId));

        List<ManifestOrderDTO> orderDTOList = manifests.stream().map(manifest -> {
            ManifestOrderDTO manifestOrderDTO = ManifestAdapter.INSTANCE.entity2OrderDTO(manifest);
            List<ManifestItem> itemsForThisManifest = manifestIdToManifestItemMap.getOrDefault(manifestOrderDTO.getId(), Collections.emptyList());
            List<ManifestItemOrderDTO> itemOrderDTOList = itemsForThisManifest.stream()
                    .map(item -> {
                        String originalItemName = item.getName();
                        String trimmedItemName = originalItemName != null ? originalItemName.trim() : "";
                        String detectedKeyword = prohibitedItemKeywordService.checkItemName(trimmedItemName);
                        boolean isProhibited = detectedKeyword != null;
                        String mappedName = null;
                        if (isProhibited && originalItemName != null) {
                            mappedName = itemNameMappingService.findAndUseActiveMappedName(originalItemName)
                                    .orElse(null);
                        }
                        return ManifestItemOrderDTO.builder()
                                .name(originalItemName) // 使用 originalName
                                .quantity(item.getQuantity())
                                .weight(item.getWeight())
                                .price(item.getPrice())
                                .isProhibited(isProhibited)
                                .detectedKeyword(detectedKeyword)
                                .mappedName(mappedName)
                                .build();
                    })
                    .collect(Collectors.toList());

            manifestOrderDTO.setManifestItems(itemOrderDTOList);
            manifestOrderDTO.setUserNickname(idNicknameMap.get(manifest.getUserId()));
            return manifestOrderDTO;
        }).collect(Collectors.toList());
        // 使用正确的构造函数
        return new PageResult<>(orderDTOList, manifestPageResult.getTotal() != null ? manifestPageResult.getTotal() : 0L);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(ManifestAddReq req) {
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        Long operatorId = loginUser.getId();
        String operatorName = loginUser.getNickname();
        String ipAddress = null; // TODO: 获取 IP 地址
        String context = "后台管理-手动录入运单";

        if (ObjUtil.isNull(req.getUserId())) {
            req.setUserId(loginUser.getId());
        }
        // 校验电话号码
        String phone = manifestDataValidateService.formatAndValidateJpMobile(req.getReceiverPhone());
        req.setReceiverPhone(phone);
        if (ObjUtil.isNull(phone)) {
            throw new ServiceException("录入失败：收件人电话号码格式错误");
        }
        // 格式化邮编
        String zipCode = req.getZipCode().replaceAll(" ", "").replaceAll("-", "");
        // 校验邮编
        if (!manifestDataValidateService.validateJpZipCode(zipCode)) {
            throw new ServiceException("录入失败：邮编错误");
        }
        UserEntity user = userService.getById(req.getUserId());
        Integer sourceType;
        if (user.ifAdmin()) {
            sourceType = SourceType.ADMIN.getCode();
        } else if (user.ifLocalUser()) {
            sourceType = SourceType.LOCAL_USER.getCode();
        } else if (user.ifNetworkUser()) {
            sourceType = SourceType.NETWORK_USER.getCode();
        } else {
            throw new ServiceException("用户角色信息错误，请联系管理员");
        }

        // 查询是否已经存在相同运单号的订单
        Manifest manifestInDb = manifestRepository.getByExpressNumber(req.getExpressNumber());
        if (ObjUtil.isNotNull(manifestInDb)) {
            // 如果已经存在且未揽件，则执行更新
            if (manifestInDb.ifPickedUp()) {
                throw new ServiceException("录入失败：物流单号已存在且已揽件，无法更新");
            }
            if (!manifestInDb.getUserId().equals(user.getId()) && !user.ifAdmin()) {
                throw new ServiceException("录入失败：权限不足");
            }
            manifestInDb.setSawagaNumber(req.getExpressNumber());
            manifestInDb.setOrderNumber(req.getOrderNumber().toUpperCase());
            manifestInDb.setReceiverName(req.getReceiverName());
            manifestInDb.setReceiverAddress(req.getReceiverAddress());
            manifestInDb.setReceiverPhone(req.getReceiverPhone());
            manifestInDb.setReceiverZipCode(zipCode);
            manifestInDb.setUserId(req.getUserId());
            manifestInDb.setSourceType(sourceType);
            manifestRepository.updateById(manifestInDb);

            List<ManifestItem> manifestItems = req.getItems().stream()
                    .map(item -> ManifestItem.builder()
                            .manifestId(manifestInDb.getId()) // 使用已存在的 manifestId
                            .name(item.getName())
                            .nameEn(translationUtil.translateFromChineseToEnglish(item.getName()))
                            .weight(item.getWeight())
                            .quantity(item.getQuantity())
                            .price(item.getPrice()).build())
                    .collect(Collectors.toList());

            // 插入数据库
            if (!manifestItems.isEmpty()) { // 检查列表是否为空
                manifestItemService.deleteByManifestId(manifestInDb.getId());
                manifestItemService.insertBatch(manifestItems);
            }

            // --- 添加日志记录 ---
            try {
                ManifestAggregate createdAggregate = ManifestAggregate.builder()
                        .manifest(manifestInDb) // 使用已包含 ID 的 manifest 对象
                        .manifestItems(manifestItems)
                        .build();
                manifestLogHelper.recordManualManifestCreationLog(createdAggregate, operatorId, operatorName, ipAddress, context);
            } catch (Exception logEx) {
                // 日志记录失败不应影响主流程
                log.error("记录手动创建运单日志失败: manifestId={}, expressNumber={}", manifestInDb.getId(), manifestInDb.getExpressNumber(), logEx);
            }
            // --- 日志记录结束 ---
            return true;
        }
        String sawagaNumber = req.getExpressNumber();

        // 如果是网络用户，需要分配一个佐川单号
        if (user.ifNetworkUser()) {
            try {
                sawagaNumber = expressNumberService.getSawagaNumber();
            } catch (ServiceException e) {
                throw new ServiceException("录入失败：系统内部物流单号库存不足，请联系管理员");
            }
        }
        String finalSawagaNumber = sawagaNumber;

        // 创建运单对象
        Manifest manifest = Manifest.builder()
                .expressNumber(req.getExpressNumber())
                .sawagaNumber(finalSawagaNumber)
                .orderNumber(req.getOrderNumber().toUpperCase())
                .orderNo(expressNumberService.getOrderNumber(finalSawagaNumber))
                .receiverName(req.getReceiverName())
                .receiverAddress(req.getReceiverAddress())
                .receiverPhone(req.getReceiverPhone())
                .receiverZipCode(zipCode)
                .userId(req.getUserId())
                .status(ManifestStatus.PENDING_PICKUP.getCode())
                .creatorId(operatorId) // 使用获取到的 operatorId
                .sourceType(sourceType)
                .isOnline(false)
                .isDelete(false).build();
        // 插入数据库
        manifestRepository.insert(manifest); // 假设 insert 后 manifest 对象会包含 ID

        // 检查 manifest.getId() 是否为空，以防万一
        if (manifest.getId() == null) {
            log.error("Manifest 保存后未能获取到 ID，无法记录日志: expressNumber={}", manifest.getExpressNumber());
            // 根据业务决定是否抛出异常或仅返回 false
            throw new ServiceException("保存运单失败，无法获取 ID");
        }

        List<ManifestItem> manifestItems = req.getItems().stream()
                .map(item -> ManifestItem.builder()
                        .manifestId(manifest.getId()) // 使用已存在的 manifestId
                        .name(item.getName())
                        .nameEn(translationUtil.translateFromChineseToEnglish(item.getName()))
                        .weight(item.getWeight())
                        .quantity(item.getQuantity())
                        .price(item.getPrice()).build())
                .collect(Collectors.toList());

        // 插入数据库
        if (!manifestItems.isEmpty()) { // 检查列表是否为空
            manifestItemService.insertBatch(manifestItems);
        }

        // --- 添加日志记录 --- 
        try {
            ManifestAggregate createdAggregate = ManifestAggregate.builder()
                    .manifest(manifest) // 使用已包含 ID 的 manifest 对象
                    .manifestItems(manifestItems)
                    .build();
            manifestLogHelper.recordManualManifestCreationLog(createdAggregate, operatorId, operatorName, ipAddress, context);
        } catch (Exception logEx) {
            // 日志记录失败不应影响主流程
            log.error("记录手动创建运单日志失败: manifestId={}, expressNumber={}", manifest.getId(), manifest.getExpressNumber(), logEx);
        }
        // --- 日志记录结束 --- 

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addWithAllocatedExpress(ManifestAddWithAllocatedExpressReq req) {
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        Long operatorId = loginUser.getId();
        String operatorName = loginUser.getNickname();
        String ipAddress = null; // TODO: 获取 IP 地址
        String context = "外部系统API-录入运单";

        if (ObjUtil.isNull(req.getUserId())) {
            req.setUserId(loginUser.getId());
        }

        // 校验电话号码
        String phone = manifestDataValidateService.formatAndValidateJpMobile(req.getReceiverPhone());
        req.setReceiverPhone(phone);
        if (ObjUtil.isNull(phone)) {
            throw new ServiceException("录入失败：收件人电话号码格式错误");
        }

        // 格式化邮编
        String zipCode = req.getZipCode().replaceAll(" ", "").replaceAll("-", "");
        // 校验邮编
        if (!manifestDataValidateService.validateJpZipCode(zipCode)) {
            throw new ServiceException("录入失败：邮编错误");
        }

        UserEntity user = userService.getById(req.getUserId());
        Integer sourceType;
        if (user.ifAdmin()) {
            sourceType = SourceType.ADMIN.getCode();
        } else if (user.ifLocalUser()) {
            sourceType = SourceType.LOCAL_USER.getCode();
        } else if (user.ifNetworkUser()) {
            sourceType = SourceType.NETWORK_USER.getCode();
        } else {
            throw new ServiceException("用户角色信息错误，请联系管理员");
        }

        // 从系统分配单号
        AllocateTrackingNumberReq allocateReq = new AllocateTrackingNumberReq();
        allocateReq.setLocationId(req.getLocationId());
        allocateReq.setShipmentTypeId(req.getShipmentTypeId());
        allocateReq.setCustomerAccountId(req.getUserId());
        allocateReq.setQuantity(1);

        TrackingNumberAllocationResultDTO allocationResult = trackingNumberService.allocateTrackingNumbers(allocateReq);
        if (allocationResult == null || allocationResult.getAllocationBatch() == null) {
            throw new ServiceException("录入失败：无法分配运单号");
        }

        // 获取分配的单号
        List<TrackingNumberPool> allocatedNumbers = trackingNumberService.findAllocatedNumbersByBatchId(
                allocationResult.getAllocationBatch().getId());
        if (CollUtil.isEmpty(allocatedNumbers)) {
            throw new ServiceException("录入失败：获取分配的运单号失败");
        }

        String allocatedExpressNumber = allocatedNumbers.get(0).getTrackingNumber();
        String sawagaNumber = allocatedExpressNumber;

        // 如果是网络用户，需要分配一个佐川单号
        if (user.ifNetworkUser()) {
            try {
                sawagaNumber = expressNumberService.getSawagaNumber();
            } catch (ServiceException e) {
                throw new ServiceException("录入失败：系统内部物流单号库存不足，请联系管理员");
            }
        }
        String finalSawagaNumber = sawagaNumber;

        // 创建运单对象
        Manifest manifest = Manifest.builder()
                .expressNumber(allocatedExpressNumber)
                .sawagaNumber(finalSawagaNumber)
                .orderNumber(req.getOrderNumber().toUpperCase())
                .orderNo(expressNumberService.getOrderNumber(finalSawagaNumber))
                .receiverName(req.getReceiverName())
                .receiverAddress(req.getReceiverAddress())
                .receiverPhone(req.getReceiverPhone())
                .receiverZipCode(zipCode)
                .userId(req.getUserId())
                .status(ManifestStatus.PENDING_PICKUP.getCode())
                .creatorId(operatorId)
                .sourceType(sourceType)
                .isOnline(false)
                .isDelete(false).build();

        // 插入数据库
        manifestRepository.insert(manifest);

        // 检查 manifest.getId() 是否为空，以防万一
        if (manifest.getId() == null) {
            log.error("Manifest 保存后未能获取到 ID，无法记录日志: expressNumber={}", manifest.getExpressNumber());
            throw new ServiceException("保存运单失败，无法获取 ID");
        }

        List<ManifestItem> manifestItems = req.getItems().stream()
                .map(item -> ManifestItem.builder()
                        .manifestId(manifest.getId())
                        .name(item.getName())
                        .nameEn(translationUtil.translateFromChineseToEnglish(item.getName()))
                        .weight(item.getWeight())
                        .quantity(item.getQuantity())
                        .price(item.getPrice()).build())
                .collect(Collectors.toList());

        // 插入数据库
        if (!manifestItems.isEmpty()) {
            manifestItemService.insertBatch(manifestItems);
        }

        // --- 添加日志记录 ---
        try {
            ManifestAggregate createdAggregate = ManifestAggregate.builder()
                    .manifest(manifest)
                    .manifestItems(manifestItems)
                    .build();
            manifestLogHelper.recordManualManifestCreationLog(createdAggregate, operatorId, operatorName, ipAddress, context);
        } catch (Exception logEx) {
            // 日志记录失败不应影响主流程
            log.error("记录外部系统创建运单日志失败: manifestId={}, expressNumber={}", manifest.getId(), manifest.getExpressNumber(), logEx);
        }
        // --- 日志记录结束 ---

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pickup(ManifestPickupReq req) {
        long start = System.currentTimeMillis();
        log.info("管理员揽件，运单号：{}", req.getExpressNumber());
        userService.validateAdmin();
        // 校验电话号码
        String phone = manifestDataValidateService.formatAndValidateJpMobile(req.getReceiverPhone());
        req.setReceiverPhone(phone);
        if (ObjUtil.isNull(phone)) {
            throw new ServiceException("录入失败：收件人电话号码格式错误");
        }
        // 格式化邮编
        String zipCode = req.getReceiverZipCode().replaceAll(" ", "").replaceAll("-", "");
        // 校验邮编
        if (!manifestDataValidateService.validateJpZipCode(zipCode)) {
            throw new ServiceException("邮编错误");
        }

        // --- 开始：添加物品名称违禁词校验 ---
        if (CollUtil.isNotEmpty(req.getManifestItems())) {
            for (ManifestItemOrderDTO item : req.getManifestItems()) {
                // 检查物品名称是否为空
                if (StrUtil.isBlank(item.getName())) {
                    log.warn("揽件请求中发现物品名称为空，运单ID: {}", req.getId());
                    // 根据业务需求决定是否抛出异常
                    // throw new ServiceException("揽件失败：存在物品名称为空");
                    continue; // 或者跳过空名称的检查
                }

                // 去除前后空格进行检查
                String trimmedItemName = item.getName().trim();
                String detectedKeyword = prohibitedItemKeywordService.checkItemName(trimmedItemName);

                if (detectedKeyword != null) {
                    // 发现违禁词，抛出异常，阻止揽件
                    log.warn("揽件失败，运单ID: {}, 物品: '{}', 违禁词: '{}'", req.getId(), item.getName(), detectedKeyword);
                    throw new ServiceException("揽件失败：物品名称 '" + item.getName() + "' 包含违禁关键词 '" + detectedKeyword + "'");
                }
                // 注意：这里只检查是否包含违禁词。如果包含，则直接失败。
                // 如果你的业务逻辑是：即使包含违禁词，但如果存在有效映射，则允许揽件（并可能使用映射后名称），
                // 那么这里的逻辑需要修改，可能需要结合 itemNameMappingService 的检查。
                // 当前实现是：只要包含违禁词就阻止揽件。
            }
        } else {
            // 根据业务需求决定是否必须有物品列表
            log.warn("揽件请求中物品列表为空，运单ID: {}", req.getId());
            // throw new ServiceException("揽件失败：物品列表不能为空"); // 如果要求列表不能为空则取消注释
        }
        // --- 结束：添加物品名称违禁词校验 ---


        // 查询运单
        Manifest manifest = manifestRepository.getById(req.getId());
        // --- 获取操作前的日志数据 ---
        Map<String, Object> beforeValues = preparePickupLogBeforeValues(manifest);
        // --- 结束：获取操作前的日志数据 ---

        if (manifest.getIsDelete()) {
            throw new ServiceException("该订单已被废弃，无法揽件");
        }
        if (manifest.ifShipped()) {
            throw new ServiceException("该运单已发货，无法再次被揽件");
        }
        
        // 如果运单已经是揽件状态，需要校验确认密码
        if (manifest.ifPickedUp() && !manifest.ifShipped()) {
            if (StrUtil.isBlank(req.getConfirmPassword())) {
                throw new ServiceException("该运单已揽件，重新揽件需要输入确认密码");
            }
            
            // 获取当前登录用户并验证密码
            LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
            UserEntity currentUser = userService.getById(loginUser.getId());
            if (!currentUser.validatePassword(req.getConfirmPassword())) {
                throw new ServiceException("确认密码错误，无法重新揽件");
            }
            
            log.info("用户 {} 确认重新揽件，运单号：{}", loginUser.getNickname(), req.getExpressNumber());
        }

        req.setReceiverZipCode(zipCode);
        ManifestUpdateByIdDTO manifestUpdateByIdDTO = ManifestAdapter.INSTANCE.pickUpReq2UpdateByIdDTO(req);
        manifestUpdateByIdDTO.setStatus(ManifestStatus.PICKED_UP.getCode());
        manifestUpdateByIdDTO.setPickUpTime(LocalDateTime.now());
        manifestUpdateByIdDTO.setTrackingStatus(TrackingStatus.PICKED_UP.getValue());
        manifestUpdateByIdDTO.setTrackingUpdateTime(LocalDateTime.now());
        manifestUpdateByIdDTO.setTransferredTrackingNumber(req.getTransferredTrackingNumber());
        manifestUpdateByIdDTO.setMasterBillId(req.getMasterBillId());
        manifestUpdateByIdDTO.setMasterBillNumber(req.getMasterBillNumber());

        // --- 传递日志参数 --- START
        updateManifestById(manifestUpdateByIdDTO, manifest, LoginUserHolder.getLoginUser().getId(), LoginUserHolder.getLoginUser().getNickname(), "后台管理-揽件", null, beforeValues);
        // --- 传递日志参数 --- END

        // --- 移除调用 recordPickupOperation 的代码 --- START
        /* // 这个 try-catch 块需要被删除
        try {
            manifestLogHelper.recordPickupOperation(
                manifest,     // 传入更新后的 manifest 对象
                req,          // 传入原始请求对象
                beforeValues, // 传入操作前的值
                operatorId,
                operatorName,
                null, // ipAddress
                context  // <--- 传入 context
            );
        } catch (Exception logEx) {
             log.error("记录揽件业务日志失败: manifestId={}, expressNumber={}",
                     manifest.getId(),
                     manifest.getExpressNumber(),
                       logEx);
        }
        */
        // --- 移除调用 recordPickupOperation 的代码 --- END

        // --- 保留并调整 Tracking 逻辑 (在 updateManifestById 之后执行) ---
        // 新增一条轨迹，表示已揽收
        Tracking tracking = Tracking.builder()
                .manifestId(req.getId())
                .operatorId(LoginUserHolder.getLoginUser().getId())
                .status(TrackingStatus.PICKED_UP.getValue())
                .track(TrackingStatus.PICKED_UP.getDescription())
                .build();
        trackingService.save(tracking);
        // --- 移除旧的 OperationLogService 调用 --- START
        /*
        OperationLogEntity operationLogEntity = OperationLogEntity.builder()
             // ...
                .build();
        operationLogService.log(operationLogEntity);
        */
        // --- 移除旧的 OperationLogService 调用 --- END

        log.info("管理员揽件，运单号：{}，耗时：{}ms", req.getExpressNumber(), System.currentTimeMillis() - start);
        return true;
    }

    private void updateManifestById(ManifestUpdateByIdDTO dto, Manifest manifest,
                                    Long operatorId, String operatorName, String context, String ipAddress,
                                    Map<String, Object> beforeValues) { // <--- 添加 beforeValues 参数
        // --- 1. 移除操作前的状态记录 --- START
        /* // 这部分代码将被移除
        Map<String, Object> beforeValues = new HashMap<>();
        Manifest originalManifest;
        if (manifest != null) {
             try {
                 originalManifest = ObjectUtil.cloneByStream(manifest); // 尝试深拷贝
                 if (originalManifest != null) {
                     beforeValues = manifestLogHelper.extractManifestStateMap(originalManifest);
                     try {
                         List<ManifestItem> beforeItems = manifestItemService.listByManifestId(originalManifest.getId());
                         beforeValues.put("manifestItems", beforeItems);
                     } catch (Exception itemEx) {
                         log.error("日志记录：查询操作前物品列表失败, manifestId: {}", originalManifest.getId(), itemEx);
                         beforeValues.put("manifestItemsError", "Failed to retrieve original items: " + itemEx.getMessage());
                     }
                 } else {
                     log.warn("克隆 Manifest 失败，无法记录 beforeValues, manifestId: {}", manifest.getId());
                     // 降级记录
                     beforeValues.put("manifestId_before", manifest.getId());
                     beforeValues.put("status_before", manifest.getStatus());
                 }
             } catch (Exception cloneEx) {
                  log.error("克隆 Manifest 对象失败, manifestId: {}", manifest != null ? manifest.getId() : "null", cloneEx);
                  beforeValues.put("error", "Failed to clone original manifest state.");
             }
        } else {
             log.warn("updateManifestById 接收到的 manifest 为 null，无法记录 beforeValues");
             // 如果 manifest 为 null，后续会 N P E，这里提前处理或让其失败
             throw new IllegalArgumentException("Manifest to update cannot be null");
        }
        */
        // --- 1. 移除操作前的状态记录 --- END

        // --- 2. 执行核心更新逻辑 --- START
        // 更新运单基本信息 (从 DTO)
        manifest.setExpressNumber(dto.getExpressNumber());
        manifest.setSawagaNumber(dto.getSawagaNumber());
        manifest.setOrderNumber(dto.getOrderNumber() != null ? dto.getOrderNumber().toUpperCase() : null);
        manifest.setTransferredTrackingNumber(dto.getTransferredTrackingNumber());
        manifest.setReceiverName(dto.getReceiverName());
        manifest.setReceiverAddress(dto.getReceiverAddress());
        manifest.setReceiverPhone(dto.getReceiverPhone());
        manifest.setLength(dto.getLength());
        manifest.setWidth(dto.getWidth());
        manifest.setHeight(dto.getHeight());
        manifest.setWeight(dto.getWeight());
        manifest.setCost(dto.getCost());
        manifest.setOverLengthSurcharge(dto.getOverLengthSurcharge());
        manifest.setRemoteAreaSurcharge(dto.getRemoteAreaSurcharge());
        manifest.setOtherCostName(dto.getOtherCostName());
        manifest.setOtherCost(dto.getOtherCost());
        manifest.setShippingFeeTemplateType(dto.getShippingFeeTemplateType());
        manifest.setDimensionalWeight(dto.getDimensionalWeight());
        manifest.setReceiverZipCode(dto.getReceiverZipCode());
        manifest.setStatus(dto.getStatus());
        manifest.setPickUpTime(dto.getPickUpTime());
        manifest.setTrackingStatus(dto.getTrackingStatus());
        manifest.setTrackingUpdateTime(dto.getTrackingUpdateTime());
        manifest.setMasterBillId(dto.getMasterBillId()); // 从 DTO 获取提单信息
        manifest.setMasterBillNumber(dto.getMasterBillNumber());
        manifest.setPickUpBy(operatorId); // 设置操作者

        // 根据模板类型调整 OrderNo (保留原有逻辑)
        if (manifest.getOrderNo() != null) { // 添加 null 检查
            if (dto.getShippingFeeTemplateType() != null &&
                    (dto.getShippingFeeTemplateType().equals(ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode()) ||
                            dto.getShippingFeeTemplateType().equals(ShippingFeeTemplateTypeEnum.SMALL_PARCEL_OSAKA.getCode()))) {
                manifest.setOrderNo(manifest.getOrderNo().replaceAll("BM", "SF"));
            } else if (manifest.getOrderNo().startsWith("SF")) {
                manifest.setOrderNo(manifest.getOrderNo().replaceAll("SF", "BM"));
            }
        }

        // 翻译和地址处理 (保留原有逻辑)
        try {
            manifest.setReceiverEnName(translationUtil.translateFromJapaneseToEnglish(manifest.getReceiverName()).toUpperCase());
        } catch (Exception e) {
            log.error("翻译收件人姓名失败", e); /* 处理或忽略 */
        }

        try {
            List<SawagaSiteCodeEntity> sawagaSiteCodeEntities = sawagaSiteService.getByZipCode(dto.getReceiverZipCode());
            if (CollUtil.isNotEmpty(sawagaSiteCodeEntities)) {
                SawagaSiteCodeEntity sawagaSiteCodeEntity = sawagaSiteCodeEntities.get(0);
                manifest.setSawagaSiteCode(sawagaSiteCodeEntity.acquireCode());
            } else {
                log.warn("未找到邮编对应的佐川站点代码: {}", dto.getReceiverZipCode());
            }

            ReceiverAreaEntity receiverAreaEntity = receiverAreaService.getReceiverArea(dto.getReceiverZipCode(), dto.getReceiverAddress());
            if (receiverAreaEntity != null) {
                String receiverEnAddress = receiverAreaService.getReceiverEnAddress(receiverAreaEntity, dto.getReceiverAddress());
                manifest.setReceiverEnAddress(receiverEnAddress);
                manifest.setPrefectureName(receiverAreaEntity.getPrefectureName());
                manifest.setMunicipalName(receiverAreaEntity.getMunicipalName());
                manifest.setLocalitiesName(receiverAreaEntity.getLocalitiesName());
                manifest.setPrefectureEnName(receiverAreaEntity.getPrefectureEnName());
                manifest.setMunicipalEnName(receiverAreaEntity.getMunicipalEnName());
                manifest.setLocalitiesEnName(receiverAreaEntity.getLocalitiesEnName());

                SawagaSitePO sawagaSite = sawagaSiteService.getSawagaSite(receiverAreaEntity.getPrefectureName(), receiverAreaEntity.getMunicipalName());
                if (sawagaSite != null) {
                    manifest.setPackageNo(sawagaSite.getSiteCode());
                } else {
                    log.warn("未匹配到对应店番号 for prefecture: {}, municipal: {}", receiverAreaEntity.getPrefectureName(), receiverAreaEntity.getMunicipalName());
                }
            } else {
                log.warn("未找到邮编和地址对应的区域信息: zip={}, addr={}", dto.getReceiverZipCode(), dto.getReceiverAddress());
            }
        } catch (Exception e) {
            log.error("处理地址和站点信息时出错", e); /* 处理或忽略 */
        }

        // 处理物品列表 (保留原有逻辑，但确保 DTO 列表不为 null)
        List<ManifestItem> newManifestItems = Collections.emptyList(); // 初始化为空列表
        if (CollUtil.isNotEmpty(dto.getManifestItems())) {
            manifestItemService.deleteByManifestId(manifest.getId());

            BigDecimal valueFloor = BigDecimal.valueOf(250);
            BigDecimal valueCeil = BigDecimal.valueOf(600);
            BigDecimal averageValueFloor = valueFloor.divide(BigDecimal.valueOf(dto.getManifestItems().size()), 2, RoundingMode.HALF_UP);
            BigDecimal averageValueCeil = valueCeil.divide(BigDecimal.valueOf(dto.getManifestItems().size()), 2, RoundingMode.HALF_UP);
            final AtomicInteger totalValue = new AtomicInteger();
            AtomicReference<String> description = new AtomicReference<>("");

            newManifestItems = dto.getManifestItems().stream()
                    .map(itemDto -> {
                        int itemTotalValue = RandomUtil.randomInt(averageValueFloor.intValue(), averageValueCeil.intValue()) * 10;
                        totalValue.getAndAdd(itemTotalValue);
                        String originalItemName = itemDto.getName();
                        String itemEnName = ""; // 初始化
                        if (originalItemName != null) {
                            try {
                                itemEnName = translationUtil.translateFromChineseToEnglish(originalItemName);
                            } catch (Exception e) {
                                log.error("翻译物品名称失败: '{}'", originalItemName, e);
                            }
                            if (StrUtil.isNotBlank(itemEnName)) {
                                description.accumulateAndGet("," + itemEnName, (current, update) -> current + update);
                            }
                        }
                        return ManifestItem.builder()
                                .manifestId(manifest.getId())
                                .name(originalItemName)
                                .weight(itemDto.getWeight())
                                .quantity(itemDto.getQuantity())
                                .nameEn(itemEnName)
                                .price(itemDto.getPrice())
                                .value(BigDecimal.valueOf(itemTotalValue))
                                .build();
                    })
                    .collect(Collectors.toList());

            String finalDescription = description.get();
            manifest.setDescription(finalDescription.isEmpty() ? "" : finalDescription.substring(1));
            manifest.setValue(BigDecimal.valueOf(totalValue.get()));
        } else {
            // 如果请求中物品列表为空，是否需要清空数据库中的物品？
            manifestItemService.deleteByManifestId(manifest.getId());
            manifest.setDescription("");
            manifest.setValue(BigDecimal.ZERO);
        }
        // --- 2. 执行核心更新逻辑 --- END

        // --- 3. 记录操作后的状态和日志 --- START
        Map<String, Object> afterValues = manifestLogHelper.extractManifestStateMap(manifest);
        afterValues.put("manifestItems", newManifestItems); // 使用更新后的列表

        // 根据上下文区分调用不同的日志方法
        if ("后台管理-揽件".equals(context)) {
            manifestLogHelper.recordPickupLog(manifest.getId(), operatorId, operatorName, ipAddress, context, beforeValues, afterValues);
        } else { // 默认为更新或其他
            manifestLogHelper.recordUpdateLog(manifest.getId(), operatorId, operatorName, ipAddress, context, beforeValues, afterValues);
        }
        // --- 3. 记录操作后的状态和日志 --- END

        // --- 4. 更新数据库 --- START
        manifestRepository.updateById(manifest);
        if (CollUtil.isNotEmpty(newManifestItems)) {
            manifestItemService.insertBatch(newManifestItems);
        }
        // --- 4. 更新数据库 --- END
    }

    @Override
    public ManifestOrderDTO getOrderById(Long id) {
        Manifest manifest = manifestRepository.getById(id);
        if (manifest == null) {
            return null;
        }
        return transferToOrderDTO(manifest);
    }

    @Override
    public ManifestSearchDTO getById(Long id) {
        Manifest manifest = manifestRepository.getById(id);
        if (manifest == null) {
            return null;
        }
        return ManifestSearchDTO.of(manifest);
    }

    @Override
    public void genWayBillImageById(ManifestGenWayBillImageByIdReq req, HttpServletResponse httpServletResponse) {
        Manifest manifest = manifestRepository.getById(req.getId());
        if (manifest == null) {
            throw new ServiceException("订单不存在");
        }
        WayBillImageGenDTO dto = ManifestAdapter.INSTANCE.entity2WayBillImageGenDTO(manifest);
        dto.setSawagaNumber(manifest.getTrackingNumber());
        List<ManifestItem> manifestItems = manifestItemService.listByManifestId(manifest.getId());
        if (LoginUserHolder.getLoginUser().ifAdmin()) {
            dto.setItemEnNames(manifestItems.stream().map(ManifestItem::getNameEn).collect(Collectors.joining(",")));
            wayBillImageService.generateWayBillImage(httpServletResponse, dto);
        } else {
            dto.setItemNames(manifestItems.stream().map(ManifestItem::getName).collect(Collectors.joining(",")));
            dto.setItemQuantity(manifestItems.stream().mapToInt(ManifestItem::getQuantity).sum());
            generateSimpleWaybill(httpServletResponse, dto);
        }
    }

    private void generateSimpleWaybill(HttpServletResponse httpServletResponse, WayBillImageGenDTO dto) {
        try {
            BufferedImage bufferedImage = generateSimpleWaybill(dto, 780, 1060, 20, 15, 220);
            httpServletResponse.setContentType("image/png"); //通知浏览器以 jpeg 格式打开图片
            httpServletResponse.setHeader("Expires", "-1");   //联合使用以下三个响应消息头，控制浏览器不要缓存。
            httpServletResponse.setHeader("Cache-Control", "no-cache");
            httpServletResponse.setHeader("Pragma", "no-cache");
            try (OutputStream out = httpServletResponse.getOutputStream()) {
                ImageIO.write(bufferedImage, "png", out);
            }
        } catch (Exception e) {
            log.error("生成面单失败: ", e);
            throw new ServiceException("生成面单失败");
        }
    }

    private static BufferedImage generateSimpleWaybill(WayBillImageGenDTO dto, int width, int height, int cornerRadius, int margin, int marginBottom) throws Exception {
        BufferedImage image = new BufferedImage(width + 2 * margin, height + margin + marginBottom, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();

        // 启用反锯齿以获得更平滑的边缘
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 设置带有页边距的背景颜色-白色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width + 2 * margin, height + margin + marginBottom);

        // 创建带有页边距的圆角矩形
        RoundRectangle2D roundedRectangle = new RoundRectangle2D.Float(margin, margin, width, height, cornerRadius, cornerRadius);

        // 绘制带有页边距的圆角矩形边框，并使外框加粗
        // 边框为黑色
        g2d.setColor(Color.BLACK);

        // 定义线条粗细和外边框粗细
        float commonStrokeWidth = 1.2f;
        BasicStroke commonStroke = new BasicStroke(commonStrokeWidth);

        // 设置外框的粗细
        float strokeWidth = 2.0f;
        BasicStroke stroke = new BasicStroke(strokeWidth);
        // 边框加粗
        g2d.setStroke(stroke);
        g2d.draw(roundedRectangle);

        // 重置边框粗细
        g2d.setStroke(commonStroke);

        // 第一行 -----------------------------------------------------------------------------------------------------
//        int lineHeight = 55;
//        Line2D line = new Line2D.Float(margin, margin + lineHeight, width + margin, margin + lineHeight);
//        g2d.draw(line);
        // 36-702
        g2d.drawImage(BarcodeImageUtil.generateBarcodeImage(dto.getSawagaNumber(), BarcodeFormat.CODE_128, 453, 86), margin + 160, margin + 40, null);
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 36));
        g2d.drawString(dto.getSawagaNumber(), margin + 240, margin + 160);
        // 收件人姓名
        g2d.drawString("名前：" + dto.getReceiverName(), margin + 180, margin + 240);
        String address = dto.getReceiverAddress();
        int i = 0;
        if (address.length() > 10) {
            for (; i < address.length() / 10; i++) {
                String subAddress = address.substring(i * 10, Math.min(i * 10 + 10, address.length()));
                if (i == 0) {
                    subAddress = "住所：" + subAddress;
                } else {
                    subAddress = "          " + subAddress;
                }
                g2d.drawString(subAddress, margin + 180, margin + 290 + i * 50);
            }
        }
        g2d.drawString("電話番号：" + dto.getReceiverPhone(), margin + 180, margin + 290 + i * 60);
        g2d.drawString("郵便番号：" + dto.getReceiverZipCode(), margin + 180, margin + 290 + ++i * 60);
        g2d.drawString("品名：" + dto.getItemNames(), margin + 180, margin + 290 + ++i * 60);
        g2d.drawString("数量：" + dto.getItemQuantity() + "件", margin + 180, margin + 290 + ++i * 60);
        g2d.drawString("印刷時間：" + DateUtil.formatDateTime(new Date()), margin + 180, margin + 290 + ++i * 60);
        g2d.drawString(LoginUserHolder.getLoginUser().getUsername(), margin + 500, margin + 1000);

        // Dispose of the graphics context to free up resources
        g2d.dispose();
        return image;
    }

    @Override
    public Boolean updateById(ManifestUpdateByIdReq req) {
        userService.validateAdmin();
        // 校验电话号码
        String phone = manifestDataValidateService.formatAndValidateJpMobile(req.getReceiverPhone());
        req.setReceiverPhone(phone);
        if (ObjUtil.isNull(phone)) {
            throw new ServiceException("录入失败：收件人电话号码格式错误");
        }
        // 格式化邮编
        String zipCode = req.getReceiverZipCode().replaceAll(" ", "").replaceAll("-", "");
        // 校验邮编
        if (!manifestDataValidateService.validateJpZipCode(zipCode)) {
            throw new ServiceException("邮编错误");
        }

        // 查询运单
        Manifest manifest = manifestRepository.getById(req.getId());
        if (manifest == null) {
            throw new ServiceException("运单不存在");
        }
        if (manifest.ifShipped()) {
            throw new ServiceException("该运单已发货，无法修改信息");
        }

        // --- 开始：为 updateById 计算 beforeValues --- START
        Map<String, Object> beforeValues = preparePickupLogBeforeValues(manifest);
        // --- 结束：为 updateById 计算 beforeValues --- END

        // 获取操作员信息
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        Long operatorId = loginUser.getId();
        String operatorName = loginUser.getNickname();
        String context = "后台管理-更新运单"; // 定义上下文
        String ipAddress = null; // IP 地址暂不获取

        // 调用包含日志记录的私有方法
        ManifestUpdateByIdDTO dto = ManifestAdapter.INSTANCE.updateByIdReq2UpdateByIdDTO(req);
        // --- 确保传递所有参数 --- START
        updateManifestById(dto, manifest, operatorId, operatorName, context, ipAddress, beforeValues);
        // --- 确保传递所有参数 --- END

        // --- 移除旧的日志记录 --- START
        /*
        OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                .operatorId(LoginUserHolder.getLoginUser().getId())
                .operationType(OperationType.MANIFEST_UPDATE.getValue())
                .operation(OperationType.MANIFEST_UPDATE.getDescription())
                .manifestId(manifest.getId())
                .build();
        operationLogService.log(operationLogEntity);
        */
        // --- 移除旧的日志记录 --- END
        return true;
    }
    // --- updateById 方法修改 --- END

    @Override
    public void downloadImportTemplate(HttpServletResponse httpServletResponse) {
        InputStream inputStream = ResourceUtil.getStream("templates/UploadTemplate.xlsx");
        httpServletResponse.setContentType("application/vnd.ms-excel");
        httpServletResponse.setCharacterEncoding("utf-8");
        httpServletResponse.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncodeUtil.encode("上传模板") + ".xlsx");
        try (BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(httpServletResponse.getOutputStream())) {
            IoUtil.copy(inputStream, bufferedOutputStream);

            byte[] buffer = new byte[inputStream.available()];
            inputStream.read(buffer);
            inputStream.close();
            bufferedOutputStream.write(buffer);
            bufferedOutputStream.flush();
        } catch (IOException e) {
            throw new ServiceException("下载文件错误");
        }
    }

    @Override
    public List<Manifest> listByIds(Collection<Long> manifestIds) {
        return manifestRepository.getByIds(manifestIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean abandon(List<Long> manifestIds) {
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        // 对这些订单上锁
        List<RLock> locks = new ArrayList<>();
        manifestIds.forEach(manifestId -> {
            RLock lock = redissonClient.getLock(CacheConstants.MANIFEST_CHANGE_SHIPPING_LOCK + manifestId);
            try {
                if (!lock.tryLock(5, TimeUnit.SECONDS)) {
                    throw new ServiceException("当前订单中存在正在被修改的订单，请稍后重试");
                }
            } catch (InterruptedException e) {
                throw new ServiceException("当前订单中存在正在被修改的订单，请稍后重试");
            }
            locks.add(lock);
        });
        try {
            List<Manifest> manifests = manifestRepository.getByIds(manifestIds);
            if (CollUtil.isEmpty(manifests)) {
                return true;
            }

            // --- 开始：记录批量废弃日志 --- START
            String context = "后台管理-废弃运单";
            String ipAddress = null; // 获取 IP 地址逻辑 (如果需要)
            manifestLogHelper.recordBatchAbandonLog(manifests, loginUser.getId(), loginUser.getNickname(), ipAddress, context);
            // --- 结束：记录批量废弃日志 --- END

            manifests.forEach(manifest -> {
                if (manifest.getIsDelete()) {
                    return;
                }
                if (!loginUser.ifAdmin() && manifest.ifPickedUp()) {
                    throw new ServiceException("订单[" + manifest.getOrderNumber() + "]已揽件，无法执行此次废弃操作");
                }
                manifest.setIsDelete(true);
                // 返还运费
                BigDecimal totalCost = manifest.getTotalCost();
                if (totalCost.compareTo(BigDecimal.ZERO) == 0) {
                    return;
                }
                if (manifest.ifShipped()) {
                    FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
                    fundAccountService.change(ModifyBalanceDTO.builder()
                            .userId(manifest.getUserId())
                            .changeBalance(totalCost)
                            .reason("废弃订单，返还运费")
                            .manifestId(manifest.getId())
                            .build());
                }
            });
            manifestRepository.updateBatchByIds(manifests);
            // 记录日志
            OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                    .operatorId(loginUser.getId())
                    .operationType(OperationType.MANIFEST_ABANDON.getValue())
                    .operation(OperationType.MANIFEST_ABANDON.getDescription())
                    .originalInfo(JSON.toJSONString(manifestIds))
                    .build();
            operationLogService.log(operationLogEntity);
            return true;
        } catch (Exception e) {
            log.error("废弃订单发生错误", e);
            throw new ServiceException("废弃订单发生错误");
        } finally {
            locks.forEach(Lock::unlock);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean restore(List<Long> manifestIds) {
        // 对这些订单上锁
        List<RLock> locks = new ArrayList<>();
        manifestIds.forEach(manifestId -> {
            RLock lock = redissonClient.getLock(CacheConstants.MANIFEST_CHANGE_SHIPPING_LOCK + manifestId);
            try {
                if (!lock.tryLock(5, TimeUnit.SECONDS)) {
                    throw new ServiceException("当前订单中存在正在被修改的订单，请稍后重试");
                }
            } catch (InterruptedException e) {
                throw new ServiceException("当前订单中存在正在被修改的订单，请稍后重试");
            }
            locks.add(lock);
        });
        try {
            List<Manifest> manifests = manifestRepository.getByIds(manifestIds);
            if (CollUtil.isEmpty(manifests)) {
                return true;
            }

            // --- 开始：记录批量恢复日志 --- START
            LoginUserEntity loginUser = LoginUserHolder.getLoginUser(); // 确保获取了用户信息
            String context = "后台管理-恢复运单";
            String ipAddress = null; // 获取 IP 地址逻辑 (如果需要)
            manifestLogHelper.recordBatchRestoreLog(manifests, loginUser.getId(), loginUser.getNickname(), ipAddress, context);
            // --- 结束：记录批量恢复日志 --- END

            manifests.forEach(manifest -> {
                if (!manifest.getIsDelete()) {
                    return;
                }
                manifest.setIsDelete(false);
                // 扣除运费
                BigDecimal totalCost = manifest.getTotalCost();
                if (totalCost.compareTo(BigDecimal.ZERO) == 0) {
                    return;
                }
                if (manifest.ifShipped()) {
                    FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
                    fundAccountService.change(ModifyBalanceDTO.builder()
                            .userId(manifest.getUserId())
                            .changeBalance(totalCost.negate())
                            .reason("恢复订单，扣除运费")
                            .manifestId(manifest.getId())
                            .build());
                }
            });
            manifestRepository.updateBatchByIds(manifests);
            // 记录日志
            OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                    .operatorId(LoginUserHolder.getLoginUser().getId())
                    .operationType(OperationType.MANIFEST_RESTORE.getValue())
                    .operation(OperationType.MANIFEST_RESTORE.getDescription())
                    .originalInfo(JSON.toJSONString(manifestIds))
                    .build();
            operationLogService.log(operationLogEntity);
            return true;
        } catch (Exception e) {
            log.error("废弃订单发生错误", e);
            throw new ServiceException("废弃订单发生错误");
        } finally {
            locks.forEach(Lock::unlock);
        }
    }

    @Override
    public Boolean remark(ManifestRemarkReq req) {
        userService.validateAdmin();
        Manifest manifest = manifestRepository.getById(req.getId());
        manifest.setRemark(req.getRemark());
        manifestRepository.updateById(manifest);
        // 记录日志
        OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                .operatorId(LoginUserHolder.getLoginUser().getId())
                .operationType(OperationType.MANIFEST_REMARK.getValue())
                .operation(OperationType.MANIFEST_REMARK.getDescription())
                .originalInfo(manifest.getRemark())
                .newInfo(req.getRemark())
                .manifestId(req.getId())
                .build();
        operationLogService.log(operationLogEntity);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFreightCost(ManifestUpdateFreightCostReq req) {
        userService.validateAdmin();
        // 判断当前订单状态是否已发货，如果已发货，需要调整对应用户的账户
        // 如果未发货，直接修改即可
        Long manifestId = req.getManifestId();
        Manifest manifest = manifestRepository.getById(manifestId);
        // 判断状态
        if (manifest.ifShipped()) {
            // 已发货，修改对应用户的账户
            // 获取之前的运费
            BigDecimal totalCost = manifest.getTotalCost();
            // 获取当前总运费
            BigDecimal currentCost = Optional.ofNullable(req.getCost()).orElse(BigDecimal.ZERO)
                    .add(Optional.ofNullable(req.getOverLengthSurcharge()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(req.getRemoteAreaSurcharge()).orElse(BigDecimal.ZERO))
                    .add(Optional.ofNullable(req.getOtherCost()).orElse(BigDecimal.ZERO));
            // 计算运费差价，多退少补
            BigDecimal costDiff = currentCost.subtract(totalCost);
            FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
            fundAccountService.cost(manifest.getUserId(), costDiff, manifestId);
        }
        // 记录日志
        OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                .operatorId(LoginUserHolder.getLoginUser().getId())
                .operationType(OperationType.MANIFEST_UPDATE_FREIGHT_COST.getValue())
                .operation(OperationType.MANIFEST_UPDATE_FREIGHT_COST.getDescription())
                .originalInfo("基本费用:" + manifest.getCost() + ";超长费:" + manifest.getOverLengthSurcharge() + ";偏远费:" + manifest.getRemoteAreaSurcharge() + ";" + manifest.getOtherCostName() + ":" + manifest.getOtherCost())
                .manifestId(manifestId)
                .build();
        // 修改运费信息
        manifest.setCost(req.getCost());
        manifest.setOverLengthSurcharge(req.getOverLengthSurcharge());
        manifest.setRemoteAreaSurcharge(req.getRemoteAreaSurcharge());
        manifest.setOtherCostName(req.getOtherCostName());
        manifest.setOtherCost(req.getOtherCost());
        manifestRepository.updateById(manifest);
        operationLogEntity.setNewInfo("基本费用:" + manifest.getCost() + ";超长费:" + manifest.getOverLengthSurcharge() + ";偏远费:" + manifest.getRemoteAreaSurcharge() + ";" + manifest.getOtherCostName() + ":" + manifest.getOtherCost());
        operationLogService.log(operationLogEntity);
        return true;
    }

    @Override
    public List<Manifest> findShippedManifests() {
        return manifestRepository.findShippedManifests();
    }

    @Override
    public void updateBatchByIds(List<Manifest> manifestList) {
        manifestRepository.updateBatchByIds(manifestList);
    }

    @Override
    public Manifest getManifestById(Long manifestId) {
        return manifestRepository.getById(manifestId);
    }

    @Override
    public void exportOrder(ManifestOrderExportReq req, HttpServletResponse response) {
        // 查询数据库
        List<Manifest> manifests = manifestRepository.getByIds(req.getIds());
        if (CollUtil.isEmpty(manifests)) {
            throw new ServiceException("没有查询到相关订单");
        }
        // 如果不为空，需要到数据库查询对应物品
        List<Long> manifestIds = new ArrayList<>();
        manifests.forEach(manifest -> manifestIds.add(manifest.getId()));
        // 查询运单
        List<ManifestItem> manifestItems = manifestItemService.listBatchByManifestIds(manifestIds);

        // 查询创建人
        // 类型转换
        Map<Long, Manifest> manifestMap = manifests.stream().collect(Collectors.toMap(Manifest::getId, manifest -> manifest));
        List<OrderDownloadExcel> downloadExcels = manifestItems.stream().map(item -> {
            Manifest manifest = manifestMap.get(item.getManifestId());
            return OrderDownloadExcel.builder()
                    .expressNumber(manifest.getExpressNumber())
                    .sawagaNumber(manifest.getSawagaNumber())
                    .orderNumber(manifest.getOrderNumber())
                    .itemWeight(item.getWeight())
                    .itemQuantity(item.getQuantity())
                    .itemPrice(item.getPrice())
                    .receiverZipCode(manifest.getReceiverZipCode())
                    .receiverName(manifest.getReceiverName())
                    .receiverAddress(manifest.getReceiverAddress())
                    .receiverPhone(manifest.getReceiverPhone())
                    .itemName(item.getName()).build();
        }).collect(Collectors.toList());
        try (InputStream inputStream = ResourceUtil.getStream("templates/DownloadOrderTemplate.xlsx")) {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "订单列表";
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncodeUtil.encode(fileName) + ".xlsx");

            // 生成sheet
            WriteSheet sheet1 = EasyExcel.writerSheet(0, "Manifest").build();
            ExcelWriter write;
            write = EasyExcel.write(response.getOutputStream(), TokyoCustomsDocumentExcelRow.class)
                    .withTemplate(inputStream)
                    .build();
            // 填充数据
            write.fill(downloadExcels, sheet1);
            write.close();
        } catch (IOException e) {
            throw new ServiceException("导出失败, 请联系管理员");
        }
    }

    @Override
    public ManifestStatisticsRes statistics(ManifestStatisticsReq req) {
        Long userId = null;
        if (!LoginUserHolder.getLoginUser().ifAdmin()) {
            userId = LoginUserHolder.getLoginUser().getId();
        } else if (req.getUserId() != null) {
            userId = req.getUserId();
        }
        StatisticsTimeType timeType = req.getTimeType();
        StatisticsTimeRangeDTO statisticsTimeRangeDTO = timeType.calcTimeRange(req.getTime());
        int pageSize = 1000;
        int count = 0;
        BigDecimal totalValue = BigDecimal.ZERO;
        for (int pageIndex = 1; ; pageIndex++) {
            PageResult<Manifest> manifestPageResult = manifestRepository.page(userId,
                    statisticsTimeRangeDTO.getStartTime(),
                    statisticsTimeRangeDTO.getEndTime(),
                    pageIndex,
                    pageSize
            );
            if (CollUtil.isEmpty(manifestPageResult.getList())) {
                break;
            }
            count += manifestPageResult.getList().size();
            totalValue = totalValue
                    .add(manifestPageResult.getList().stream()
                            .map(Manifest::getTotalCost)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO));
        }
        return ManifestStatisticsRes.builder()
                .shipmentVolume(count)
                .shipmentValue(totalValue)
                .build();
    }

    @Override
    public Manifest fuzzyGetBySawagaNumber(String sawagaNumber) {
        return manifestRepository.fuzzyGetBySawagaNumber(sawagaNumber);
    }

    @Override
    @Transactional
    public void deleteByUserId(Long userId) {
        List<Manifest> manifests = manifestRepository.getByUserId(userId);
        if (CollUtil.isEmpty(manifests)) {
            return;
        }
        List<Long> manifestIds = manifests.stream().map(Manifest::getId).collect(Collectors.toList());
        manifestRepository.deleteByIds(manifestIds);
        manifestItemService.deleteByManifestIds(manifestIds);
    }

    @Override
    public Boolean changeOwner(ManifestChangeOwnerReq req) {
        List<Manifest> manifests = manifestRepository.getByIds(req.getManifestIds());
        if (CollUtil.isEmpty(manifests)) {
            return true;
        }
        manifests.forEach(manifest -> manifest.setUserId(req.getToUserId()));
        manifestRepository.updateBatchByIds(manifests);
        return true;
    }

    @Override
    public void printSimpleWaybill(List<Long> manifestIds, HttpServletResponse httpServletResponse) {
        // 设定HTTP响应的内容类型为 "application/pdf"
        httpServletResponse.setContentType("application/pdf");
        // 设置'Content-Disposition'头部信息，指定PDF文件名并设为附件形式进行下载
        String filename = "output.pdf";
        try {
            httpServletResponse.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(filename, "UTF-8") + "\"");
        } catch (UnsupportedEncodingException e) {
            throw new ServiceException("打印面单失败");
        }
        // 查询所有需要打印的运单
        List<ManifestAggregate> manifestAggregates = getManifestAggregateByIdsSort(manifestIds);
        if (CollUtil.isEmpty(manifestAggregates)) {
            throw new ServiceException("查询不到所选订单");
        }
        // Initialize PDF writer
        PdfWriter writer;
        try {
            writer = new PdfWriter(httpServletResponse.getOutputStream());
        } catch (IOException e) {
            throw new ServiceException("打印面单失败");
        }

        // Initialize PDF document
        PdfDocument pdf = new PdfDocument(writer);

        // Initialize document
        Document document = new Document(pdf, PageSize.A4);
        document.setFont(InvoiceServiceImpl.getFont());
        document.setMargins(0, 0, 0, 0); // remove margins if desired
        for (int i = 0; i < manifestAggregates.size(); i++) {
            ManifestAggregate manifestAggregate = manifestAggregates.get(i);
            Manifest manifest = manifestAggregate.getManifest();
            List<ManifestItem> manifestItems = manifestAggregate.getManifestItems();
            // 创建一个SolidLine对象
            SolidLine lineDrawer = new SolidLine(2.5f);  // 线的粗细可以根据需要调整
            lineDrawer.setColor(ColorConstants.BLACK);
            LineSeparator lineSeparator = new LineSeparator(lineDrawer);
            lineSeparator.setMarginTop(20);
            lineSeparator.setMarginBottom(20);
            lineSeparator.setHorizontalAlignment(HorizontalAlignment.CENTER); // 设置线居中
            lineSeparator.setWidth(pdf.getDefaultPageSize().getWidth() * 0.90f); // 设置线的宽度为页面宽度的75%
            document.add(lineSeparator);

            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                BitMatrix bitMatrix = new MultiFormatWriter().encode(manifest.getTrackingNumber(), BarcodeFormat.CODE_128, 300, 100);
                MatrixToImageWriter.writeToStream(bitMatrix, "png", outputStream);
                Image code = new Image(ImageDataFactory.create(outputStream.toByteArray())).setHeight(40).setWidth(200);


                // Resize image to fit page
                PageSize pageSize = pdf.getDefaultPageSize();
                float width = pageSize.getWidth() - document.getLeftMargin() - document.getRightMargin();
                float height = pageSize.getHeight() - document.getTopMargin() - document.getBottomMargin();
                code.setAutoScaleWidth(true);
                code.setAutoScaleHeight(true);
                code.setMaxHeight(height);
                code.setMaxWidth(width);

                // Add image to the document
                document.add(code);

                // 单号
                Paragraph paragraph = new Paragraph(manifest.getTrackingNumber());
                paragraph.setFontSize(28);
                paragraph.setMarginTop(5);
                paragraph.setMarginBottom(5);
                paragraph.setTextAlignment(TextAlignment.CENTER);
                document.add(paragraph);
                document.add(lineSeparator);

                Paragraph info = getParagraph("名前: " + manifest.getReceiverName());
                document.add(info);
                document.add(getParagraph("住所: " + manifest.getReceiverAddress().replaceAll("\n", " ")));
                document.add(getParagraph("電話番号: " + manifest.getReceiverPhone()));
                document.add(getParagraph("郵便番号: " + manifest.getReceiverZipCode()));
                document.add(getParagraph("品名: " + manifestItems.stream().map(ManifestItem::getName).collect(Collectors.joining(","))));
                document.add(getParagraph("数量: " + manifestItems.stream().mapToInt(ManifestItem::getQuantity).sum()));

                document.add(lineSeparator);

                Paragraph printTime = getParagraph("印刷時間: " + DateUtil.formatDateTime(new Date()));
                printTime.setMarginRight(50);
                printTime.setTextAlignment(TextAlignment.RIGHT);
                document.add(printTime);
                Paragraph username = getParagraph(LoginUserHolder.getLoginUser().getUsername());
                username.setMarginRight(50);
                username.setTextAlignment(TextAlignment.RIGHT);
                document.add(username);

                document.add(lineSeparator.setVerticalAlignment(VerticalAlignment.BOTTOM));
                // Ensure next iteration creates a new page
                if (i < manifestAggregates.size() - 1) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        try {
            document.close();
            httpServletResponse.flushBuffer();
        } catch (IOException e) {
            throw new ServiceException("生成面单文件失败");
        }
    }

    @Override
    public List<ManifestBatchSearchByExpressNumberRes> batchSearchByExpressNumbers(List<String> expressNumbers) {
        expressNumbers = expressNumbers.stream().map(expressNumber -> expressNumber.replaceAll(" ", "")).collect(Collectors.toList());
        List<Manifest> manifests = manifestRepository.getByExpressNumbers(expressNumbers);
        List<Long> smallParcelManifestIds = manifests.stream()
                .filter(manifest -> ObjUtil.equals(manifest.getShippingFeeTemplateType(), ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode()))
                .map(Manifest::getId).collect(Collectors.toList());
        Map<Long, List<Tracking>> manifestIdTrackingMap = new HashMap<>();
        if (CollUtil.isNotEmpty(smallParcelManifestIds)) {
            List<Tracking> trackingList = trackingService.getByManifestIds(smallParcelManifestIds);
            if (CollUtil.isNotEmpty(trackingList)) {
                manifestIdTrackingMap = trackingList.stream().collect(Collectors.groupingBy(Tracking::getManifestId));
            }
        }
        List<String> finalExpressNumbers = expressNumbers;
        manifests.sort(Comparator.comparing(m -> finalExpressNumbers.indexOf(m.getExpressNumber())));
        Map<Long, List<Tracking>> finalManifestIdTrackingMap = manifestIdTrackingMap;
        return manifests.stream()
                .map(manifest -> {
                    if (ObjUtil.equals(manifest.getShippingFeeTemplateType(), ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode())) {
                        List<Tracking> tracks = finalManifestIdTrackingMap.get(manifest.getId());
                        Tracking tracking = tracks.get(0);
                        return ManifestBatchSearchByExpressNumberRes.builder()
                                .id(manifest.getId())
                                .expressNumber(manifest.getExpressNumber())
                                .orderNumber(manifest.getOrderNumber())
                                .orderNo(manifest.getOrderNo())
                                .receiverAddress(manifest.getReceiverAddress())
                                .receiverName(manifest.getReceiverName())
                                .pickUpTime(manifest.getPickUpTime())
                                .deliveredTime(manifest.getDeliveredTime())
                                .place(tracking.getPlace())
                                .trackingUpdateTime(tracking.getTime() == null ? tracking.getCreateTime() :
                                        tracking.getTime().toInstant()
                                                .atZone(ZoneId.systemDefault())
                                                .toLocalDateTime())
                                .trackingDesc(tracking.getTrack()).build();
                    } else {
                        Integer trackingStatus = manifest.getTrackingStatus();
                        TrackingStatus trackingStatus1 = TrackingStatus.matchByValue(trackingStatus);
                        return ManifestBatchSearchByExpressNumberRes.builder()
                                .id(manifest.getId())
                                .expressNumber(manifest.getExpressNumber())
                                .orderNumber(manifest.getOrderNumber())
                                .orderNo(manifest.getOrderNo())
                                .receiverAddress(manifest.getReceiverAddress())
                                .receiverName(manifest.getReceiverName())
                                .pickUpTime(manifest.getPickUpTime())
                                .deliveredTime(manifest.getDeliveredTime())
                                .place(trackingStatus1.getPlace())
                                .trackingUpdateTime(manifest.getTrackingUpdateTime())
                                .trackingDesc(trackingStatus1.getDescription()).build();
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public Manifest getManifestByOrderNoOrExpressNumber(String orderNo) {
        return manifestRepository.getByOrderNoOrExpressNumber(orderNo);
    }

    @Override
    public List<Manifest> findShippedManifestsByShippingFeeTemplateTypes(List<Integer> templateTypes) {
        return manifestRepository.findShippedManifestsByShippingFeeTemplateTypes(templateTypes);
    }

    @Override
    public void generateBarcodePdf(ManifestGenerateBarcodePdfReq req, HttpServletResponse httpServletResponse) {
        List<Long> ids = req.getIds();
        List<Manifest> manifests;
        if (CollUtil.isNotEmpty(ids)) {
            manifests = manifestRepository.getByIds(ids);
        } else {
            manifests = manifestRepository.listByPickUpTimeAndShippingFeeTemplateType(req.getPickUpTimeFrom(), req.getPickUpTimeTo(), req.getShippingFeeTemplateTypes());
        }
        if (CollUtil.isEmpty(manifests)) {
            throw new ServiceException("没有符合条件的运单");
        }
        barcodePdfService.generate(manifests, httpServletResponse);
    }

    @Override
    public void exportPreReportInfo(ManifestExportPreReportInfoReq req, HttpServletResponse response) {
        // 查询对应订单
        List<Manifest> manifests;
        // 如果有ID就按ID查
        if (CollUtil.isNotEmpty(req.getIds())) {
            manifests = manifestRepository.getByIds(req.getIds());
        } else if (req.getPreReportedTimeFrom() != null && req.getPreReportedTimeTo() != null) {
            // 如果没有ID就按时间查
            LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
            if (loginUser.ifAdmin()) {
                // 如果是管理员，那么按照时间查
                manifests = manifestRepository.listByPreReportedTime(req.getPreReportedTimeFrom(), req.getPreReportedTimeTo(), null);
            } else {
                // 如果不是管理员，那么按照用户ID和时间查
                manifests = manifestRepository.listByPreReportedTime(req.getPreReportedTimeFrom(), req.getPreReportedTimeTo(), loginUser.getId());
            }
        } else {
            // 如果都没有那么报错
            throw new ServiceException("查询参数错误，ID和预报时间需有一者");
        }
        if (CollUtil.isEmpty(manifests)) {
            throw new ServiceException("没有符合条件的运单");
        }
        // 如果不为空，需要到数据库查询对应物品
        List<Long> manifestIds = new ArrayList<>();
        Map<Long, Manifest> manifestMap = new HashMap<>();
        manifests.forEach(manifest -> {
            manifestIds.add(manifest.getId());
            manifestMap.put(manifest.getId(), manifest);
        });
        // 查询运单
        List<ManifestItem> manifestItems = manifestItemService.listBatchByManifestIds(manifestIds);

        // 类型转换
        List<OrderDownloadExcel> downloadExcels = manifestItems
                .stream()
                .map(item -> {
                    Manifest manifest = manifestMap.get(item.getManifestId());
                    return OrderDownloadExcel.builder()
                            .expressNumber(manifest.getExpressNumber())
                            .sawagaNumber(manifest.getSawagaNumber())
                            .orderNumber(manifest.getOrderNumber())
                            .itemWeight(item.getWeight())
                            .itemQuantity(item.getQuantity())
                            .itemPrice(item.getPrice())
                            .receiverZipCode(manifest.getReceiverZipCode())
                            .receiverName(manifest.getReceiverName())
                            .receiverAddress(manifest.getReceiverAddress())
                            .receiverPhone(manifest.getReceiverPhone())
                            .itemName(item.getName()).build();
                }).collect(Collectors.toList());
        try (InputStream inputStream = ResourceUtil.getStream("templates/DownloadOrderTemplate.xlsx")) {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "订单列表";
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncodeUtil.encode(fileName) + ".xlsx");

            // 生成sheet
            WriteSheet sheet1 = EasyExcel.writerSheet(0, "Manifest").build();
            ExcelWriter write;
            write = EasyExcel.write(response.getOutputStream(), TokyoCustomsDocumentExcelRow.class)
                    .withTemplate(inputStream)
                    .build();
            // 填充数据
            write.fill(downloadExcels, sheet1);
            write.close();
        } catch (IOException e) {
            throw new ServiceException("导出失败, 请联系管理员");
        }
    }

    @Override
    public Boolean transfer(ManifestTransferReq req) {
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        if (!loginUser.ifSuperAdmin()) {
            throw new ServiceException("没有权限");
        }

        // --- 开始：记录转单号日志 --- START
        // 1. 获取原始运单信息
        Manifest originalManifest = manifestRepository.getById(req.getId());
        if (originalManifest == null) {
            throw new ServiceException("运单不存在，无法转单号");
        }
        String oldTransferredNumber = originalManifest.getTransferredTrackingNumber();
        String newTransferredNumber = req.getTransferredTrackingNumber();
        Long manifestId = req.getId();
        String context = "后台管理-单个转单号";
        String ipAddress = null; // 获取 IP 地址逻辑
        // --- 结束：记录转单号日志准备 --- END

        // 更新数据库
        manifestRepository.updateById(Manifest.builder().id(req.getId()).transferredTrackingNumber(req.getTransferredTrackingNumber()).build());

        // --- 开始：调用 Helper 记录日志 --- START
        try {
            manifestLogHelper.recordTransferLog(manifestId, loginUser.getId(), loginUser.getNickname(),
                    ipAddress, context, oldTransferredNumber, newTransferredNumber);
        } catch (Exception logEx) {
            log.error("记录单个转单号日志失败: manifestId={}", manifestId, logEx);
            // 日志记录失败不影响主流程
        }
        // --- 结束：调用 Helper 记录日志 --- END

        return true;
    }

    @Override
    public Boolean batchTransfer(MultipartFile file) {
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        if (!loginUser.ifSuperAdmin()) {
            throw new ServiceException("没有权限");
        }
        log.info("用户：id-[{}], nickname-[{}]开始批量转单号", loginUser.getId(), loginUser.getNickname());
        TransferTrackingNumberExcelListener transferTrackingNumberExcelListener = new TransferTrackingNumberExcelListener();
        try {
            EasyExcel.read(file.getInputStream(), TransferTrackingNumberRow.class, transferTrackingNumberExcelListener)
                    .headRowNumber(0)
                    .sheet()
                    .doRead();
        } catch (IOException e) {
            log.error("读取上传文件失败:", e);
            throw new ServiceException("读取上传文件失败，请联系管理员");
        }
        List<TransferTrackingNumberRow> parsedData = transferTrackingNumberExcelListener.getParsedData();
        if (CollUtil.isEmpty(parsedData)) {
            log.error("解析到数据为空");
            throw new ServiceException("解析到数据为空，请检查上传文件");
        }
        Set<String> expressNumbers = parsedData.stream().map(TransferTrackingNumberRow::getOriginalTrackingNumber).collect(Collectors.toSet());
        try {
            Map<String, String> trackingNumberMap = parsedData.stream().collect(Collectors.toMap(TransferTrackingNumberRow::getOriginalTrackingNumber, TransferTrackingNumberRow::getTransferredTrackingNumber));

            // --- 开始：批量转单号日志准备 --- START
            // 1. 获取原始运单列表
            List<Manifest> originalManifests = manifestRepository.getByExpressNumbers(expressNumbers);
            if (CollUtil.isEmpty(originalManifests)) {
                log.error("没有找到对应的运单进行批量转单号");
                throw new ServiceException("没有找到对应的运单");
            }
            // 2. 存储原始转单号
            Map<Long, String> oldTransferredNumbersMap = originalManifests.stream()
                    .filter(m -> m != null && m.getId() != null)
                    .collect(Collectors.toMap(Manifest::getId, Manifest::getTransferredTrackingNumber, (v1, v2) -> v1)); // 处理可能的 ID 重复（理论上不应发生）
            // --- 结束：批量转单号日志准备 --- END

            // 更新 Manifest 对象列表 (在内存中)
            originalManifests.forEach(manifest -> manifest.setTransferredTrackingNumber(trackingNumberMap.get(manifest.getExpressNumber())));

            // 批量更新数据库
            manifestRepository.updateBatchByIds(originalManifests);

            // --- 开始：记录批量转单号日志 --- START
            String context = "后台管理-批量转单号(文件导入)";
            String ipAddress = null; // 获取 IP 地址逻辑
            try {
                manifestLogHelper.recordBatchTransferLog(originalManifests, oldTransferredNumbersMap,
                        loginUser.getId(), loginUser.getNickname(), ipAddress, context);
            } catch (Exception logEx) {
                log.error("记录批量转单号日志失败", logEx);
                // 日志记录失败不影响主流程
            }
            // --- 结束：记录批量转单号日志 --- END

            log.info("用户：id-[{}], nickname-[{}]批量转单号成功, 数量{}条", loginUser.getId(), loginUser.getNickname(), originalManifests.size());
        } catch (IllegalStateException ie) {
            throw new ServiceException("原单号" + ie.getMessage().replace("Duplicate key ", "") + "重复，请删除后重新上传文件");
        } catch (Exception e) {
            log.error("解析错误", e);
            throw new ServiceException("解析到数据为空，请检查上传文件");
        }
        return true;
    }

    @Override
    public List<ManifestAggregate> listByMasterBillId(Long masterBillId) {
        List<Manifest> manifests = manifestRepository.listByMasterBillId(masterBillId);

        if (CollUtil.isEmpty(manifests)) {
            return Collections.emptyList();
        }
        List<Long> ids = manifests.stream().map(Manifest::getId).collect(Collectors.toList());
        List<ManifestItem> manifestItems = manifestItemService.listBatchByManifestIds(ids);
        Map<Long, List<ManifestItem>> manifestIdMapItem = manifestItems.stream().collect(Collectors.groupingBy(ManifestItem::getManifestId));

        return manifests.stream()
                .map(manifest -> ManifestAggregate
                        .builder()
                        .manifest(manifest)
                        .manifestItems(manifestIdMapItem.get(manifest.getId())).build())
                .collect(Collectors.toList());
    }

    private static Paragraph getParagraph(String text) {
        Paragraph info = new Paragraph(text);
        info.setFontSize(20);
        info.setMarginTop(3);
        info.setMarginBottom(3);
        info.setMarginLeft(50);
        info.setMarginRight(50);
        return info;
    }

    @Override
    public PageResult<ManifestSearchPickedUpRes> searchPickedUp(ManifestSearchPickedUpReq req) {
        LocalDate pickUpDate = req.getPickUpDate();

        Integer timeIntervalPoint = req.getTimeIntervalPoint();
        Long masterBillId = req.getMasterBillId();
        String expressNumber = req.getExpressNumber();
        String sawagaNumber = req.getSawagaNumber();
        Integer cargoType = req.getCargoType();
        Integer destination = req.getDestination();
        Integer pageNo = req.getPageNo();
        Integer pageSize = req.getPageSize();
        log.info("Advanced search for manifests with pickUpDate: {}, timeIntervalPoint: {}, masterBillId: {}, expressNumber: {}, sawagaNumber: {}, cargoType: {}, destination: {}, pageNo: {}, pageSize: {}",
                pickUpDate, timeIntervalPoint, masterBillId, expressNumber, sawagaNumber, cargoType, destination, pageNo, pageSize);
        LocalDateTime pickUpTimeStart = null;
        LocalDateTime pickUpTimeEnd = null;
        if (timeIntervalPoint != null && pickUpDate != null) {
            pickUpTimeStart = pickUpDate.atStartOfDay();
            // 设置时间区间
            pickUpTimeStart = pickUpTimeStart.withHour(timeIntervalPoint);
            pickUpTimeEnd = pickUpTimeStart.plusDays(1);
        }

        // 根据cargoType, destination转换成shippingFeeTemplateType
        List<Integer> shippingFeeTemplateTypes = getShippingFeeTemplateTypes(destination, cargoType);

        PageResult<Manifest> pageResult = manifestRepository.advancedSearch(
                pickUpTimeStart, pickUpTimeEnd, masterBillId, expressNumber, sawagaNumber, shippingFeeTemplateTypes
                , pageNo, pageSize
        );

        if (pageResult == null || CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }

        // 查询揽件人
        Set<Long> pickUpIds = pageResult.getList().stream().map(Manifest::getPickUpBy).collect(Collectors.toSet());
        List<UserEntity> userEntities = userService.listBatchByIds(pickUpIds);
        Map<Long, UserEntity> userIdMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userEntities)) {
            for (UserEntity userEntity : userEntities) {
                userIdMap.put(userEntity.getId(), userEntity);
            }
        }

        // 查询提单号
        Set<Long> masterBillIds = pageResult.getList().stream().map(Manifest::getMasterBillId).collect(Collectors.toSet());
        List<MasterBill> masterBills = masterBillService.listByIds(masterBillIds);
        Map<Long, MasterBill> masterBillIdMap = new HashMap<>();
        if (CollUtil.isNotEmpty(masterBills)) {
            for (MasterBill masterBill : masterBills) {
                masterBillIdMap.put(masterBill.getId(), masterBill);
            }
        }

        List<ManifestSearchPickedUpRes> resList = pageResult.getList().stream()
                .map(d -> ManifestSearchPickedUpRes.from(d, userIdMap, masterBillIdMap))
                .collect(Collectors.toList());

        return new PageResult<>(resList, pageResult.getTotal());
    }

    private static @NotNull List<Integer> getShippingFeeTemplateTypes(Integer destination, Integer cargoType) {
        List<Integer> shippingFeeTemplateTypes = new ArrayList<>();
        // 如果destination = null，那么cargoType如果是1，那么shippingFeeTemplateType就是1和4，如果是2，那么shippingFeeTemplateType就是2，如果是3，那么shippingFeeTemplateType就是3和5
        // 如果destination = 1，那么cargoType是多少那么shippingFeeTemplateType就是多少
        // 如果destination = 2，那么cargoType如果是1，那么shippingFeeTemplateType就是4，如果是3，那么shippingFeeTemplateType就是5
        if (destination == null && cargoType != null) {
            if (cargoType == 1) {
                shippingFeeTemplateTypes.add(1);
                shippingFeeTemplateTypes.add(4);
            } else if (cargoType == 2) {
                shippingFeeTemplateTypes.add(2);
            } else if (cargoType == 3) {
                shippingFeeTemplateTypes.add(3);
                shippingFeeTemplateTypes.add(5);
            } else if (cargoType == 6) {
                shippingFeeTemplateTypes.add(6);
            }
        } else if (destination != null && destination == 1) {
            if (cargoType == null) {
                shippingFeeTemplateTypes.add(1);
                shippingFeeTemplateTypes.add(2);
                shippingFeeTemplateTypes.add(3);
                shippingFeeTemplateTypes.add(6);
            } else {
                shippingFeeTemplateTypes.add(cargoType);
            }
        } else if (destination != null && destination == 2) {
            if (cargoType == null) {
                shippingFeeTemplateTypes.add(4);
                shippingFeeTemplateTypes.add(5);
            } else if (cargoType == 1) {
                shippingFeeTemplateTypes.add(4);
            } else if (cargoType == 3) {
                shippingFeeTemplateTypes.add(5);
            }
        }
        return shippingFeeTemplateTypes;
    }

    /**
     * 准备揽件操作前的日志数据
     *
     * @param manifest 操作前的运单对象
     * @return 包含操作前关键字段和物品列表的 Map
     */
    private Map<String, Object> preparePickupLogBeforeValues(Manifest manifest) {
        // 1. 调用 Helper 获取 Manifest 自身的状态字段
        // 注意：因为 extractManifestStateMap 是 private 的，我们需要通过注入的 manifestLogHelper 访问
        // 或者将 extractManifestStateMap 改为 public static (如果不想依赖注入)
        // 假设 ManifestLogHelper 已通过 @RequiredArgsConstructor 注入
        Map<String, Object> beforeValues = manifestLogHelper.extractManifestStateMap(manifest);

        // 2. 添加物品列表(操作前)
        if (manifest != null && manifest.getId() != null) { // 增加检查，防止 manifest 为 null 时调用 service
            try {
                List<ManifestItem> beforeItems = manifestItemService.listByManifestId(manifest.getId());
                beforeValues.put("manifestItems", beforeItems);
            } catch (Exception e) {
                log.error("查询操作前物品列表失败, manifestId: {}", manifest.getId(), e);
                beforeValues.put("manifestItemsError", "Failed to retrieve items: " + e.getMessage());
            }
        } else if (manifest == null) {
            log.warn("preparePickupLogBeforeValues 收到 null manifest 对象，无法查询物品列表");
            // 在 manifest 为 null 时，extractManifestStateMap 返回空 map，这里不需要额外操作
        }
        // else manifest != null 但 id 为 null 的情况，extractManifestStateMap 也可能返回部分数据

        return beforeValues;
    }

}
