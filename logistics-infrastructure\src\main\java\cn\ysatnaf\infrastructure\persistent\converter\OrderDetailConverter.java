package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.order.model.entity.OrderDetail;
import cn.ysatnaf.infrastructure.persistent.po.OrderDetailPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * OrderDetailConverter
 *
 * <AUTHOR>
 * @date 2024/2/7 14:25
 */
@Mapper
public interface OrderDetailConverter {

    OrderDetailConverter INSTANCE = Mappers.getMapper(OrderDetailConverter.class);

    OrderDetailPO entity2po(OrderDetail orderDetail);

    OrderDetail po2entity(OrderDetailPO selectOne);
}
