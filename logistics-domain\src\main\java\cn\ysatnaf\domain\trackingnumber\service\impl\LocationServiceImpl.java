package cn.ysatnaf.domain.trackingnumber.service.impl;

import cn.ysatnaf.domain.trackingnumber.model.entity.Location;
import cn.ysatnaf.domain.trackingnumber.repository.LocationRepository;
import cn.ysatnaf.domain.trackingnumber.service.LocationService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 地点/区域基础数据服务实现
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class LocationServiceImpl implements LocationService {

    private final LocationRepository locationRepository;

    @Override
    public List<Location> listActiveLocations() {
        return locationRepository.findAllActive();
    }

    @Override
    public Location getLocationById(Long id) {
        Location location = locationRepository.findById(id);
        if (location == null) {
            throw new ServiceException("地点不存在, ID: " + id);
        }
        return location;
    }

    @Override
    public Location getLocationByCode(String code) {
        Location location = locationRepository.findByCode(code);
        if (location == null) {
            throw new ServiceException("地点不存在, Code: " + code);
        }
        return location;
    }
} 