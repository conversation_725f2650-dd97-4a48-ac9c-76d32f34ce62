package cn.ysatnaf.domain.excel.serviice.generator.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.ysatnaf.domain.manifest.adapter.ManifestAdapter;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestSearchDTO;
import cn.ysatnaf.domain.excel.serviice.generator.row.TokyoCustomsDocumentExcelRow;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingBoxPO;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingPackagePO;
import cn.ysatnaf.domain.parcelsorting.service.ParcelSortingService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@RequiredArgsConstructor
public class TokyoCustomsDocumentDataExcelConverter {

    /**
     * 将manifest转换为excel数据
     */
    public List<TokyoCustomsDocumentExcelRow> convert(List<Manifest> manifests) {
        // 转换成对应的excel对象
        List<TokyoCustomsDocumentExcelRow> manifestExcelData = new ArrayList<>();
        List<String> expressNumbers = new ArrayList<>();
        for (Manifest manifest : manifests) {
            expressNumbers.add(manifest.getTrackingNumber());
            TokyoCustomsDocumentExcelRow tokyoCustomsDocumentExcelRow = ManifestAdapter.INSTANCE.po2excel(ManifestSearchDTO.of(manifest));
            manifestExcelData.add(tokyoCustomsDocumentExcelRow);
        }
        // 设置袋号
        Map<String, Long> expressNumberBoxIdMap = new HashMap<>();
        Map<Long, String> boxIdPackageNumberMap = new HashMap<>();
        ParcelSortingService parcelSortingService = SpringUtil.getBean(ParcelSortingService.class);
        List<ParcelSortingPackagePO> packages = parcelSortingService.listPackageByExpressNumbers(expressNumbers);
        if (CollUtil.isNotEmpty(packages)) {
            Set<Long> boxIds = new HashSet<>();
            for (ParcelSortingPackagePO aPackage : packages) {
                boxIds.add(aPackage.getBoxId());
                expressNumberBoxIdMap.put(aPackage.getExpressNumber(), aPackage.getBoxId());
            }
            List<ParcelSortingBoxPO> boxes = parcelSortingService.listBoxByBoxIds(boxIds);
            if (CollUtil.isNotEmpty(boxes)) {
                for (ParcelSortingBoxPO box : boxes) {
                    boxIdPackageNumberMap.put(box.getId(), box.getPackageNumber());
                }
            }
        }

        // 设置序列NO
        for (int i = 0; i < manifestExcelData.size(); i++) {
            TokyoCustomsDocumentExcelRow tokyoCustomsDocumentExcelRow = manifestExcelData.get(i);
            Long boxId = expressNumberBoxIdMap.getOrDefault(tokyoCustomsDocumentExcelRow.getBillNo2(), 0L);
            String packageNumber = boxIdPackageNumberMap.get(boxId);
            tokyoCustomsDocumentExcelRow.setPackageNo(packageNumber);
            tokyoCustomsDocumentExcelRow.setQuantity(1);
            tokyoCustomsDocumentExcelRow.setNo(i + 1);
        }
        return manifestExcelData;
    }
}
