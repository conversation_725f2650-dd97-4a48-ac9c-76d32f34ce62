package cn.ysatnaf.domain.fund.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "查看账户 出参")
@Data
public class FundAccountGetRes {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "账户余额")
    private BigDecimal balance;
}
