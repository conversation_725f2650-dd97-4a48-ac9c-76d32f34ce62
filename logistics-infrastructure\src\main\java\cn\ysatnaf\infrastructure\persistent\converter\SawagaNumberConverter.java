package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.manifest.model.entity.SawagaNumberEntity;
import cn.ysatnaf.infrastructure.persistent.po.SawagaNumberPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SawagaNumberConverter {
    SawagaNumberConverter INSTANCE = Mappers.getMapper(SawagaNumberConverter.class);

    SawagaNumberPO toSawagaNumberPO(SawagaNumberEntity entity);

    SawagaNumberEntity toSawagaNumberEntity(SawagaNumberPO po);

    List<SawagaNumberEntity> toSawagaNumberEntityList(List<SawagaNumberPO> poList);

    List<SawagaNumberPO> toSawagaNumberPOList(List<SawagaNumberEntity> entityList);
}
