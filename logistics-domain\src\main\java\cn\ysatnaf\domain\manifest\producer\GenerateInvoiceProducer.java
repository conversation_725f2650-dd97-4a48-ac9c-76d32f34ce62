package cn.ysatnaf.domain.manifest.producer;

import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GenerateInvoiceProducer {

    private final RabbitTemplate rabbitTemplate;

    /**
     * 生成发票任务生产消息
     */
    public void produce(Long taskId) {
        rabbitTemplate.convertAndSend("generate.exchange", "generate.invoice", taskId);
    }
}
