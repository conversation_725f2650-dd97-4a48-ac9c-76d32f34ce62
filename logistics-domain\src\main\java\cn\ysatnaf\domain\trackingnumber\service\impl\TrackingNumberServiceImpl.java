package cn.ysatnaf.domain.trackingnumber.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.trackingnumber.model.listener.TrackingNumberExcelListener;
import cn.ysatnaf.domain.trackingnumber.model.dto.TrackingNumberAllocationResultDTO;
import cn.ysatnaf.domain.trackingnumber.model.entity.*;
import cn.ysatnaf.domain.trackingnumber.model.excel.AllocatedTrackingNumberExcelRow;
import cn.ysatnaf.domain.trackingnumber.model.req.AllocateTrackingNumberReq;
import cn.ysatnaf.domain.trackingnumber.model.valobj.TrackingNumberStatus;
import cn.ysatnaf.domain.trackingnumber.repository.*;
import cn.ysatnaf.domain.trackingnumber.service.CarrierService;
import cn.ysatnaf.domain.trackingnumber.service.TrackingNumberService;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.HashSet;

import cn.ysatnaf.domain.auth.repository.UserRepository;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.trackingnumber.model.dto.ChannelAvailableCountDTO;

/**
 * 预报单号核心服务实现
 * <AUTHOR>
 */
@Slf4j
@Service
public class TrackingNumberServiceImpl implements TrackingNumberService {

    private final LocationRepository locationRepository;
    private final ShipmentTypeRepository shipmentTypeRepository;
    private final TrackingNumberPoolRepository poolRepository;
    private final TrackingNumberImportBatchRepository importBatchRepository;
    private final TrackingNumberAllocationBatchRepository allocationBatchRepository;
    private final CarrierRepository carrierRepository;
    private final CarrierService carrierService;
    private final RedissonClient redissonClient;
    private final UserRepository userRepository;
    private final TrackingNumberChannelRepository trackingNumberChannelRepository;

    @Autowired
    public TrackingNumberServiceImpl(LocationRepository locationRepository,
                                     ShipmentTypeRepository shipmentTypeRepository,
                                     TrackingNumberPoolRepository poolRepository,
                                     TrackingNumberImportBatchRepository importBatchRepository,
                                     TrackingNumberAllocationBatchRepository allocationBatchRepository,
                                     CarrierRepository carrierRepository,
                                     CarrierService carrierService,
                                     RedissonClient redissonClient,
                                     UserRepository userRepository,
                                     TrackingNumberChannelRepository trackingNumberChannelRepository) {
        this.locationRepository = locationRepository;
        this.shipmentTypeRepository = shipmentTypeRepository;
        this.poolRepository = poolRepository;
        this.importBatchRepository = importBatchRepository;
        this.allocationBatchRepository = allocationBatchRepository;
        this.carrierRepository = carrierRepository;
        this.carrierService = carrierService;
        this.redissonClient = redissonClient;
        this.userRepository = userRepository;
        this.trackingNumberChannelRepository = trackingNumberChannelRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TrackingNumberImportBatch importTrackingNumbers(MultipartFile file, Long locationId, Long shipmentTypeId) {
        Long uploaderId = LoginUserHolder.getLoginUser().getId();
        String originalFilename = file.getOriginalFilename();
        log.info("用户ID: {}, 开始导入单号, 地点ID: {}, 货物类型ID: {}, 文件名: {}",
                 uploaderId, locationId, shipmentTypeId, originalFilename);

        // Find channels matching locationId and shipmentTypeId
        List<TrackingNumberChannel> potentialChannels = trackingNumberChannelRepository.findAllActiveChannels().stream()
             .filter(ch -> locationId.equals(ch.getLocationId()) && shipmentTypeId.equals(ch.getShipmentTypeId()))
             .collect(Collectors.toList());

        if (potentialChannels.isEmpty()) {
            log.warn("未找到与地点ID {} 和货物类型ID {} 匹配的有效渠道", locationId, shipmentTypeId);
             throw new ServiceException("无法为此地点和货物类型找到有效的单号渠道，请检查渠道配置");
        }
        if (potentialChannels.size() > 1) {
            log.warn("找到多个与地点ID {} 和货物类型ID {} 匹配的有效渠道，无法确定唯一渠道", locationId, shipmentTypeId);
            throw new ServiceException("找到多个匹配的单号渠道，无法确定导入目标，请检查渠道配置");
        }

        TrackingNumberChannel channel = potentialChannels.get(0);
        Long channelId = channel.getId();
        // We might still need carrierId if some logic depends on it, but it's part of the channel now.
        // Long carrierId = channel.getCarrierId();

        log.info("找到唯一匹配渠道: ID={}, Code={}, Name={}",
                 channelId, channel.getChannelCode(), channel.getChannelName());

        if (!channel.isChannelActive()) {
            throw new ServiceException("找到的渠道 " + channel.getChannelCode() + " 未启用，无法导入单号");
        }

        TrackingNumberImportBatch importBatch = TrackingNumberImportBatch.builder()
                .uploaderId(uploaderId)
                .channelId(channelId)
                .originalFilename(originalFilename)
                .totalCount(0)
                .successCount(0)
                .failedCount(0)
                .importTime(LocalDateTime.now())
                .build();
        importBatch = importBatchRepository.save(importBatch);
        log.info("创建导入批次记录, 批次ID: {}, 目标渠道ID: {}", importBatch.getId(), channelId);

        TrackingNumberExcelListener listener = new TrackingNumberExcelListener();
        try (InputStream inputStream = file.getInputStream()) {
            EasyExcel.read(inputStream, listener).headRowNumber(1).sheet().doRead();
        } catch (IOException e) {
             log.error("读取Excel文件失败, 批次ID: {}, 文件名: {}", importBatch.getId(), originalFilename, e);
            throw new ServiceException("读取上传文件失败");
        } catch (RuntimeException re) {
             log.error("解析Excel时发生运行时异常(可能是表头错误), 批次ID: {}, 文件名: {}", importBatch.getId(), originalFilename, re);
             if (re.getMessage() != null && re.getMessage().contains("表头格式错误")) {
                 throw new ServiceException(re.getMessage());
             }
             throw new ServiceException("解析Excel文件时发生错误");
        }
        List<String> trackingNumbersInExcel = listener.getTrackingNumbers();
         if (CollUtil.isEmpty(trackingNumbersInExcel)) {
            log.warn("Excel文件中未解析到单号, 批次ID: {}", importBatch.getId());
            importBatch.setTotalCount(0);
            importBatchRepository.save(importBatch);
            return importBatch;
        }
        log.info("Excel解析完成, 共 {} 条单号, 批次ID: {}", trackingNumbersInExcel.size(), importBatch.getId());

        Long finalImportBatchId = importBatch.getId();
        List<String> trimmedTrackingNumbers = trackingNumbersInExcel.stream()
                .map(String::trim).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(trimmedTrackingNumbers)) {
            log.warn("Excel 文件中所有单号均为空或无效, 批次ID: {}", finalImportBatchId);
            importBatch.setTotalCount(trackingNumbersInExcel.size());
            importBatch.setSuccessCount(0);
            importBatch.setFailedCount(trackingNumbersInExcel.size());
            importBatchRepository.save(importBatch);
            return importBatch;
        }

        Set<String> existingNumbersSet = poolRepository.findExistingNumbers(channelId, trimmedTrackingNumbers);
        log.info("查询到 {} 个已存在的单号, 目标渠道ID: {}, 批次ID: {}", existingNumbersSet.size(), channelId, finalImportBatchId);

        int totalCount = trackingNumbersInExcel.size();
        int successCount = 0;
        int failedCount = 0;
        List<TrackingNumberPool> numbersToSave = new ArrayList<>();
        for (String trimmedTn : trimmedTrackingNumbers) {
            if (existingNumbersSet.contains(trimmedTn)) {
                failedCount++;
                log.warn("单号已存在(批量查询), 跳过: {}, 渠道ID: {}, 批次ID: {}", trimmedTn, channelId, finalImportBatchId);
            } else {
                numbersToSave.add(TrackingNumberPool.builder()
                        .trackingNumber(trimmedTn)
                        .channelId(channelId)
                        .status(TrackingNumberStatus.AVAILABLE)
                        .importBatchId(finalImportBatchId)
                        .build());
                successCount++;
            }
        }
        int originalFailedCount = trackingNumbersInExcel.size() - trimmedTrackingNumbers.size();
        failedCount += originalFailedCount;

        if (CollUtil.isNotEmpty(numbersToSave)) {
             try {
                int insertedCount = poolRepository.saveBatch(numbersToSave);
                log.info("成功批量插入 {} 条单号, 批次ID: {}, 渠道ID: {}", insertedCount, finalImportBatchId, channelId);
                if(insertedCount != successCount){
                    log.error("批量插入单号数量与预期不符! 预期: {}, 实际: {}. 批次ID: {}, 渠道ID: {}", successCount, insertedCount, finalImportBatchId, channelId);
                     throw new ServiceException("批量插入单号时发生错误");
                }
            } catch (Exception e) {
                log.error("批量插入单号失败, 批次ID: {}, 渠道ID: {}", finalImportBatchId, channelId, e);
                 importBatch.setTotalCount(totalCount);
                 importBatch.setSuccessCount(0);
                 importBatch.setFailedCount(totalCount);
                 importBatchRepository.save(importBatch);
                throw new ServiceException("保存单号时发生数据库错误");
            }
        }

        importBatch.setTotalCount(totalCount);
        importBatch.setSuccessCount(successCount);
        importBatch.setFailedCount(failedCount);
        importBatch = importBatchRepository.save(importBatch);
        log.info("单号导入完成, 批次ID: {}, 渠道ID: {}, 总数: {}, 成功: {}, 失败: {}",
                finalImportBatchId, channelId, totalCount, successCount, failedCount);

        return importBatch;
    }

    // Lock prefix remains channel-based as allocation happens per channel
    private static final String ALLOCATE_LOCK_PREFIX = "lock:tracking_number:allocate:channel:";
    private static final long LOCK_WAIT_TIME_SECONDS = 5;
    private static final long LOCK_LEASE_TIME_SECONDS = 10;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TrackingNumberAllocationResultDTO allocateTrackingNumbers(AllocateTrackingNumberReq req) {
        Long allocatorId = LoginUserHolder.getLoginUser().getId();
        Long locationId = req.getLocationId();
        Long shipmentTypeId = req.getShipmentTypeId();
        // Get customerAccountId from request
        Long customerAccountId = req.getCustomerAccountId(); 
        Integer quantityRequested = req.getQuantity();

        log.info("用户ID: {}, 请求分配单号, 地点ID: {}, 货物类型ID: {}, 客户ID: {}, 请求数量: {}",
                allocatorId, locationId, shipmentTypeId, customerAccountId, quantityRequested);

        // Validate customerAccountId along with other parameters
        if (locationId == null || shipmentTypeId == null || customerAccountId == null || quantityRequested == null || quantityRequested <= 0) {
             log.error("分配请求参数无效: locationId={}, shipmentTypeId={}, customerAccountId={}, quantity={}", 
                      locationId, shipmentTypeId, customerAccountId, quantityRequested);
             throw new ServiceException("分配请求参数无效: 必须提供有效的目的地ID、货物类型ID、客户ID和大于0的数量");
        }
        
        // Optional: Validate if customerAccountId exists in tb_user
        // UserEntity customer = userRepository.getById(customerAccountId); // Assuming you have a getById or findById method
        // if (customer == null) {
        //     log.warn("分配目标客户账户ID不存在: {}", customerAccountId);
        //     throw new ServiceException("指定的客户账户ID无效");
        // }

        // Find the unique active channel based on location and shipment type
         List<TrackingNumberChannel> potentialChannels = trackingNumberChannelRepository.findAllActiveChannels().stream()
             .filter(ch -> locationId.equals(ch.getLocationId()) && shipmentTypeId.equals(ch.getShipmentTypeId()))
             .collect(Collectors.toList());

        if (potentialChannels.isEmpty()) {
            log.warn("未找到与地点ID {} 和货物类型ID {} 匹配的有效渠道", locationId, shipmentTypeId);
             throw new ServiceException("无法为此地点和货物类型找到有效的单号渠道，请检查渠道配置或确保渠道已启用");
        }
        if (potentialChannels.size() > 1) {
            log.warn("找到多个与地点ID {} 和货物类型ID {} 匹配的有效渠道，无法确定唯一渠道", locationId, shipmentTypeId);
            throw new ServiceException("找到多个匹配的单号渠道，无法确定分配目标，请检查渠道配置");
        }
        TrackingNumberChannel channel = potentialChannels.get(0);
        Long channelId = channel.getId();
        Carrier carrier = carrierService.getCarrierById(channel.getCarrierId());
        log.info("根据地点和货物类型找到唯一目标渠道: ID={}, Code={}, Name={}, Carrier={}",
                 channelId, channel.getChannelCode(), channel.getChannelName(), carrier != null ? carrier.getName() : "未知");
        if (!channel.isChannelActive()) {
             throw new ServiceException("找到的渠道 " + channel.getChannelCode() + " 未启用，无法分配单号");
        }

        // Define lock key based on the RESOLVED channelId
        String lockKey = ALLOCATE_LOCK_PREFIX + channelId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(LOCK_WAIT_TIME_SECONDS, LOCK_LEASE_TIME_SECONDS, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("获取渠道分配锁失败, 目标渠道ID: {}, Key: {}", channelId, lockKey);
                throw new ServiceException("系统繁忙，请稍后重试(未能获取锁)");
            }
            log.info("成功获取渠道分配锁, 目标渠道ID: {}, Key: {}", channelId, lockKey);

            // Check available count using the resolved channelId
            long availableCount = poolRepository.countAvailableByChannel(channelId);
            if (availableCount < quantityRequested) {
                 log.warn("可用单号数量不足(持有锁), 目标渠道ID: {}, 请求数量: {}, 可用数量: {}",
                        channelId, quantityRequested, availableCount);
                throw new ServiceException(String.format("渠道 [%s] 单号库存不足，请求 %d，可用 %d",
                        channel.getChannelName(), quantityRequested, availableCount));
            }

            // Create allocation batch record, including customerAccountId
            TrackingNumberAllocationBatch allocationBatch = TrackingNumberAllocationBatch.builder()
                    .allocatorId(allocatorId)
                    .requestedLocationId(locationId)
                    .requestedShipmentTypeId(shipmentTypeId)
                    // Set the customer account ID
                    .customerAccountId(customerAccountId) 
                    .quantityRequested(quantityRequested)
                    .quantityAllocated(0)
                    .allocationTime(LocalDateTime.now())
                    .build();
            allocationBatch = allocationBatchRepository.save(allocationBatch);
            Long allocationBatchId = allocationBatch.getId();
            log.info("创建分配批次记录(持有锁), 批次ID: {}, 客户ID: {}, 原始请求: Loc={}, ShipType={}, 目标渠道ID: {}", 
                     allocationBatchId, customerAccountId, locationId, shipmentTypeId, channelId);

            // Find available numbers using the resolved channelId
            List<TrackingNumberPool> availableNumbers = poolRepository.findAvailableByChannel(channelId, quantityRequested);
            if (CollUtil.isEmpty(availableNumbers) || availableNumbers.size() < quantityRequested) {
                log.error("获取可用单号失败或数量不足(持有锁)! 目标渠道ID: {}, 请求数量: {}, 获取数量: {}. 分配批次ID: {}",
                        channelId, quantityRequested, availableNumbers.size(), allocationBatchId);
                throw new ServiceException("分配单号时库存发生变化(持有锁)，请稍后重试");
            }
            // Update number status
            List<Long> numberIdsToAllocate = availableNumbers.stream().map(TrackingNumberPool::getId).collect(Collectors.toList());
            int updatedCount = poolRepository.updateStatusAndAllocationBatchByIds(numberIdsToAllocate, TrackingNumberStatus.ALLOCATED, allocationBatchId);
            if (updatedCount != quantityRequested) {
                log.error("更新单号状态数量与预期不符(持有锁)! 预期: {}, 实际: {}. 分配批次ID: {}",
                        quantityRequested, updatedCount, allocationBatchId);
                throw new ServiceException("更新单号状态时发生错误(持有锁)");
            }
            log.info("成功更新 {} 条单号状态为已分配(持有锁), 分配批次ID: {}", updatedCount, allocationBatchId);
            // Update allocation batch with actual allocated count
            allocationBatch.setQuantityAllocated(updatedCount);
            allocationBatch = allocationBatchRepository.save(allocationBatch); 

            log.info("单号分配成功(持有锁), 分配批次ID: {}, 目标渠道ID: {}, 客户ID: {}, 分配数量: {}", 
                     allocationBatchId, channelId, customerAccountId, updatedCount);
            return TrackingNumberAllocationResultDTO.builder()
                    .allocationBatch(allocationBatch)
                    .build();

        } catch (InterruptedException e) {
             Thread.currentThread().interrupt();
            log.error("获取渠道分配锁时被中断, 目标渠道ID: {}, Key: {}", channelId, lockKey, e);
            throw new ServiceException("系统处理中断，请稍后重试");
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("释放渠道分配锁, 目标渠道ID: {}, Key: {}", channelId, lockKey);
            }
        }
    }

    @Override
    public PageResult<TrackingNumberImportBatch> pageQueryImportBatches(Long uploaderId, Long channelId, LocalDateTime startTime, LocalDateTime endTime, int pageNo, int pageSize) {
        PageResult<TrackingNumberImportBatch> pageResult = importBatchRepository.pageQuery(uploaderId, channelId, startTime, endTime, pageNo, pageSize);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return pageResult;
        }
        Set<Long> uploaderIds = pageResult.getList().stream()
                .map(TrackingNumberImportBatch::getUploaderId)
                .filter(id -> id != null)
                .collect(Collectors.toSet());
        Set<Long> channelIds = pageResult.getList().stream()
                .map(TrackingNumberImportBatch::getChannelId)
                .filter(id -> id != null)
                .collect(Collectors.toSet());
        Map<Long, String> userIdToNameMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(uploaderIds)) {
            List<UserEntity> users = userRepository.listByIds(uploaderIds);
            userIdToNameMap = users.stream()
                    .collect(Collectors.toMap(UserEntity::getId, UserEntity::getNickname, (existing, replacement) -> existing));
        }
        Map<Long, TrackingNumberChannel> channelMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(channelIds)) {
            List<TrackingNumberChannel> channels = trackingNumberChannelRepository.findByIds(channelIds);
            channelMap = channels.stream()
                           .collect(Collectors.toMap(TrackingNumberChannel::getId, Function.identity()));
        }
        Map<Long, String> finalUserIdToNameMap = userIdToNameMap;
        Map<Long, TrackingNumberChannel> finalChannelMap = channelMap;
        pageResult.getList().forEach(batch -> {
            if (batch.getUploaderId() != null) {
                batch.setUploaderName(finalUserIdToNameMap.getOrDefault(batch.getUploaderId(), "未知用户"));
            }
            if (batch.getChannelId() != null) {
                TrackingNumberChannel channel = finalChannelMap.get(batch.getChannelId());
                if (channel != null) {
                    batch.setChannelCode(channel.getChannelCode());
                    batch.setChannelName(channel.getChannelName());
                }
            }
        });
        return pageResult;
    }

    @Override
    public PageResult<TrackingNumberAllocationBatch> pageQueryAllocationBatches(Long allocatorId,
                                                                                Long requestedLocationId,
                                                                                Long requestedShipmentTypeId,
                                                                                LocalDateTime startTime,
                                                                                LocalDateTime endTime,
                                                                                int pageNo,
                                                                                int pageSize) {
        PageResult<TrackingNumberAllocationBatch> pageResult = allocationBatchRepository.pageQuery(allocatorId, requestedLocationId, requestedShipmentTypeId, startTime, endTime, pageNo, pageSize);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return pageResult;
        }

        // Collect all relevant user IDs (allocators and customers)
        Set<Long> userIdsToQuery = new HashSet<>();
        pageResult.getList().forEach(batch -> {
            if (batch.getAllocatorId() != null) {
                userIdsToQuery.add(batch.getAllocatorId());
            }
            if (batch.getCustomerAccountId() != null) {
                userIdsToQuery.add(batch.getCustomerAccountId());
            }
        });

        // Query user information for all unique IDs
        Map<Long, String> userIdToNameMap = Collections.emptyMap();
        if (!userIdsToQuery.isEmpty()) {
            List<UserEntity> users = userRepository.listByIds(userIdsToQuery);
            // Create map from ID to Nickname
            userIdToNameMap = users.stream()
                    .collect(Collectors.toMap(UserEntity::getId, UserEntity::getNickname, (existing, replacement) -> existing)); // Handle potential duplicates
        }

        // Populate allocatorName and customerAccountName
        Map<Long, String> finalUserIdToNameMap = userIdToNameMap;
        pageResult.getList().forEach(batch -> {
            // Populate allocator name
            if (batch.getAllocatorId() != null) {
                batch.setAllocatorName(finalUserIdToNameMap.getOrDefault(batch.getAllocatorId(), "未知分配者"));
            }
            // Populate customer account name
            if (batch.getCustomerAccountId() != null) {
                batch.setCustomerAccountName(finalUserIdToNameMap.getOrDefault(batch.getCustomerAccountId(), "未知客户"));
            }
            // Channel info population logic was previously removed, keeping it removed.
            // If needed, it should be added back here based on requestedLocationId/requestedShipmentTypeId.
        });

        return pageResult;
    }

    @Override
    public List<TrackingNumberPool> findAllocatedNumbersByBatchId(Long allocationBatchId) {
        return poolRepository.findByAllocationBatchId(allocationBatchId);
    }

    @Override
    public void exportAllocatedNumbers(Long allocationBatchId, HttpServletResponse response) {
        TrackingNumberAllocationBatch batchInfo = allocationBatchRepository.findById(allocationBatchId);
        if (batchInfo == null) {
            throw new ServiceException("分配批次不存在, ID: " + allocationBatchId);
        }

        // Get customer info
        String customerName = "未知客户"; // Default name
        if (batchInfo.getCustomerAccountId() != null) {
            UserEntity customer = userRepository.getById(batchInfo.getCustomerAccountId()); // Assuming getById exists
            if (customer != null && StrUtil.isNotBlank(customer.getNickname())) {
                customerName = customer.getNickname();
            }
        }

        List<TrackingNumberPool> allocatedNumbers = findAllocatedNumbersByBatchId(allocationBatchId);
        if (CollUtil.isEmpty(allocatedNumbers)) {
            throw new ServiceException("该分配批次下没有找到已分配的单号");
        }

        List<AllocatedTrackingNumberExcelRow> excelData = allocatedNumbers.stream()
                .map(pool -> AllocatedTrackingNumberExcelRow.builder()
                        .trackingNumber(pool.getTrackingNumber())
                        .build())
                .collect(Collectors.toList());

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        int numberOfTrackings = allocatedNumbers.size();
        // Construct new filename with customer name
        String fileName = String.format("分配单号_%s_批次%d_%d条", customerName, allocationBatchId, numberOfTrackings);
        try {
            // Sanitize customerName for filename if necessary (removing invalid characters)
            // For simplicity, assuming customerName is reasonably safe here.
            fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        } catch (IOException e) {
             log.error("设置Excel导出文件名失败, 分配批次ID: {}", allocationBatchId, e);
             throw new ServiceException("导出文件名编码错误");
        }

        try (ServletOutputStream outputStream = response.getOutputStream()){
            EasyExcel.write(outputStream, AllocatedTrackingNumberExcelRow.class)
                    .sheet("分配单号")
                    .doWrite(excelData);

            try {
                allocationBatchRepository.updateExportedStatus(allocationBatchId, true);
                log.info("成功更新分配批次导出状态, 批次ID: {}", allocationBatchId);
            } catch (Exception updateEx) {
                log.error("更新分配批次导出状态失败, 批次ID: {}", allocationBatchId, updateEx);
            }

        } catch (IOException e) {
             log.error("导出已分配单号Excel失败, 分配批次ID: {}", allocationBatchId, e);
             throw new ServiceException("写入Excel文件时发生错误");
        }
    }

    @Override
    public void printAllocatedNumbers(Long allocationBatchId, HttpServletResponse response) {
        log.warn("打印功能尚未完全实现, allocationBatchId: {}", allocationBatchId);
        try {
           exportAllocatedNumbers(allocationBatchId, response);
           try {
                allocationBatchRepository.updatePrintedStatus(allocationBatchId, true);
                log.info("成功更新分配批次打印状态, 批次ID: {}", allocationBatchId);
            } catch (Exception updateEx) {
                log.error("更新分配批次打印状态失败, 批次ID: {}", allocationBatchId, updateEx);
            }
        } catch (Exception e) {
            log.error("打印(导出)单号失败, 分配批次ID: {}", allocationBatchId, e);
             response.reset();
             response.setContentType("application/json");
             response.setCharacterEncoding(StandardCharsets.UTF_8.name());
             try {
                 response.getWriter().write("{\"code\": 500, \"message\": \"打印失败: " + e.getMessage() + "\"}");
             } catch (IOException ioException) {
                 log.error("写入打印失败响应时出错", ioException);
             }
        }
    }

    @Override
    public List<ChannelAvailableCountDTO> getAvailableCounts() {
        log.info("开始查询所有活动渠道的可用单号数量");

        List<TrackingNumberChannel> activeChannels = trackingNumberChannelRepository.findAllActiveChannels();
        if (CollUtil.isEmpty(activeChannels)) {
            log.info("没有找到活动的单号渠道");
            return Collections.emptyList();
        }
        log.debug("找到 {} 个活动渠道", activeChannels.size());

        List<Long> channelIds = activeChannels.stream().map(TrackingNumberChannel::getId).collect(Collectors.toList());
        List<Long> locationIds = activeChannels.stream().map(TrackingNumberChannel::getLocationId).distinct().collect(Collectors.toList());
        List<Long> shipmentTypeIds = activeChannels.stream().map(TrackingNumberChannel::getShipmentTypeId).distinct().collect(Collectors.toList());
        List<Long> carrierIds = activeChannels.stream().map(TrackingNumberChannel::getCarrierId).distinct().collect(Collectors.toList());

        Map<Long, Long> availableCountsMap = poolRepository.countAvailableByChannelIds(channelIds);
        log.debug("查询到 {} 个渠道的可用数量信息", availableCountsMap.size());

        Map<Long, Location> locationMap = locationIds.isEmpty() ? Collections.emptyMap() :
                locationRepository.findByIds(locationIds).stream().collect(Collectors.toMap(Location::getId, Function.identity()));
        Map<Long, ShipmentType> shipmentTypeMap = shipmentTypeIds.isEmpty() ? Collections.emptyMap() :
                shipmentTypeRepository.findByIds(shipmentTypeIds).stream().collect(Collectors.toMap(ShipmentType::getId, Function.identity()));
        Map<Long, Carrier> carrierMap = carrierIds.isEmpty() ? Collections.emptyMap() :
                carrierRepository.findByIds(carrierIds).stream().collect(Collectors.toMap(Carrier::getId, Function.identity()));
        log.debug("查询到地点: {}, 货物类型: {}, 承运商: {} 条记录", locationMap.size(), shipmentTypeMap.size(), carrierMap.size());

        List<ChannelAvailableCountDTO> resultList = new ArrayList<>();
        for (TrackingNumberChannel channel : activeChannels) {
            Long currentChannelId = channel.getId();
            Long count = availableCountsMap.getOrDefault(currentChannelId, 0L);

            Location location = locationMap.get(channel.getLocationId());
            ShipmentType shipmentType = shipmentTypeMap.get(channel.getShipmentTypeId());
            Carrier carrier = carrierMap.get(channel.getCarrierId());

            resultList.add(ChannelAvailableCountDTO.builder()
                    .channelId(currentChannelId)
                    .channelCode(channel.getChannelCode())
                    .channelName(channel.getChannelName())
                    .carrierId(channel.getCarrierId())
                    .carrierName(carrier != null ? carrier.getName() : "未知")
                    .locationId(channel.getLocationId())
                    .locationName(location != null ? location.getName() : "未知")
                    .shipmentTypeId(channel.getShipmentTypeId())
                    .shipmentTypeName(shipmentType != null ? shipmentType.getName() : "未知")
                    .availableCount(count)
                    .build());
        }

        log.info("查询活动渠道可用单号数量完成，共返回 {} 条记录", resultList.size());
        return resultList;
    }
} 