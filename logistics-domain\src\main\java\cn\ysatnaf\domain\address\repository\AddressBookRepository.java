package cn.ysatnaf.domain.address.repository;

import cn.ysatnaf.domain.address.model.entity.AddressBookEntity;
import cn.ysatnaf.types.common.PageResult;

import java.util.List;

/**
 * AddressBookRepository
 *
 * <AUTHOR>
 * @date 2023/12/22 10:42
 */
public interface AddressBookRepository {
    Boolean insert(AddressBookEntity addressBookEntity);

    Boolean delete(Long id);

    Boolean updateById(AddressBookEntity addressBookEntity);

    PageResult<AddressBookEntity> page(String openid, Integer pageNo, Integer pageSize);

    AddressBookEntity getById(Long sendAddressBookId);

    List<AddressBookEntity> listByOpenid(String openid);

    void updateBatchByIds(List<AddressBookEntity> bookEntities);

    AddressBookEntity getDefaultByOpenid(String openid);

    AddressBookEntity getFirstOneByOpenid(String openid);
}
