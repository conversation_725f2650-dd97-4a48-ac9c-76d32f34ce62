package cn.ysatnaf.domain.statistics.model.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TodayAmountDetailDTO {
    // 今日运单金额
    private BigDecimal totalAmount;
    
    // 与昨日相比变化百分比
    private BigDecimal compareYesterday;
    
    // 昨日运单金额
    private BigDecimal yesterdayAmount;
    
    // 昨日与前日相比变化百分比
    private BigDecimal yesterdayCompare;
    
    // 时段分布数据
    private List<HourDistributionDTO> hourDistribution;
    
    // 今日模板类型金额分布
    private List<TemplateDistributionDTO> templateDistribution;
    
    // 今日与昨日模板类型金额对比
    private ComparisonDTO comparison;
    
    @Data
    public static class HourDistributionDTO {
        private String hour;        // 格式: "10:00"
        private BigDecimal amount;  // 该小时运单金额
    }
    
    @Data
    public static class TemplateDistributionDTO {
        private String name;            // 模板类型
        private BigDecimal amount;      // 金额
        private BigDecimal percentage;  // 百分比
    }
    
    @Data
    public static class ComparisonDTO {
        private List<String> types;              // 模板类型
        private List<BigDecimal> todayAmounts;   // 今日各类型金额
        private List<BigDecimal> yesterdayAmounts; // 昨日各类型金额
    }
}