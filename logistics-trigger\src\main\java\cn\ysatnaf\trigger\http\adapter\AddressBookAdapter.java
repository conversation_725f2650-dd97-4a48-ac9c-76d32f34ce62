package cn.ysatnaf.trigger.http.adapter;

import cn.ysatnaf.domain.address.model.entity.AddressBookEntity;
import cn.ysatnaf.trigger.http.req.address.AddressBookAddReq;
import cn.ysatnaf.trigger.http.req.address.AddressBookUpdateReq;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * AddressBookConverter
 *
 * <AUTHOR>
 * @date 2023/12/22 10:46
 */
@Mapper
public interface AddressBookAdapter {
    AddressBookAdapter INSTANCE = Mappers.getMapper(AddressBookAdapter.class);

    AddressBookEntity req2Entity(AddressBookAddReq req);

    AddressBookEntity req2Entity(AddressBookUpdateReq req);
}
