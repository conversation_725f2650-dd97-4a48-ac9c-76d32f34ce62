package cn.ysatnaf.domain.shippingfeetemplate.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("tb_shipping_fee_template_user")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ShippingFeeTemplateUserPO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private Integer type;

    private Long templateId;
}
