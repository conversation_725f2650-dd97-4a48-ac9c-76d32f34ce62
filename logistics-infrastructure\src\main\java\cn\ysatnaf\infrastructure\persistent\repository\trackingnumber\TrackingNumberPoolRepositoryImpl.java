package cn.ysatnaf.infrastructure.persistent.repository.trackingnumber;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberPool;
import cn.ysatnaf.domain.trackingnumber.model.valobj.TrackingNumberStatus;
import cn.ysatnaf.domain.trackingnumber.repository.TrackingNumberPoolRepository;
import cn.ysatnaf.infrastructure.persistent.converter.trackingnumber.TrackingNumberPoolConverter;
import cn.ysatnaf.infrastructure.persistent.dao.trackingnumber.TrackingNumberPoolDao;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberPoolPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预报单号池仓库实现
 * <AUTHOR>
 */
@Repository
public class TrackingNumberPoolRepositoryImpl extends ServiceImpl<TrackingNumberPoolDao, TrackingNumberPoolPO> implements TrackingNumberPoolRepository {

    private final TrackingNumberPoolConverter converter = TrackingNumberPoolConverter.INSTANCE;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<TrackingNumberPool> trackingNumbers) {
        if (CollUtil.isEmpty(trackingNumbers)) {
            return 0;
        }
        List<TrackingNumberPoolPO> poList = converter.toPOList(trackingNumbers);
        // 注意：MyBatis Plus 默认的 saveBatch 可能不是所有数据库都高效支持批量插入
        // 这里可以使用 DAO 层定义的 insertBatchSomeColumn (如果数据库支持)
        // 或者依赖 MP 的 saveBatch (它可能是循环插入)
        // 为了演示，我们假设使用 MP 的 saveBatch
        boolean success = saveBatch(poList);
        return success ? poList.size() : 0; // MP saveBatch 返回 boolean
        // 如果使用 DAO 的 insertBatchSomeColumn:
        // return getBaseMapper().insertBatchSomeColumn(poList);
    }

    @Override
    public boolean exists(Long channelId, String trackingNumber) {
        LambdaQueryWrapper<TrackingNumberPoolPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TrackingNumberPoolPO::getChannelId, channelId)
                    .eq(TrackingNumberPoolPO::getTrackingNumber, trackingNumber)
                    .last("LIMIT 1");
        return count(queryWrapper) > 0;
    }

    @Override
    public long countAvailableByChannel(Long channelId) {
        LambdaQueryWrapper<TrackingNumberPoolPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TrackingNumberPoolPO::getChannelId, channelId)
                    .eq(TrackingNumberPoolPO::getStatus, TrackingNumberStatus.AVAILABLE);
        return count(queryWrapper);
    }
    
    @Override
    public Map<Long, Long> countAvailableByChannelIds(Collection<Long> channelIds) {
        if (CollUtil.isEmpty(channelIds)) {
            return Collections.emptyMap();
        }
        List<Map<String, Object>> results = baseMapper.countAvailableByChannelIds(channelIds, TrackingNumberStatus.AVAILABLE.getCode());
        
        // 将 List<Map<String, Object>> 转换为 Map<Long, Long>
        return results.stream().collect(Collectors.toMap(
                map -> ((Number) map.get("channelId")).longValue(), // Key: channelId
                map -> ((Number) map.get("availableCount")).longValue()  // Value: count
        ));
    }

    @Override
    public List<TrackingNumberPool> findAvailableByChannel(Long channelId, int limit) {
        LambdaQueryWrapper<TrackingNumberPoolPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TrackingNumberPoolPO::getChannelId, channelId)
                    .eq(TrackingNumberPoolPO::getStatus, TrackingNumberStatus.AVAILABLE)
                    .last("LIMIT " + limit);
        List<TrackingNumberPoolPO> poList = list(queryWrapper);
        return converter.toEntityList(poList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateStatusAndAllocationBatchByIds(List<Long> ids, TrackingNumberStatus status, Long allocationBatchId) {
        if (CollUtil.isEmpty(ids) || status == null) {
            return 0;
        }
        LambdaUpdateWrapper<TrackingNumberPoolPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.in(TrackingNumberPoolPO::getId, ids)
                     .set(TrackingNumberPoolPO::getStatus, status.getCode())
                     .set(allocationBatchId != null, TrackingNumberPoolPO::getAllocationBatchId, allocationBatchId);
        return baseMapper.update(null, updateWrapper); // 使用 baseMapper.update
    }

    @Override
    public List<TrackingNumberPool> findByAllocationBatchId(Long allocationBatchId) {
        LambdaQueryWrapper<TrackingNumberPoolPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TrackingNumberPoolPO::getAllocationBatchId, allocationBatchId);
        List<TrackingNumberPoolPO> poList = list(queryWrapper);
        return converter.toEntityList(poList);
    }

    @Override
    public Set<String> findExistingNumbers(Long channelId, List<String> trackingNumbers) {
         if (channelId == null || CollUtil.isEmpty(trackingNumbers)) {
            return Collections.emptySet();
        }
        return baseMapper.selectExistingNumbers(channelId, trackingNumbers);
    }

    @Override
    public Optional<TrackingNumberPool> findByTrackingNumber(String trackingNumber) {
        if (StrUtil.isBlank(trackingNumber)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<TrackingNumberPoolPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TrackingNumberPoolPO::getTrackingNumber, trackingNumber)
                    .last("LIMIT 1"); // Assuming tracking number should be unique or we take the first
        TrackingNumberPoolPO po = getOne(queryWrapper);
        return Optional.ofNullable(converter.toEntity(po));
    }
} 