# 换箱接口文档

## 接口基本信息

| 项目     | 内容                                           |
| -------- | ---------------------------------------------- |
| 接口名称 | 换箱                                           |
| 接口地址 | `/web/parcelSorting/changeBox`                 |
| 请求方式 | POST                                           |
| 请求类型 | application/json                               |
| 功能描述 | 将已分箱的包裹从当前箱子转移到指定的目标箱子中 |

## 请求参数

### 请求头

| 参数名       | 类型   | 必填 | 说明             |
| ------------ | ------ | ---- | ---------------- |
| Content-Type | String | 是   | application/json |

### 请求体

| 参数名 | 类型             | 必填 | 校验规则  | 说明                                     |
| ------ | ---------------- | ---- | --------- | ---------------------------------------- |
| ids    | List&lt;Long&gt; | 是   | @NotEmpty | 分箱包裹 ID 列表，需要转移的包裹 ID 数组 |
| boxId  | Long             | 是   | @NotNull  | 要转换的箱子 ID，目标箱子的唯一标识      |

### 请求示例

```json
{
  "ids": [1001, 1002, 1003],
  "boxId": 2001
}
```

## 响应参数

### 响应体结构

| 参数名 | 类型    | 说明                         |
| ------ | ------- | ---------------------------- |
| code   | Integer | 响应状态码，0 表示成功       |
| data   | Boolean | 业务数据，换箱操作结果       |
| msg    | String  | 响应消息，错误时包含错误描述 |

### 成功响应示例

```json
{
  "code": 0,
  "data": true,
  "msg": ""
}
```

### 错误响应示例

```json
{
  "code": 400,
  "data": null,
  "msg": "请求参数不正确"
}
```

## 错误码说明

| 错误码 | 错误描述       | 解决方案                 |
| ------ | -------------- | ------------------------ |
| 0      | 成功           | 无需处理                 |
| 400    | 请求参数不正确 | 检查请求参数格式和必填项 |
| 401    | 账号未登录     | 重新登录获取有效 token   |
| 403    | 没有该操作权限 | 联系管理员分配权限       |
| 500    | 系统异常       | 联系技术支持             |
| 9999   | 服务器内部异常 | 联系管理员               |

## 业务逻辑说明

1. **参数校验**：

   - `ids` 不能为空，至少包含一个包裹 ID
   - `boxId` 不能为空，必须是有效的箱子 ID

2. **业务流程**：

   - 根据包裹 ID 列表查找对应的包裹记录
   - 验证目标箱子是否存在且可用
   - 批量更新包裹记录的箱子归属
   - 返回操作结果

3. **注意事项**：
   - 换箱操作会直接修改包裹的箱子归属关系
   - 操作不可逆，请确认后再执行
   - 目标箱子必须与源箱子属于同一个分箱记录

## 相关接口

- [装箱接口](./装箱接口文档.md) - 将包裹放入箱子
- [拆箱接口](./拆箱接口文档.md) - 从箱子中移除包裹
- [包裹列表接口](./包裹列表接口文档.md) - 查询箱子中的包裹列表
- [箱子列表接口](./箱子列表接口文档.md) - 查询分箱记录下的箱子列表

## 更新日志

| 版本 | 更新时间   | 更新内容                   | 更新人    |
| ---- | ---------- | -------------------------- | --------- |
| v1.0 | 2024-03-29 | 初始版本，支持基础换箱功能 | Chen Hang |
