package cn.ysatnaf.domain.order.adapter;

import cn.ysatnaf.domain.order.model.entity.OrderEntity;
import cn.ysatnaf.domain.order.model.res.MpOrderPageRes;
import cn.ysatnaf.domain.order.model.res.OrderSearchRes;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Mapper
public interface OrderAdapter {
    OrderAdapter INSTANCE = Mappers.getMapper(OrderAdapter.class);


    OrderSearchRes entity2searchRes(OrderEntity orderEntity);

    @Mappings(@Mapping(source = "receiverMunicipalName", target = "receiverCityName"))
    List<OrderSearchRes> entityList2searchResList(List<OrderEntity> orderEntityList);

    List<MpOrderPageRes> entity2pageMpResList(List<OrderEntity> list);
}
