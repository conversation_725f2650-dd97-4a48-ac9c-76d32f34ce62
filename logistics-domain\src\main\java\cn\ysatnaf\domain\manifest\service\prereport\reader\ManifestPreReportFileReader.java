package cn.ysatnaf.domain.manifest.service.prereport.reader;

import cn.ysatnaf.domain.manifest.model.excel.ManifestTemplateExcelListener;
import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Slf4j
@Component
public class ManifestPreReportFileReader implements ExcelFileReader<ManifestPreReportRow>{
    @Override
    public List<ManifestPreReportRow> read(MultipartFile file) {
        // 使用EasyExcel读取文件
        ManifestTemplateExcelListener manifestTemplateExcelListener = new ManifestTemplateExcelListener();
        try {
            EasyExcel.read(file.getInputStream(), ManifestPreReportRow.class, manifestTemplateExcelListener)
                    .sheet()
                    .headRowNumber(5).doRead();
        } catch (IOException e) {
            log.error("读取上传文件失败:", e);
            throw new ServiceException("读取上传文件失败，请联系管理员");
        }
        return manifestTemplateExcelListener.getParsedData();
    }
}
