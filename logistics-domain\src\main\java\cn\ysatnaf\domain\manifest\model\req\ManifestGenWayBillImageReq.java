package cn.ysatnaf.domain.manifest.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "生成物流面单入参")
@Data
public class ManifestGenWayBillImageReq {

    @Schema(description = "运单号")
    @NotBlank(message = "运单号不能为空")
    private String expressNumber;

    @Schema(description = "佐川单号")
    @NotBlank(message = "佐川单号不能为空")
    private String sawagaNumber;

    @Schema(description = "转单号")
    private String transferredTrackingNumber;

    @Schema(description = "邮编")
    @NotBlank(message = "邮编不能为空")
    private String receiverZipCode;

    @Schema(description = "收件人")
    @NotBlank(message = "收件人不能为空")
    private String receiverName;

    @Schema(description = "收件地址")
    @NotBlank(message = "收件地址不能为空")
    private String receiverAddress;

    @Schema(description = "收件人电话")
    @NotBlank(message = "收件人电话不能为空")
    private String receiverPhone;

    @Schema(description = "重量（kg），打印面单第三步填的", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private BigDecimal weight;

    @Schema(description = "运费模板类型: 1-普通模板;2-带电模板;3-投函模板")
    private Integer shippingFeeTemplateType;

    public String getFormattedZipCode() {
        return receiverZipCode.substring(0, 3) + "-" + receiverZipCode.substring(3);
    }
}
