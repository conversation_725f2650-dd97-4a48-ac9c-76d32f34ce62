package cn.ysatnaf.domain.statistics.service.impl;

import cn.ysatnaf.domain.manifest.repository.ManifestRepository;
import cn.ysatnaf.domain.statistics.calculator.DateTimeUtil;
import cn.ysatnaf.domain.statistics.model.dto.TimeAnalysisDTO;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class TimeAnalysisService {


    @Qualifier("queryExecutor")
    private final ExecutorService executorService;

    private final ManifestRepository manifestRepository;

    public TimeAnalysisDTO getTimeAnalysisData(String unit, LocalDate startDate, LocalDate endDate, int startHour) {
        try {
            // 标准化时间单位
            unit = normalizeTimeUnit(unit);

            // 计算当前周期的时间范围
            LocalDateTime currentStartTime = DateTimeUtil.getStartOfDayWithHour(startDate, startHour);
            LocalDateTime currentEndTime = DateTimeUtil.getEndOfDayWithHour(endDate, startHour);

            // 计算上一周期的时间范围
            int periodLengthDays = (int) ChronoUnit.DAYS.between(startDate, endDate) + 1;
            LocalDateTime prevStartTime = currentStartTime.minusDays(periodLengthDays);
            LocalDateTime prevEndTime = currentEndTime.minusDays(periodLengthDays);

            // 计算时间跨度（用于自定义范围的坐标轴选择）
            long daysBetween = ChronoUnit.DAYS.between(startDate, endDate) + 1;

            TimeAnalysisDTO result = new TimeAnalysisDTO();

            // 并行获取所有需要的数据
            CompletableFuture<Map<String, Object>> currentSummaryFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getPeriodSummary(currentStartTime, currentEndTime),
                    executorService
            );

            CompletableFuture<Map<String, Object>> prevSummaryFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getPeriodSummary(prevStartTime, prevEndTime),
                    executorService
            );

            String finalUnit = unit;
            CompletableFuture<List<Map<String, Object>>> currentTrendFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTrendData(currentStartTime, currentEndTime, finalUnit, String.valueOf(daysBetween)),
                    executorService
            );

            String finalUnit1 = unit;
            CompletableFuture<List<Map<String, Object>>> prevTrendFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTrendData(prevStartTime, prevEndTime, finalUnit1, String.valueOf(daysBetween)),
                    executorService
            );

            CompletableFuture<List<Map<String, Object>>> templateDistributionFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getTemplateDistribution(currentStartTime, currentEndTime),
                    executorService
            );

            CompletableFuture<List<Map<String, Object>>> heatmapDataFuture = CompletableFuture.supplyAsync(
                    () -> manifestRepository.getHeatmapData(currentStartTime, currentEndTime),
                    executorService
            );

            // 等待所有异步任务完成
            CompletableFuture.allOf(
                    currentSummaryFuture, prevSummaryFuture,
                    currentTrendFuture, prevTrendFuture,
                    templateDistributionFuture, heatmapDataFuture
            ).join();

            // 处理数据
            processSummaryData(
                    result,
                    currentSummaryFuture.get(),
                    prevSummaryFuture.get()
            );

            processTrendData(
                    result,
                    currentTrendFuture.get(),
                    prevTrendFuture.get(),
                    unit,
                    currentStartTime,
                    currentEndTime,
                    daysBetween
            );

            processComparisonData(
                    result,
                    templateDistributionFuture.get()
            );

            processHeatmapData(
                    result,
                    heatmapDataFuture.get()
            );

            return result;

        } catch (InterruptedException | ExecutionException e) {
            log.error("Error getting time analysis data", e);
            throw new RuntimeException("获取时间维度分析数据失败", e);
        }
    }

    /**
     * 处理摘要数据
     */
    private void processSummaryData(
            TimeAnalysisDTO result,
            Map<String, Object> currentSummary,
            Map<String, Object> prevSummary) {

        TimeAnalysisDTO.StatisticsSummary summary = new TimeAnalysisDTO.StatisticsSummary();

        // 总发货数量
        int totalQuantity = ((Number) currentSummary.getOrDefault("total_quantity", 0)).intValue();
        int prevTotalQuantity = ((Number) prevSummary.getOrDefault("total_quantity", 0)).intValue();
        summary.setTotalQuantity(totalQuantity);

        // 较上期变化百分比（修改字段名为periodOnPeriodChange）
        BigDecimal quantityChange = calculatePercentageChange(totalQuantity, prevTotalQuantity);
        summary.setQuantityPeriodOnPeriodChange(quantityChange);

        // 总发货金额
        BigDecimal totalAmount = new BigDecimal(currentSummary.getOrDefault("total_amount", 0).toString());
        BigDecimal prevTotalAmount = new BigDecimal(prevSummary.getOrDefault("total_amount", 0).toString());
        summary.setTotalAmount(totalAmount);

        // 较上期变化百分比（修改字段名为periodOnPeriodChange）
        BigDecimal amountChange = calculatePercentageChange(totalAmount, prevTotalAmount);
        summary.setAmountPeriodOnPeriodChange(amountChange);

        // 平均日发货数量
        int avgDailyQuantity = ((Number) currentSummary.getOrDefault("avg_daily_quantity", 0)).intValue();
        int prevAvgDailyQuantity = ((Number) prevSummary.getOrDefault("avg_daily_quantity", 0)).intValue();
        summary.setAvgDailyQuantity(avgDailyQuantity);

        // 较上期变化百分比（修改字段名为periodOnPeriodChange）
        BigDecimal avgQuantityChange = calculatePercentageChange(avgDailyQuantity, prevAvgDailyQuantity);
        summary.setAvgQuantityPeriodOnPeriodChange(avgQuantityChange);

        // 平均日发货金额
        BigDecimal avgDailyAmount = new BigDecimal(currentSummary.getOrDefault("avg_daily_amount", 0).toString());
        BigDecimal prevAvgDailyAmount = new BigDecimal(prevSummary.getOrDefault("avg_daily_amount", 0).toString());
        summary.setAvgDailyAmount(avgDailyAmount);

        // 较上期变化百分比（修改字段名为periodOnPeriodChange）
        BigDecimal avgAmountChange = calculatePercentageChange(avgDailyAmount, prevAvgDailyAmount);
        summary.setAvgAmountPeriodOnPeriodChange(avgAmountChange);

        result.setSummary(summary);
    }

    /**
     * 处理趋势数据（修改：不再支持去年同期）
     */
    private void processTrendData(
            TimeAnalysisDTO result,
            List<Map<String, Object>> currentData,
            List<Map<String, Object>> prevData,
            String unit,
            LocalDateTime startTime,
            LocalDateTime endTime,
            long daysBetween) {

        TimeAnalysisDTO.TrendData trendData = new TimeAnalysisDTO.TrendData();

        // 根据时间跨度和单位自动生成标签
        List<String> timeLabels = getCustomTimeLabels(unit, startTime, endTime, daysBetween);

        // 创建映射，用于快速查找数据
        Map<String, Integer> currentQuantityMap = new HashMap<>();
        Map<String, BigDecimal> currentAmountMap = new HashMap<>();
        Map<String, Integer> prevQuantityMap = new HashMap<>();
        Map<String, BigDecimal> prevAmountMap = new HashMap<>();

        // 填充当前周期数据映射
        for (Map<String, Object> data : currentData) {
            String label = data.get("time_label").toString();
            int quantity = ((Number) data.get("quantity")).intValue();
            BigDecimal amount = new BigDecimal(data.get("amount").toString());

            currentQuantityMap.put(label, quantity);
            currentAmountMap.put(label, amount);
        }

        // 填充上个周期数据映射
        for (Map<String, Object> data : prevData) {
            String label = data.get("time_label").toString();
            int quantity = ((Number) data.get("quantity")).intValue();
            BigDecimal amount = new BigDecimal(data.get("amount").toString());

            prevQuantityMap.put(label, quantity);
            prevAmountMap.put(label, amount);
        }

        // 初始化结果数组
        List<Integer> currentQuantity = new ArrayList<>();
        List<Integer> prevPeriodQuantity = new ArrayList<>();
        List<BigDecimal> currentAmount = new ArrayList<>();
        List<BigDecimal> prevPeriodAmount = new ArrayList<>();

        // 填充结果数组
        for (String label : timeLabels) {
            currentQuantity.add(currentQuantityMap.getOrDefault(label, 0));
            prevPeriodQuantity.add(prevQuantityMap.getOrDefault(label, 0));
            currentAmount.add(currentAmountMap.getOrDefault(label, BigDecimal.ZERO));
            prevPeriodAmount.add(prevAmountMap.getOrDefault(label, BigDecimal.ZERO));
        }

        // 设置结果
        trendData.setTimeLabels(timeLabels);
        trendData.setCurrentQuantity(currentQuantity);
        trendData.setPrevPeriodQuantity(prevPeriodQuantity);
        trendData.setCurrentAmount(currentAmount);
        trendData.setPrevPeriodAmount(prevPeriodAmount);

        result.setTrend(trendData);
    }

    /**
     * 处理对比数据
     */
    private void processComparisonData(
            TimeAnalysisDTO result,
            List<Map<String, Object>> templateDistribution) {

        TimeAnalysisDTO.ComparisonData comparison = new TimeAnalysisDTO.ComparisonData();

        List<TimeAnalysisDTO.TypeDistribution> quantityDistribution = new ArrayList<>();
        List<TimeAnalysisDTO.TypeDistribution> amountDistribution = new ArrayList<>();

        // 计算总量
        int totalQuantity = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;

        for (Map<String, Object> item : templateDistribution) {
            int quantity = ((Number) item.get("quantity")).intValue();
            BigDecimal amount = new BigDecimal(item.get("amount").toString());

            totalQuantity += quantity;
            totalAmount = totalAmount.add(amount);
        }

        // 创建类型分布数据
        for (Map<String, Object> item : templateDistribution) {
            String type = (String) item.get("template_type");
            int quantity = ((Number) item.get("quantity")).intValue();
            BigDecimal amount = new BigDecimal(item.get("amount").toString());

            // 数量分布
            TimeAnalysisDTO.TypeDistribution quantityItem = new TimeAnalysisDTO.TypeDistribution();
            quantityItem.setName(type);
            quantityItem.setValue(quantity);

            // 计算百分比
            BigDecimal percentageQuantity;
            if (totalQuantity > 0) {
                percentageQuantity = new BigDecimal(quantity * 100)
                        .divide(new BigDecimal(totalQuantity), 1, RoundingMode.HALF_UP);
            } else {
                percentageQuantity = BigDecimal.ZERO;
            }
            quantityItem.setPercentage(percentageQuantity);
            quantityDistribution.add(quantityItem);

            // 金额分布
            TimeAnalysisDTO.TypeDistribution amountItem = new TimeAnalysisDTO.TypeDistribution();
            amountItem.setName(type);
            amountItem.setValue(amount);

            // 计算百分比
            BigDecimal percentageAmount;
            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                percentageAmount = amount.multiply(new BigDecimal(100))
                        .divide(totalAmount, 1, RoundingMode.HALF_UP);
            } else {
                percentageAmount = BigDecimal.ZERO;
            }
            amountItem.setPercentage(percentageAmount);
            amountDistribution.add(amountItem);
        }

        comparison.setQuantityDistribution(quantityDistribution);
        comparison.setAmountDistribution(amountDistribution);

        result.setComparison(comparison);
    }

    /**
     * 处理热力图数据
     */
    private void processHeatmapData(
            TimeAnalysisDTO result,
            List<Map<String, Object>> heatmapData) {

        TimeAnalysisDTO.HeatmapData heatmap = new TimeAnalysisDTO.HeatmapData();

        // 初始化小时和星期标签
        List<String> hours = new ArrayList<>();
        for (int i = 0; i < 24; i++) {
            hours.add(String.format("%02d:00", i));
        }

        List<String> days = Arrays.asList("周一", "周二", "周三", "周四", "周五", "周六", "周日");

        // 初始化数据映射
        Map<String, Integer> dataMap = new HashMap<>();

        // 填充数据映射
        for (Map<String, Object> item : heatmapData) {
            int hour = ((Number) item.get("hour")).intValue();
            int dayOfWeek = ((Number) item.get("day_of_week")).intValue();
            int quantity = ((Number) item.get("quantity")).intValue();

            dataMap.put(hour + ":" + dayOfWeek, quantity);
        }

        // 构建热力图数据
        List<List<Integer>> data = new ArrayList<>();
        for (int day = 0; day < 7; day++) {
            for (int hour = 0; hour < 24; hour++) {
                List<Integer> point = new ArrayList<>();
                point.add(hour);  // X坐标
                point.add(day);   // Y坐标

                // 获取该时间点的值，默认为0
                int value = dataMap.getOrDefault(hour + ":" + day, 0);
                point.add(value); // 值

                data.add(point);
            }
        }

        heatmap.setHours(hours);
        heatmap.setDays(days);
        heatmap.setData(data);

        result.setHeatmap(heatmap);
    }

    /**
     * 根据自定义时间范围生成时间标签
     */
    private List<String> getCustomTimeLabels(String unit, LocalDateTime startTime, LocalDateTime endTime, long daysBetween) {
        if ("day".equals(unit)) {
            // 日维度用小时作为标签
            return IntStream.range(0, 24)
                    .mapToObj(i -> String.format("%02d:00", i))
                    .collect(Collectors.toList());
        } else if ("week".equals(unit)) {
            // 周维度用星期几作为标签
            return Arrays.asList("周一", "周二", "周三", "周四", "周五", "周六", "周日");
        } else if ("month".equals(unit)) {
            // 月维度用日期作为标签
            int days = endTime.toLocalDate().lengthOfMonth();
            return IntStream.rangeClosed(1, days)
                    .mapToObj(i -> String.format("%02d日", i))
                    .collect(Collectors.toList());
        } else if ("quarter".equals(unit)) {
            // 季度维度用月份作为标签
            return IntStream.rangeClosed(startTime.getMonthValue(), endTime.getMonthValue())
                    .mapToObj(i -> String.format("%02d月", i))
                    .collect(Collectors.toList());
        } else if ("year".equals(unit)) {
            // 年维度用月份作为标签
            return IntStream.rangeClosed(1, 12)
                    .mapToObj(i -> String.format("%02d月", i))
                    .collect(Collectors.toList());
        } else {
            // 自定义时间范围
            List<String> labels = new ArrayList<>();

            if (daysBetween > 180) {
                // 超过180天按月聚合
                LocalDate current = startTime.toLocalDate().withDayOfMonth(1);
                LocalDate end = endTime.toLocalDate().withDayOfMonth(1);

                while (!current.isAfter(end)) {
                    labels.add(current.format(DateTimeFormatter.ofPattern("yyyy-MM")));
                    current = current.plusMonths(1);
                }
            } else if (daysBetween > 30) {
                // 30-180天按周聚合
                LocalDate current = startTime.toLocalDate();
                // 调整到周一
                current = current.minusDays(current.getDayOfWeek().getValue() - 1);

                while (current.isBefore(endTime.toLocalDate())) {
                    labels.add(current.format(DateTimeFormatter.ofPattern("MM.dd")) + "周");
                    current = current.plusDays(7);
                }
            } else if (daysBetween > 7) {
                // 7-30天按日聚合
                LocalDate current = startTime.toLocalDate();
                LocalDate end = endTime.toLocalDate();

                while (!current.isAfter(end)) {
                    labels.add(current.format(DateTimeFormatter.ofPattern("MM-dd")));
                    current = current.plusDays(1);
                }
            } else if (daysBetween > 2) {
                // 2-7天按12小时聚合
                LocalDate current = startTime.toLocalDate();
                LocalDate end = endTime.toLocalDate();

                while (!current.isAfter(end)) {
                    labels.add(current.format(DateTimeFormatter.ofPattern("MM-dd")) + " 上午");
                    labels.add(current.format(DateTimeFormatter.ofPattern("MM-dd")) + " 下午");
                    current = current.plusDays(1);
                }
            } else {
                // 1-2天按小时聚合
                LocalDateTime current = startTime;

                while (current.isBefore(endTime)) {
                    labels.add(current.format(DateTimeFormatter.ofPattern("MM-dd HH:00")));
                    current = current.plusHours(1);
                }
            }

            return labels;
        }
    }

    /**
     * 标准化时间单位
     */
    private String normalizeTimeUnit(String unit) {
        if (unit == null) {
            return "day";
        }

        switch (unit.toLowerCase()) {
            case "day":
            case "week":
            case "month":
            case "quarter":
            case "year":
                return unit.toLowerCase();
            default:
                return "custom"; // 自定义时间范围
        }
    }

    /**
     * 计算百分比变化
     */
    private BigDecimal calculatePercentageChange(Number current, Number previous) {
        if (previous.doubleValue() == 0) {
            return BigDecimal.ZERO;
        }

        return new BigDecimal(
                (current.doubleValue() - previous.doubleValue()) * 100 / previous.doubleValue()
        ).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算百分比变化（BigDecimal版本）
     */
    private BigDecimal calculatePercentageChange(BigDecimal current, BigDecimal previous) {
        if (previous.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return current.subtract(previous)
                .multiply(new BigDecimal("100"))
                .divide(previous, 1, RoundingMode.HALF_UP);
    }

    /**
     * 创建从标签到索引的映射
     */
    private Map<String, Integer> createLabelToIndexMap(List<Map<String, Object>> dataList, String labelField) {
        Map<String, Integer> map = new HashMap<>();

        if (dataList == null || dataList.isEmpty()) {
            return map;
        }

        for (int i = 0; i < dataList.size(); i++) {
            Map<String, Object> data = dataList.get(i);
            if (data.containsKey(labelField)) {
                String label = data.get(labelField).toString();
                map.put(label, i);
            }
        }

        return map;
    }
}
