package cn.ysatnaf.domain.system.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.system.model.entity.SystemInfo;
import cn.ysatnaf.domain.system.model.req.SystemInfoUpdateReq;
import cn.ysatnaf.domain.system.service.SystemService;
import cn.ysatnaf.types.constants.CacheConstants;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class SystemServiceImpl implements SystemService {

    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public Boolean updateInfo(SystemInfoUpdateReq req) {
        stringRedisTemplate.opsForValue().set(CacheConstants.SYSTEM_INFO, JSON.toJSONString(req));
        return true;
    }

    @Override
    public SystemInfo getInfo() {
        String systemInfoJson = stringRedisTemplate.opsForValue().get(CacheConstants.SYSTEM_INFO);
        if (StrUtil.isNotBlank(systemInfoJson)) {
            return JSON.parseObject(systemInfoJson, SystemInfo.class);
        }
        SystemInfo systemInfo = new SystemInfo();
        stringRedisTemplate.opsForValue().set(CacheConstants.SYSTEM_INFO, JSON.toJSONString(systemInfo));
        return systemInfo;
    }
}
