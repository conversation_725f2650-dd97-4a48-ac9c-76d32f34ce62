package cn.ysatnaf.domain.address.service;

import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;

import java.util.List;

/**
 * ReceiverAreaService
 *
 * <AUTHOR>
 * @date 2023/12/22 14:02
 */
public interface ReceiverAreaService {
    List<ReceiverAreaEntity> getByZipCode(String zipCode);

    ReceiverAreaEntity getReceiverArea(String zipCode, String receiverAddress);

    ReceiverAreaEntity getById(Long receiverAreaId);

    String getReceiverEnAddress(ReceiverAreaEntity receiverAreaEntity, String receiverAddress);
}
