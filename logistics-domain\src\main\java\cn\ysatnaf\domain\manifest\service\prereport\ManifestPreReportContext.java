package cn.ysatnaf.domain.manifest.service.prereport;

import cn.hutool.core.util.IdUtil;
import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import cn.ysatnaf.domain.manifest.service.prereport.validate.ErrorMsgManager;
import cn.ysatnaf.domain.manifest.service.prereport.validate.ValidationResult;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 预报运单的上下文
 */
@Data
@Builder
@AllArgsConstructor
public class ManifestPreReportContext {

    /**
     * 批次ID
     */
    private final String batchId;

    /**
     * 用户ID
     */
    private final UserEntity user;

    /**
     * 文件存储路径
     */
    private final String fileStorePath;

    /**
     * 上传的原始数据
     */
    @Getter
    private final List<ManifestPreReportRow> originalData;

    /**
     * 上传文件的原始名称
     */
    @Getter
    private final String originalFilename;

    /**
     * 已揽件的运单号
     */
    private final Set<String> pickedUpExpressNumbers;

    /**
     * 已揽件的订单号
     */
    private final Set<String> pickedUpOrderNumbers;

    /**
     * 错误信息管理器
     */
    private final ErrorMsgManager errorMsgManager;

    /**
     * 检测结果
     */
    private final ValidationResult validationResult;

    /**
     * 已预报的运单号
     */
    private final Set<String> preReportedExpressNumbers = new HashSet<>();

    /**
     * 是否覆盖重复数据
     */
    private Boolean overwrite;

    public ManifestPreReportContext(List<ManifestPreReportRow> data, UserEntity user, String fileStorePath, String originalFilename) {
        this.batchId = IdUtil.fastSimpleUUID();
        this.originalData = data != null ? Collections.unmodifiableList(data) : Collections.emptyList();
        this.user = user;
        this.fileStorePath = fileStorePath;
        this.originalFilename = originalFilename;
        this.pickedUpExpressNumbers = new HashSet<>();
        this.pickedUpOrderNumbers = new HashSet<>();
        this.errorMsgManager = new ErrorMsgManager();
        this.validationResult = new ValidationResult();
    }

    /**
     * 记录已预报数据
     *
     * @param data 数据
     */
    public void recordPreReportedData(ManifestPreReportRow data) {
        preReportedExpressNumbers.add(data.getExpressNumber());
        validationResult.getPreReportedList().add(data);
    }

    /**
     * 记录未预报数据
     *
     * @param data 数据
     */
    public void recordNotPreReportedData(ManifestPreReportRow data) {
        validationResult.getNotPreReportedList().add(data);
    }
}
