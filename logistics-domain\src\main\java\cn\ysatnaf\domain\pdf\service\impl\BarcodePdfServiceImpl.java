package cn.ysatnaf.domain.pdf.service.impl;

import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.SawagaSiteCodeEntity;
import cn.ysatnaf.domain.manifest.service.impl.InvoiceServiceImpl;
import cn.ysatnaf.domain.pdf.service.BarcodePdfService;
import cn.ysatnaf.types.exception.ServiceException;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.properties.AreaBreakType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class BarcodePdfServiceImpl implements BarcodePdfService {

    @Override
    public void generate(List<Manifest> manifests, HttpServletResponse httpServletResponse) {
        // 设定HTTP响应的内容类型为 "application/pdf"
        httpServletResponse.setContentType("application/pdf");
        // 设置'Content-Disposition'头部信息，指定PDF文件名并设为附件形式进行下载
        String filename = "output.pdf";
        try {
            httpServletResponse.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(filename, "UTF-8") + "\"");
        } catch (UnsupportedEncodingException e) {
            log.error("生成条形码PDF失败：", e);
            throw new ServiceException("设置文件名时发生编码错误");
        }

        // Initialize PDF writer
        PdfWriter writer;
        try {
            writer = new PdfWriter(httpServletResponse.getOutputStream());
        } catch (IOException e) {
            log.error("生成条形码PDF失败：", e);
            throw new ServiceException("生成条形码PDF文件失败");
        }

        // Initialize PDF document
        PdfDocument pdf = new PdfDocument(writer);

        // Initialize document
        Document document = new Document(pdf, PageSize.A4);
        document.setFont(InvoiceServiceImpl.getFont());
        document.setMargins(0, 0, 0, 0); // remove margins if desired
        PageSize defaultPageSize = pdf.getDefaultPageSize();
        int baseHeight = 0;
        for (int i = 0; i < manifests.size(); i++) {
            Manifest manifest = manifests.get(i);
            int number = i % 10 + 1;
            float height = defaultPageSize.getHeight() - baseHeight - 70 * number;
            // 着店号条形码
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                BitMatrix bitMatrix = new MultiFormatWriter().encode(SawagaSiteCodeEntity.acquireBarcodeCode(manifest.getSawagaSiteCode()), BarcodeFormat.CODABAR, 20, 50);
                MatrixToImageWriter.writeToStream(bitMatrix, "png", outputStream);
                Image code = new Image(ImageDataFactory.create(outputStream.toByteArray())).setFixedPosition(50, height);
                document.add(code);
            } catch (Exception e) {
                log.error("生成条形码失败：", e);
                throw new ServiceException("生成文件失败");
            }

            Paragraph info = new Paragraph(String.valueOf(i + 1) + ".");
            info.setFontSize(10).setFixedPosition(30, height + 20, 100);
            document.add(info);

            info = new Paragraph(manifest.getSawagaSiteCode());
            info.setFontSize(10).setFixedPosition(70, height - 20, 100);
            document.add(info);

            // 佐川单号条形码
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                BitMatrix bitMatrix = new MultiFormatWriter().encode("d" + manifest.getTrackingNumber() + "d", BarcodeFormat.CODABAR, 20, 50);
                MatrixToImageWriter.writeToStream(bitMatrix, "png", outputStream);
                Image code = new Image(ImageDataFactory.create(outputStream.toByteArray())).setFixedPosition(350, height);
                document.add(code);
            } catch (Exception e) {
                log.error("生成条形码失败：", e);
                throw new ServiceException("生成文件失败");
            }

            info = new Paragraph("d" + manifest.getTrackingNumber() + "d");
            info.setFontSize(10).setFixedPosition(370, height - 20, 100);
            document.add(info);

            // 每20个换一行
            if ((i + 1) % 10 == 0) {
                document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
            }
        }
        try {
            document.close();
            httpServletResponse.flushBuffer();
        } catch (IOException e) {
            throw new ServiceException("生成面单文件失败");
        }
    }
}
