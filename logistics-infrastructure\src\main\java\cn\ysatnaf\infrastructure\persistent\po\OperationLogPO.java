package cn.ysatnaf.infrastructure.persistent.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR> <PERSON>
 */

@Data
@TableName("tb_operation_log")
public class OperationLogPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long operatorId;

    private Long userId;

    private Integer operationType;

    private String operation;

    private String originalInfo;

    private String newInfo;

    private Long manifestId;
}
