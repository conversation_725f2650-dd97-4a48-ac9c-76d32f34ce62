package cn.ysatnaf.domain.statistics.calculator;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class TimeRangeCalculator {

    public static LocalDateTime[] calculateTodayRange(int startHour) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayStart = now.withHour(startHour).withMinute(0).withSecond(0);
        LocalDateTime todayEnd = todayStart.plusDays(1).minusSeconds(1);

        // 处理当前时间在今日统计周期之前的情况（如上午9点查询 startHour=10）
        if (now.isBefore(todayStart)) {
            todayStart = todayStart.minusDays(1);
            todayEnd = todayEnd.minusDays(1);
        }

        return new LocalDateTime[]{todayStart, todayEnd};
    }

    public static LocalDateTime[] calculateYesterdayRange(int startHour) {
        LocalDateTime[] todayRange = calculateTodayRange(startHour);
        return new LocalDateTime[]{
                todayRange[0].minusDays(1),
                todayRange[1].minusDays(1)
        };
    }

    public static LocalDateTime[] calculateTimeRangeByMonthOffset(int startHour, int monthOffset) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime baseDate = now.plusMonths(monthOffset);

        // 计算当前周期的开始时间（上月同期）
        LocalDateTime start = baseDate
                .withDayOfMonth(1)
                .withHour(startHour)
                .withMinute(0)
                .withSecond(0);

        // 调整到下个月的同小时
        LocalDateTime end = start.plusMonths(1);

        return new LocalDateTime[]{start, end};
    }

    public static LocalDateTime[] calculateTimeRange(int startHour, LocalDateTime startTime, LocalDateTime endTime) {
        // 计算当前周期的开始时间（上月同期）
        startTime = startTime
                .withHour(startHour)
                .withMinute(0)
                .withSecond(0);

        endTime = endTime
                .withHour(startHour)
                .withMinute(0)
                .withSecond(0);

        // 调整到下个月的同小时
        return new LocalDateTime[]{startTime, endTime};
    }
}
