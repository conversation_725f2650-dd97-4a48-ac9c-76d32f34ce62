package cn.ysatnaf.domain.task.model.req;

import cn.ysatnaf.types.common.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "任务列表入参")
@Data
public class TaskListReq extends PageParam {

    @Schema(description = "任务类型（不传为全部）: INVOICE_GENERATION-导出发票")
    private String taskType;

    @Schema(description = "任务状态（不传为全部）：PENDING-等待执行；IN_PROGRESS-正在执行；COMPLETED-已完成；FAILED-执行失败")
    private String taskStatus;

    @Schema(description = "任务执行时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeStart;

    @Schema(description = "任务执行时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;
}
