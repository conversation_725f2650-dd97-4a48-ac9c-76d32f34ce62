package cn.ysatnaf.domain.statistics.calculator;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

public class DateTimeUtil {
    
    /**
     * 根据指定的startHour，获取当前月的开始时间
     */
    public static LocalDateTime getCurrentMonthStartWithHour(int startHour) {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfMonth = LocalDate.of(today.getYear(), today.getMonth(), 1);
        
        return LocalDateTime.of(firstDayOfMonth, LocalTime.of(startHour, 0, 0));
    }
    
    /**
     * 根据指定的startHour，获取当前月的结束时间
     */
    public static LocalDateTime getCurrentMonthEndWithHour(int startHour) {
        LocalDate today = LocalDate.now();
        YearMonth yearMonth = YearMonth.of(today.getYear(), today.getMonth());
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();
        
        return LocalDateTime.of(lastDayOfMonth, LocalTime.of(startHour-1, 59, 59)).plusDays(1);
    }
    
    /**
     * 根据指定的startHour，获取上个月的开始时间
     */
    public static LocalDateTime getLastMonthStartWithHour(int startHour) {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfLastMonth = LocalDate.of(today.getYear(), today.getMonth(), 1).minusMonths(1);
        
        return LocalDateTime.of(firstDayOfLastMonth, LocalTime.of(startHour, 0, 0));
    }
    
    /**
     * 根据指定的startHour，获取上个月的结束时间
     */
    public static LocalDateTime getLastMonthEndWithHour(int startHour) {
        LocalDate today = LocalDate.now();
        YearMonth lastMonth = YearMonth.of(today.getYear(), today.getMonth()).minusMonths(1);
        LocalDate lastDayOfLastMonth = lastMonth.atEndOfMonth();
        
        return LocalDateTime.of(lastDayOfLastMonth, LocalTime.of(startHour-1, 59, 59)).plusDays(1);
    }
    
    /**
     * 获取过去N个月的开始时间
     */
    public static LocalDateTime getMonthsAgoStartWithHour(int months, int startHour) {
        LocalDate today = LocalDate.now();
        LocalDate firstDayOfNMonthsAgo = LocalDate.of(today.getYear(), today.getMonth(), 1).minusMonths(months);
        
        return LocalDateTime.of(firstDayOfNMonthsAgo, LocalTime.of(startHour, 0, 0));
    }
    
    /**
     * 格式化日期为前端友好的显示格式
     */
    public static String formatDateRange(LocalDate date, int startHour) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return String.format("%s %d:00 - %s %d:59", 
                date.format(formatter), startHour, 
                date.plusDays(1).format(formatter), startHour-1);
    }

    /**
     * 获取今日开始时间（考虑自定义起始小时）
     */
    public static LocalDateTime getTodayStartWithHour(int startHour) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = now.toLocalDate();
        LocalTime currentTime = now.toLocalTime();

        // 如果当前时间小于startHour，则"今日"应该是前一天的startHour到现在
        if (currentTime.getHour() < startHour) {
            today = today.minusDays(1);
        }

        return LocalDateTime.of(today, LocalTime.of(startHour, 0, 0));
    }

    /**
     * 获取今日结束时间（考虑自定义起始小时）
     */
    public static LocalDateTime getTodayEndWithHour(int startHour) {
        LocalDateTime now = LocalDateTime.now();
        LocalDate today = now.toLocalDate();
        LocalTime currentTime = now.toLocalTime();

        // 如果当前时间小于startHour，则"今日"结束时间是当天的startHour
        if (currentTime.getHour() < startHour) {
            return LocalDateTime.of(today, LocalTime.of(startHour, 0, 0));
        } else {
            // 否则"今日"结束时间是明天的startHour
            return LocalDateTime.of(today.plusDays(1), LocalTime.of(startHour, 0, 0));
        }
    }

    /**
     * 获取昨日开始时间（考虑自定义起始小时）
     */
    public static LocalDateTime getYesterdayStartWithHour(int startHour) {
        return getTodayStartWithHour(startHour).minusDays(1);
    }

    /**
     * 获取昨日结束时间（考虑自定义起始小时）
     */
    public static LocalDateTime getYesterdayEndWithHour(int startHour) {
        return getTodayEndWithHour(startHour).minusDays(1);
    }

    /**
     * 获取指定日期的起始时间（考虑自定义起始小时）
     */
    public static LocalDateTime getStartOfDayWithHour(LocalDate date, int startHour) {
        LocalTime startTime = LocalTime.of(startHour, 0, 0);
        return LocalDateTime.of(date, startTime);
    }

    /**
     * 获取指定日期的结束时间（考虑自定义起始小时）
     */
    public static LocalDateTime getEndOfDayWithHour(LocalDate date, int startHour) {
        if (startHour == 0) {
            return LocalDateTime.of(date, LocalTime.of(23, 59, 59, 999999999));
        } else {
            return LocalDateTime.of(date.plusDays(1), LocalTime.of(startHour - 1, 59, 59, 999999999));
        }
    }
}