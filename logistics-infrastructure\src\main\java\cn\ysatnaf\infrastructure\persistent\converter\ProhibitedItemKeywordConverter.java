package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.item.model.entity.ProhibitedItemKeyword;
import cn.ysatnaf.infrastructure.persistent.po.item.ProhibitedItemKeywordPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 违禁关键词 PO 与 Domain Entity 转换器
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProhibitedItemKeywordConverter {

    ProhibitedItemKeywordConverter INSTANCE = Mappers.getMapper(ProhibitedItemKeywordConverter.class);

    ProhibitedItemKeywordPO toPO(ProhibitedItemKeyword entity);

    ProhibitedItemKeyword toEntity(ProhibitedItemKeywordPO po);

    List<ProhibitedItemKeyword> toEntityList(List<ProhibitedItemKeywordPO> poList);

} 