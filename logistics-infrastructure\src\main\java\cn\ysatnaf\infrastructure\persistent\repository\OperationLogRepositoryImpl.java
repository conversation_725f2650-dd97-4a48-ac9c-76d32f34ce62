package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.log.model.entity.OperationLogEntity;
import cn.ysatnaf.domain.log.repository.OperationLogRepository;
import cn.ysatnaf.infrastructure.persistent.converter.OperationLogConverter;
import cn.ysatnaf.infrastructure.persistent.dao.OperationLogDao;
import cn.ysatnaf.infrastructure.persistent.po.OperationLogPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class OperationLogRepositoryImpl implements OperationLogRepository {

    private final OperationLogDao operationLogDao;

    @Override
    public void insert(OperationLogEntity operationLogEntity) {
        operationLogDao.insert(OperationLogConverter.INSTANCE.toPO(operationLogEntity));
    }

    @Override
    public void insertBatch(List<OperationLogEntity> operationLogEntities) {
        operationLogDao.insertBatchSomeColumn(OperationLogConverter.INSTANCE.toPOList(operationLogEntities));
    }

    @Override
    public PageResult<OperationLogEntity> page(Long operatorId,
                                               Long manifestId,
                                               LocalDateTime createTimeStart,
                                               LocalDateTime createTimeEnd,
                                               Integer pageNo,
                                               Integer pageSize) {
        LambdaQueryWrapper<OperationLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjUtil.isNotNull(operatorId), OperationLogPO::getOperatorId, operatorId);
        queryWrapper.eq(ObjUtil.isNotNull(manifestId), OperationLogPO::getManifestId, manifestId);
        queryWrapper.gt(ObjUtil.isNotNull(createTimeStart), OperationLogPO::getCreateTime, createTimeStart);
        queryWrapper.lt(ObjUtil.isNotNull(createTimeEnd), OperationLogPO::getCreateTime, createTimeEnd);
        queryWrapper.orderByDesc(OperationLogPO::getCreateTime);
        Page<OperationLogPO> operationLogPOPage = operationLogDao.selectPage(new Page<>(pageNo, pageSize), queryWrapper);
        List<OperationLogPO> records = operationLogPOPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageResult<>(operationLogPOPage.getTotal());
        }
        return new PageResult<>(OperationLogConverter.INSTANCE.toEntityList(records), operationLogPOPage.getTotal());
    }
}
