package cn.ysatnaf.domain.excel.service.listener; // 包路径可能需要根据实际调整\n\nimport com.alibaba.excel.context.AnalysisContext;\nimport com.alibaba.excel.event.AnalysisEventListener;\nimport com.alibaba.excel.exception.ExcelDataConvertException;\nimport lombok.Getter;\nimport lombok.extern.slf4j.Slf4j;\n\nimport java.util.ArrayList;\nimport java.util.List;\nimport java.util.Map;\n\n/**\n * 用于读取单号导入Excel的监听器\n * <AUTHOR> */\n@Slf4j\n@Getter\npublic class TrackingNumberExcelListener extends AnalysisEventListener<Map<Integer, String>> {\n\n    // 存储读取到的单号 (假设单号在第一列)\n    private final List<String> trackingNumbers = new ArrayList<>();\n    private static final int TRACKING_NUMBER_COLUMN_INDEX = 0; // 单号所在列索引 (0-based)\n\n    /**\n     * 每解析一行数据会调用此方法\n     * @param data            one row value. Is is same as {@link #invoke(Object, AnalysisContext)}.\n     * @param context\n     */\n    @Override\n    public void invoke(Map<Integer, String> data, AnalysisContext context) {\n        // Excel数据是 Map<列索引, 单元格字符串值>\n        String trackingNumber = data.get(TRACKING_NUMBER_COLUMN_INDEX);\n        if (trackingNumber != null && !trackingNumber.trim().isEmpty()) {\n            trackingNumbers.add(trackingNumber.trim());\n        }\ else {\n            // 根据需要处理空行或空格，这里选择忽略\n            log.debug(\"忽略空单号或空格行，行号: {}\", context.readRowHolder().getRowIndex() + 1);\n        }\n    }\n\n    /**\n     * 所有数据解析完成后会调用此方法\n     * @param context\n     */\n    @Override\n    public void doAfterAllAnalysed(AnalysisContext context) {\n        log.info(\"Excel单号读取完成，共读取到 {} 条有效单号\", trackingNumbers.size());\n    }\n\n    /**\n     * 处理表头数据\n     * @param headMap\n     * @param context\n     */\n    @Override\n    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {\n        // 校验表头是否符合预期 (\"单号\")\n        String expectedHeader = \"单号\";\n        String actualHeader = headMap.get(TRACKING_NUMBER_COLUMN_INDEX);\n        if (actualHeader == null || !expectedHeader.equals(actualHeader.trim())) {\n            log.error(\"Excel表头格式错误，第一列应为 '{}'，实际为 '{}'\", expectedHeader, actualHeader);\n            // 可以选择抛出异常中断读取\n            throw new RuntimeException(\"导入失败：Excel表头格式错误，第一列应为 '单号'\");\n        }\n         log.info(\"Excel表头读取成功: {}\", headMap);\n    }\n    \n    /**\n     * 发生异常时调用\n     * @param exception\n     * @param context\n     * @throws Exception\n     */\n    @Override\n    public void onException(Exception exception, AnalysisContext context) throws Exception {\n        log.error(\"解析Excel行号 {} 时发生异常:\", context.readRowHolder().getRowIndex() + 1, exception);\n        // 如果是数据转换异常，可以提供更友好的提示\n        if (exception instanceof ExcelDataConvertException) {\n            ExcelDataConvertException dataConvertException = (ExcelDataConvertException) exception;\n            log.error(\"数据转换错误: 第{}行，第{}列，数据 '{}'\", \n                      dataConvertException.getRowIndex() + 1, \n                      dataConvertException.getColumnIndex() + 1,\n                      dataConvertException.getCellData());\n        }\n        // 选择是否继续抛出异常\n         super.onException(exception, context);\n    }\n}\n 