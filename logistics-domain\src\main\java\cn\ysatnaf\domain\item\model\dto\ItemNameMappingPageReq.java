package cn.ysatnaf.domain.item.model.dto;

import cn.ysatnaf.types.common.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "物品名称映射分页查询请求")
public class ItemNameMappingPageReq extends PageParam {

    @Schema(description = "原始名称 (模糊查询)", example = "电池")
    private String originalName;

    @Schema(description = "映射后名称 (模糊查询)", example = "玩具")
    private String mappedName;

    @Schema(description = "是否启用 (精确查询)", example = "true")
    private Boolean isActive;

} 