package cn.ysatnaf.domain.manifest.service.prereport.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.domain.log.service.ManifestLogHelper;
import cn.ysatnaf.domain.manifest.model.aggregate.ManifestAggregate;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import cn.ysatnaf.domain.manifest.model.req.ManifestConfirmPreReportReq;
import cn.ysatnaf.domain.manifest.model.res.ManifestConfirmPreReportRes;
import cn.ysatnaf.domain.manifest.model.res.ManifestPreReportPreviewRes;
import cn.ysatnaf.domain.manifest.model.res.ManifestPreReportUploadRes;
import cn.ysatnaf.domain.manifest.model.valobj.PreReportPreviewType;
import cn.ysatnaf.domain.manifest.repository.ManifestRepository;
import cn.ysatnaf.domain.manifest.service.ManifestItemService;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportService;
import cn.ysatnaf.domain.manifest.service.prereport.cache.ManifestPreReportCache;
import cn.ysatnaf.domain.manifest.service.prereport.handledata.ManifestPreReportDataConverter;
import cn.ysatnaf.domain.manifest.service.prereport.handledata.PreReportDataTransferFactory;
import cn.ysatnaf.domain.manifest.service.prereport.reader.ExcelFileReader;
import cn.ysatnaf.domain.manifest.service.prereport.storage.FileStorage;
import cn.ysatnaf.domain.manifest.service.prereport.validate.ManifestPreReportDataValidateHandler;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportContext;
import cn.ysatnaf.domain.manifest.service.prereport.validate.ErrorMsgManager;
import cn.ysatnaf.domain.manifest.service.prereport.validate.ValidationResult;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ManifestPreReportServiceImpl implements ManifestPreReportService {

    private final FileStorage fileStorage;

    private final ExcelFileReader<ManifestPreReportRow> excelFileReader;

    private final ManifestPreReportDataValidateHandler manifestPreReportDataValidateHandler;

    private final ManifestPreReportCache manifestPreReportCache;

    private final ManifestRepository manifestRepository;

    private final ManifestItemService manifestItemService;

    private final UserService userService;

    private final PreReportDataTransferFactory preReportDataTransferFactory;

    private final ManifestLogHelper manifestLogHelper;

    @Override
    public ManifestPreReportUploadRes preReportUpload(MultipartFile file, Long userId) {
        // 处理文件
        ManifestPreReportContext manifestPreReportContext = processFile(file, userId);

        // 如果解析有错误，直接返回错误
        if (manifestPreReportContext.getErrorMsgManager().hasError()) {
            return ManifestPreReportUploadRes.builder()
                    .errors(manifestPreReportContext.getErrorMsgManager().getErrorMessages())
                    .build();
        }

        // 如果没有错误，那么缓存数据
        manifestPreReportCache.set(manifestPreReportContext);
        return buildPreReportUploadRes(manifestPreReportContext);
    }

    @Override
    public ManifestPreReportPreviewRes preReportPreview(String batchId,
                                                        Integer previewType,
                                                        Integer pageNumber,
                                                        Integer pageSize) {
        ManifestPreReportContext manifestPreReportContext = getPreReportContextFromCache(batchId);
        if (manifestPreReportContext == null) {
            throw new ServiceException("预报操作超时，请重新上传文件");
        }

        // 缓存续期
        manifestPreReportCache.set(manifestPreReportContext);

        // 根据查询条件筛选构建返回数据
        return buildPreReportPreviewRes(previewType, pageNumber, pageSize, manifestPreReportContext);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ManifestConfirmPreReportRes confirmPreReport(ManifestConfirmPreReportReq req) {
        // 假设获取方法如下，需要您根据实际情况替换
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        Long operatorId = loginUser.getId();
        String operatorName = loginUser.getNickname();
        String context = "后台管理-批量预报确认";
        String ipAddress = null; // TODO: 获取真实 IP 地址

        // 从缓存中读取预报数据
        ManifestPreReportContext manifestPreReportContext = manifestPreReportCache.get(req.getBatchId());
        if (manifestPreReportContext == null) {
            throw new ServiceException("预报操作超时，请重新上传");
        }

        // 在上下文中设置是否需要覆盖已预报数据
        manifestPreReportContext.setOverwrite(req.getOverwrite());

        // 获取批次信息用于日志
        String fileName = manifestPreReportContext.getOriginalFilename();
        int attemptedCount = manifestPreReportContext.getOriginalData().size();

        // --- 生成失败原因摘要 --- 
        String failureReasonSummary = null;
        ErrorMsgManager errorMsgManager = manifestPreReportContext.getErrorMsgManager();
        if (errorMsgManager != null && errorMsgManager.hasError()) {
            List<String> errors = errorMsgManager.getErrorMessages();
            // 取前 3 条不同的错误信息作为摘要示例
            failureReasonSummary = errors.stream()
                                         .distinct() // 去重
                                         .limit(3)   // 限制数量
                                         .collect(Collectors.joining("; ")); // 用分号+空格连接
            // 可以根据需要调整摘要逻辑，例如截断长消息或统计错误类型
            int totalErrors = errors.size();
            if (totalErrors > 3) {
                 failureReasonSummary += String.format("; ... (共 %d 条错误)", totalErrors);
            }
        }
        // --- 摘要生成结束 ---

        // 创建预报数据转换器
        ManifestPreReportDataConverter converter = preReportDataTransferFactory.create(manifestPreReportContext);

        // 开始处理数据
        PreReportHandlingResult handlingResult = handlePreReportData(manifestPreReportContext, converter);
        int successCount = handlingResult.getSuccessCount();
        // 使用 errorMsgManager 中的错误数（如果它准确反映了失败数）或者简单计算
        int failedCount = attemptedCount - successCount;
        // 如果 ErrorMsgManager 记录了所有失败（包括验证和保存失败），可以考虑用 errorMsgManager.getErrorCount() 作为 failedCount

        // 记录批次操作日志 (传递摘要)
        manifestLogHelper.recordBatchPreReportLog(req.getBatchId(), operatorId, operatorName, ipAddress, context, fileName, attemptedCount, successCount, failedCount, failureReasonSummary);

        // --- 添加新的批量确认日志记录 --- START
        if (CollUtil.isNotEmpty(handlingResult.getCreatedManifestAggregates())) {
            manifestLogHelper.recordBatchConfirmPreReportLog(
                handlingResult.getCreatedManifestAggregates(),
                operatorId,
                operatorName,
                ipAddress,
                context // 使用上面定义的 context
            );
        }
        // --- 添加新的批量确认日志记录 --- END

        // 清除缓存
        manifestPreReportCache.remove(req.getBatchId());

        return ManifestConfirmPreReportRes.builder().count(successCount).build();
    }

    // --- 处理数据方法 (需要修改返回值) ---

    /**
     * 处理预报数据 (返回 PreReportHandlingResult)
     *
     * @param context   上下文
     * @param converter 转换器
     * @return 处理结果，包含成功数量和创建的 Manifest 聚合列表
     */
    public PreReportHandlingResult handlePreReportData(ManifestPreReportContext context, ManifestPreReportDataConverter converter) {
        ValidationResult validationResult = context.getValidationResult();
        int saveCount = 0;

        // 转换未预报数据
        List<ManifestAggregate> notPreReportedData = converter.convert(validationResult.getNotPreReportedList());
        // 保存未预报数据
        saveNotPreReportedData(notPreReportedData); // 确保此方法会填充ID到 notPreReportedData 列表的对象中
        List<ManifestAggregate> createdAggregates = new ArrayList<>(notPreReportedData);
        saveCount += notPreReportedData.size();

        if (context.getOverwrite()) {
            // 如果需要覆盖已存在的数据，那么也转换已预报的数据
            List<ManifestAggregate> preReportedData = converter.convert(validationResult.getPreReportedList());
            savePreReportedData(preReportedData, context.getPreReportedExpressNumbers()); // 确保此方法会填充ID
            createdAggregates.addAll(preReportedData); // 注意：覆盖操作也加入列表，日志记录时会区分
            saveCount += preReportedData.size();
        }

        // 此处不再需要重新查询填充ID，因为已假设 save 方法内部填充

        return new PreReportHandlingResult(saveCount, createdAggregates);
    }

    /**
     * 保存已预报数据
     *
     * @param preReportedData           已预报的数据 (预期 Manifest 对象包含 ID)
     * @param preReportedExpressNumbers 已预报的快递单号列表
     */
    private void savePreReportedData(List<ManifestAggregate> preReportedData, Set<String> preReportedExpressNumbers) {
        if (CollUtil.isEmpty(preReportedData)) {
            return;
        }
        List<Manifest> updateManifests = new ArrayList<>();
        List<ManifestItem> insertManifestItems = new ArrayList<>();
        List<Long> deleteItemManifestIds = new ArrayList<>();

        // 查询在库的订单以确认 ID (如果 preReportedData 尚未包含 ID)
        // 如果 preReportedData 已包含 ID，此步骤可省略或用于验证
        List<Manifest> inDbManifests = manifestRepository.getByExpressNumbers(preReportedExpressNumbers);
        Map<String, Long> expressNumber2IdMap = inDbManifests
                .stream()
                .collect(Collectors.toMap(Manifest::getExpressNumber, Manifest::getId));

        preReportedData.forEach(manifestAggregate -> {
            Manifest manifest = manifestAggregate.getManifest();
            // 获取ID，设置id (如果尚未设置)
            if (manifest.getId() == null) {
                manifest.setId(expressNumber2IdMap.get(manifest.getExpressNumber()));
            }
            Long manifestId = manifest.getId();
            if (manifestId == null) { // Double check
                 log.error("无法确定要更新的 Manifest ID: expressNumber={}", manifest.getExpressNumber());
                 return; // 跳过此条记录
            }

            // 记录要更新的Manifest
            updateManifests.add(manifest);
            // 记录要删除的ManifestItem的manifestId
            deleteItemManifestIds.add(manifestId);
            // 设置manifestId
            List<ManifestItem> manifestItems = manifestAggregate.getManifestItems();
            manifestItems.forEach(manifestItem -> manifestItem.setManifestId(manifestId));
            insertManifestItems.addAll(manifestItems);
        });
        // 批量更新
        if (!updateManifests.isEmpty()) manifestRepository.updateBatchByIds(updateManifests);
        // 批量删除manifestItem
        if (!deleteItemManifestIds.isEmpty()) manifestItemService.deleteByManifestIds(deleteItemManifestIds);
        // 插入manifestItems
        if (!insertManifestItems.isEmpty()) manifestItemService.insertBatch(insertManifestItems);
    }

    /**
     * 插入未预报数据 (确保填充 Manifest ID 到传入的列表中)
     *
     * @param notPreReportedData 未预报数据 (此列表中的对象会被修改以包含 ID)
     */
    private void saveNotPreReportedData(List<ManifestAggregate> notPreReportedData) {
        if (CollUtil.isEmpty(notPreReportedData)) {
            return;
        }
        List<Manifest> insertManifests = new ArrayList<>();
        List<String> insertExpressNumbers = new ArrayList<>();
        List<ManifestItem> toInsertManifestItems = new ArrayList<>();
        notPreReportedData.forEach(manifestAggregate -> {
            Manifest manifest = manifestAggregate.getManifest();
            String expressNumber = manifest.getExpressNumber();

            List<ManifestItem> manifestItems = manifestAggregate.getManifestItems();
            manifestItems.forEach(item -> item.setExpressNumber(expressNumber)); // 确保 item 有 expressNumber
            toInsertManifestItems.addAll(manifestItems);

            insertExpressNumbers.add(expressNumber);
            insertManifests.add(manifest);
        });

        // 插入 Manifest 数据库
        if (!insertManifests.isEmpty()) manifestRepository.insertBatch(insertManifests);

        // 查询出来获取到ID
        List<Manifest> manifestsWithId = manifestRepository.getByExpressNumbers(insertExpressNumbers);
        Map<String, Long> expressNumber2IdMap = manifestsWithId
                .stream()
                .collect(Collectors.toMap(Manifest::getExpressNumber, Manifest::getId, (id1, id2) -> id1)); // 处理可能的重复 expressNumber

        // 填充原始列表中的 ID 和 items 的 manifestId
        notPreReportedData.forEach(agg -> {
            Long id = expressNumber2IdMap.get(agg.getManifest().getExpressNumber());
            if (id != null) {
                 agg.getManifest().setId(id);
                 agg.getManifestItems().forEach(item -> item.setManifestId(id));
            } else {
                 log.error("插入后未能找到 Manifest ID: expressNumber={}", agg.getManifest().getExpressNumber());
                 // 可能需要从 toInsertManifestItems 中移除与此相关的项
            }
        });

        // 过滤掉没有成功获取到 Manifest ID 的 Item
        List<ManifestItem> finalItemsToInsert = toInsertManifestItems.stream()
            .filter(item -> item.getManifestId() != null)
            .collect(Collectors.toList());

        // 插入物品列表
        if (!finalItemsToInsert.isEmpty()) manifestItemService.insertBatch(finalItemsToInsert);
    }

    /**
     * 处理文件，包括读取文件和校验文件内容
     *
     * @param file   上传的文件
     * @param userId 用户ID
     * @return 预报上下文
     */
    private ManifestPreReportContext processFile(MultipartFile file, Long userId) {
        // 读取文件并创建预报上下文
        ManifestPreReportContext context = createPreReportContextByFile(file, userId);
        // 校验文件内容
        manifestPreReportDataValidateHandler.handle(context);
        return context;
    }

    /**
     * 通过文件创建预报上下文
     * <p>
     * 如果有错误，直接返回
     *
     * @param file   上传的文件
     * @param userId 用户ID
     * @return 分析结果
     */
    private ManifestPreReportContext createPreReportContextByFile(MultipartFile file, Long userId) {
        // 存储文件
        String storePath = fileStorage.store(file);
//        String storePath = "";

        // 读取文件内容
        List<ManifestPreReportRow> excelRows = excelFileReader.read(file);

        // 返回上下文对象
        return prepareContext(excelRows, userId, storePath, file.getOriginalFilename());
    }

    /**
     * 从缓存中获取预报上下文
     *
     * @param batchId 批次号
     * @return PreReportManifestContext 预报上下文
     */
    private ManifestPreReportContext getPreReportContextFromCache(String batchId) {
        return manifestPreReportCache.get(batchId);
    }

    /**
     * 构建分析上传清单的响应对象
     * 根据上传预览类型和分页信息，从预报告清单上下文中提取并组装分析结果
     *
     * @param context 预报告清单上下文，包含上传数据的验证结果
     * @return 返回一个组装好的分析上传清单的响应对象
     */
    private ManifestPreReportUploadRes buildPreReportUploadRes(ManifestPreReportContext context) {
        // 构建分析结果，根据条件筛选返回数据
        ManifestPreReportUploadRes uploadRes = new ManifestPreReportUploadRes();
        // 设置批次号
        uploadRes.setBatchId(context.getBatchId());

        // 设置已预报数量
        int preReportedCount = context.getValidationResult().getPreReportedList().size();
        uploadRes.setPreReportedCount(preReportedCount);

        // 设置未预报数量
        int notPreReportedCount = context.getValidationResult().getNotPreReportedList().size();
        uploadRes.setNotPreReportedCount(notPreReportedCount);

        // 设置总上传数量
        int uploadTotal = notPreReportedCount + preReportedCount;
        uploadRes.setUploadCount(uploadTotal);

        // 返回解析结果
        return uploadRes;
    }

    /**
     * 构建分析上传清单的响应对象
     * 根据上传预览类型和分页信息，从预报告清单上下文中提取并组装分析结果
     *
     * @param previewType 上传预览类型，决定返回哪些数据（已预报或未预报的数据）
     * @param pageNumber  分页参数，指定当前页码
     * @param pageSize    分页参数，指定每页数据量
     * @param context     预报告清单上下文，包含上传数据的验证结果
     * @return 返回一个组装好的分析上传清单的响应对象
     */
    private ManifestPreReportPreviewRes buildPreReportPreviewRes(Integer previewType,
                                                                 Integer pageNumber,
                                                                 Integer pageSize,
                                                                 ManifestPreReportContext context) {
        // 构建分析结果，根据条件筛选返回数据
        ManifestPreReportPreviewRes manifestPreReportPreviewRes = new ManifestPreReportPreviewRes();
        // 设置批次号
        manifestPreReportPreviewRes.setBatchId(context.getBatchId());
        // 过滤结果
        List<ManifestPreReportRow> list;
        int total;
        if (PreReportPreviewType.NOT_PRE_REPORTED.getCode().equals(previewType)) {
            list = context.getValidationResult().getNotPreReportedList();
        } else {
            list = context.getValidationResult().getPreReportedList();
        }
        // 根据分页参数截取数据
        total = list.size();
        list = list.stream().skip((long) (pageNumber - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        PageResult<ManifestPreReportRow> pageResult = new PageResult<>(list, (long) total);
        manifestPreReportPreviewRes.setPageResult(pageResult);

        // 设置已预报数量
        int preReportedCount = context.getValidationResult().getPreReportedList().size();
        manifestPreReportPreviewRes.setPreReportedCount(preReportedCount);

        // 设置未预报数量
        int notPreReportedCount = context.getValidationResult().getNotPreReportedList().size();
        manifestPreReportPreviewRes.setNotPreReportedCount(notPreReportedCount);

        // 设置总上传数量
        int uploadTotal = notPreReportedCount + preReportedCount;
        manifestPreReportPreviewRes.setUploadCount(uploadTotal);

        // 返回解析结果
        return manifestPreReportPreviewRes;
    }

    /**
     * 准备上下文容器
     * 1. 准备已揽件的运单号集合
     * 2. 准备已揽件的订单号集合
     * 3. 记录非已揽件的已预报数据，用于后续提示用户是否需要覆盖
     *
     * @return 上下文对象
     */
    public ManifestPreReportContext prepareContext(List<ManifestPreReportRow> data, Long userId, String storePath, String originalFilename) {
        if (CollUtil.isEmpty(data)) {
            throw new ServiceException("未检测到文件数据，请检查文件使用的模板是否正确");
        }
        List<String> allExpressNumbers = new ArrayList<>();
        Map<String, ManifestPreReportRow> expressNumber2RowMap = new HashMap<>();
        data.forEach(row -> {
            if (StrUtil.isBlank(row.getExpressNumber())) {
                return;
            }
            allExpressNumbers.add(row.getExpressNumber());
            expressNumber2RowMap.put(row.getExpressNumber(), row);
        });

        List<Manifest> existsManifests = manifestRepository.getByExpressNumbers(allExpressNumbers);
        UserEntity user = userService.getById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在，请联系管理员");
        }

        ManifestPreReportContext context = new ManifestPreReportContext(data, user, storePath, originalFilename);

        if (CollUtil.isNotEmpty(existsManifests)) {
            existsManifests.forEach(manifest -> {
                if (manifest.ifPickedUp()) {
                    context.getPickedUpExpressNumbers().add(manifest.getExpressNumber());
                    context.getPickedUpOrderNumbers().add(manifest.getOrderNumber());
                } else {
                    context.recordPreReportedData(expressNumber2RowMap.get(manifest.getExpressNumber()));
                }
            });
        }
        return context;
    }

    // --- 内部辅助类，用于 handlePreReportData 的返回值 ---
    @lombok.Getter
    @lombok.Setter
    @lombok.AllArgsConstructor
    private static class PreReportHandlingResult {
        private int successCount;
        private List<ManifestAggregate> createdManifestAggregates;
    }
}
