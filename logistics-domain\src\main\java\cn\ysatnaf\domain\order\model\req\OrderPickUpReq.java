package cn.ysatnaf.domain.order.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * OrderReWeightingReq
 *
 * <AUTHOR>
 * @date 2024/2/7 15:25
 */
@Schema(description = "取件 入参")
@Data
public class OrderPickUpReq {

    @Schema(description = "订单ID")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "订单重量")
    @NotNull(message = "订单重量不能为空")
    private BigDecimal weight;

    @Schema(description = "长度")
    private BigDecimal length;

    @Schema(description = "宽度")
    private BigDecimal width;

    @Schema(description = "高度")
    private BigDecimal height;

    @Schema(description = "体积重量")
    private BigDecimal dimensionalWeight;

    @Schema(description = "基本费用")
    @NotNull(message = "基本费用不能为空")
    private BigDecimal basicFee;
}
