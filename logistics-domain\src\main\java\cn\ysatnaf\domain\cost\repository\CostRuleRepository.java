package cn.ysatnaf.domain.cost.repository;

import cn.ysatnaf.domain.cost.model.entity.CostRuleEntity;
import cn.ysatnaf.types.common.PageResult;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
public interface CostRuleRepository {
    void insert(CostRuleEntity costRuleEntity);

    void updateById(CostRuleEntity costRuleEntity);

    PageResult<CostRuleEntity> page(Integer pageNo, Integer pageSize);

    CostRuleEntity getById(Long id);

    void deleteById(Long id);

    void deleteByPlatformType(Integer platformType);

    void insertBatch(List<CostRuleEntity> costRuleEntities);

    List<CostRuleEntity> list();

    List<CostRuleEntity> listByPlatformType(Integer platformType);
}
