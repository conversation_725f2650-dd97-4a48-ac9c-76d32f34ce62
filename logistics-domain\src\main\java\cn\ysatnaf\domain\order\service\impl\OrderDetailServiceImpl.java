package cn.ysatnaf.domain.order.service.impl;

import cn.ysatnaf.domain.order.model.entity.OrderDetail;
import cn.ysatnaf.domain.order.repository.OrderDetailRepository;
import cn.ysatnaf.domain.order.service.OrderDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * OrderDetailServiceImpl
 *
 * <AUTHOR> Hang
 * @date 2024/2/7 14:23
 */
@Service
@RequiredArgsConstructor
public class OrderDetailServiceImpl implements OrderDetailService {

    private final OrderDetailRepository orderDetailRepository;

    @Override
    public void insert(OrderDetail orderDetail) {
        orderDetailRepository.insert(orderDetail);
    }

    @Override
    public OrderDetail getByOrderId(Long orderId) {
        return orderDetailRepository.getByOrderId(orderId);
    }

    @Override
    public void updateById(OrderDetail orderDetail) {
        orderDetailRepository.updateById(orderDetail);
    }
}
