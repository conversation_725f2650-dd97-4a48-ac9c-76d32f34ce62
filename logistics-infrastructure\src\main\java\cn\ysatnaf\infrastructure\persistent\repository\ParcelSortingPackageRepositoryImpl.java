package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingPackagePO;
import cn.ysatnaf.domain.parcelsorting.repository.ParcelSortingPackageRepository;
import cn.ysatnaf.infrastructure.persistent.dao.BoxingDetailDao;
import cn.ysatnaf.infrastructure.persistent.dao.ParcelSortingDetailDao;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingBoxPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hang
 */
@RequiredArgsConstructor
@Repository
public class ParcelSortingPackageRepositoryImpl implements ParcelSortingPackageRepository {

    private final BoxingDetailDao boxingDetailDao;
    private final ParcelSortingDetailDao parcelSortingDetailDao;

    @Override
    public ParcelSortingPackagePO getByExpressNumber(String expressNumber) {
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingPackagePO::getExpressNumber, expressNumber);
        return boxingDetailDao.selectOne(wrapper);
    }

    @Override
    public ParcelSortingPackagePO getByOrderNo(String orderNo) {
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingPackagePO::getOrderNo, orderNo);
        return boxingDetailDao.selectOne(wrapper);
    }

    @Override
    public void create(ParcelSortingPackagePO parcelSortingPackagePO) {
        boxingDetailDao.insert(parcelSortingPackagePO);
    }

    @Override
    public ParcelSortingPackagePO getByBoxIdAndExpressNumber(Long boxId, String expressNumber) {
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingPackagePO::getBoxId, boxId);
        wrapper.eq(ParcelSortingPackagePO::getExpressNumber, expressNumber);
        return boxingDetailDao.selectOne(wrapper);
    }

    @Override
    public ParcelSortingPackagePO getByBoxIdAndOrderNo(Long boxId, String orderNo) {
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingPackagePO::getBoxId, boxId);
        wrapper.eq(ParcelSortingPackagePO::getOrderNo, orderNo);
        return boxingDetailDao.selectOne(wrapper);
    }

    @Override
    public void deleteById(Long id) {
        boxingDetailDao.deleteById(id);
    }

    @Override
    public void updateByIds(List<Long> ids, Long boxId) {
        LambdaUpdateWrapper<ParcelSortingPackagePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ParcelSortingPackagePO::getId, ids);
        ParcelSortingPackagePO parcelSortingPackagePO = new ParcelSortingPackagePO();
        parcelSortingPackagePO.setBoxId(boxId);
        boxingDetailDao.update(parcelSortingPackagePO, wrapper);
    }

    @Override
    public List<ParcelSortingPackagePO> listByBoxId(Long boxId) {
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(boxId != null, ParcelSortingPackagePO::getBoxId, boxId);
        wrapper.orderByDesc(ParcelSortingPackagePO::getCreateTime);
        return boxingDetailDao.selectList(wrapper);
    }

    @Override
    public void deleteByBoxId(Long boxId) {
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingPackagePO::getBoxId, boxId);
        boxingDetailDao.delete(wrapper);
    }

    @Override
    public List<ParcelSortingPackagePO> listByBoxIds(List<Long> boxIds) {
        if (boxIds == null || boxIds.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ParcelSortingPackagePO::getBoxId, boxIds);
        return boxingDetailDao.selectList(wrapper);
    }

    @Override
    public List<ParcelSortingPackagePO> listByExpressNumbers(Collection<String> expressNumbers) {
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ParcelSortingPackagePO::getExpressNumber, expressNumbers);
        return boxingDetailDao.selectList(wrapper);
    }

    @Override
    public ParcelSortingPackagePO getByManifestId(Long id) {
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingPackagePO::getManifestId, id);
        return boxingDetailDao.selectOne(wrapper);
    }

    @Override
    public List<ParcelSortingPackagePO> listByManifestIds(List<Long> manifestIds) {
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ParcelSortingPackagePO::getManifestId, manifestIds);
        return boxingDetailDao.selectList(wrapper);
    }

    @Override
    public int batchCreate(List<ParcelSortingPackagePO> packages) {
        if (packages == null || packages.isEmpty()) {
            return 0;
        }
        return boxingDetailDao.insertBatchSomeColumn(packages);
    }

    @Override
    public Integer countByBoxIds(List<Long> boxIds) {
        if (boxIds == null || boxIds.isEmpty()) {
            return 0;
        }
        LambdaQueryWrapper<ParcelSortingPackagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ParcelSortingPackagePO::getBoxId, boxIds);
        // 使用 count 方法计数
        return Math.toIntExact(boxingDetailDao.selectCount(wrapper));
    }

    @Override
    public ParcelSortingPackagePO getById(Long packageId) {
        return boxingDetailDao.selectById(packageId);
    }

    @Override
    public List<ParcelSortingPackagePO> listByRecordId(Long recordId) {
        LambdaQueryWrapper<ParcelSortingBoxPO> boxWrapper = new LambdaQueryWrapper<>();
        boxWrapper.eq(ParcelSortingBoxPO::getRecordId, recordId);
        boxWrapper.select(ParcelSortingBoxPO::getId);
        List<ParcelSortingBoxPO> boxes = parcelSortingDetailDao.selectList(boxWrapper);

        if (boxes == null || boxes.isEmpty()) {
            return Collections.emptyList();
        }

        List<Long> boxIds = boxes.stream().map(ParcelSortingBoxPO::getId).collect(Collectors.toList());

        return listByBoxIds(boxIds);
    }
}
