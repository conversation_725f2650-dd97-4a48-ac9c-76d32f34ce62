package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.log.model.entity.ManifestOperationLog;
import cn.ysatnaf.domain.log.repository.ManifestOperationLogRepository;
import cn.ysatnaf.infrastructure.persistent.converter.ManifestOperationLogConverter;
import cn.ysatnaf.infrastructure.persistent.dao.ManifestOperationLogDao;
import cn.ysatnaf.infrastructure.persistent.po.ManifestOperationLogPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 运单操作日志仓库实现
 */
@Repository
@RequiredArgsConstructor
public class ManifestOperationLogRepositoryImpl implements ManifestOperationLogRepository {

    private final ManifestOperationLogDao manifestOperationLogDao;
    private final ManifestOperationLogConverter converter = ManifestOperationLogConverter.INSTANCE;

    @Override
    public void save(ManifestOperationLog log) {
        if (log == null) {
            // 可以选择抛出异常或记录日志
            System.err.println("Attempted to save a null ManifestOperationLog");
            return;
        }
        ManifestOperationLogPO logPO = converter.toPO(log);
        manifestOperationLogDao.insert(logPO);
        log.setId(logPO.getId()); // 回填 ID
    }

    @Override
    public void saveBatch(List<ManifestOperationLog> logEntities) {
        if (logEntities == null || logEntities.isEmpty()) {
            return;
        }
        List<ManifestOperationLogPO> poList = converter.toPOList(logEntities);
        manifestOperationLogDao.insertBatchSomeColumn(poList);
    }

    @Override
    public PageResult<ManifestOperationLog> listLogsByManifestId(Long manifestId, Integer pageNo, Integer pageSize) {
        // 1. 创建 MyBatis-Plus 分页对象
        Page<ManifestOperationLogPO> page = new Page<>(pageNo, pageSize);

        // 2. 创建查询条件
        LambdaQueryWrapper<ManifestOperationLogPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ManifestOperationLogPO::getManifestId, manifestId);
        queryWrapper.orderByDesc(ManifestOperationLogPO::getOperationTime); // 按操作时间降序排列

        // 3. 执行分页查询
        Page<ManifestOperationLogPO> resultPage = manifestOperationLogDao.selectPage(page, queryWrapper);

        // 4. 检查查询结果
        if (resultPage == null || resultPage.getRecords() == null || resultPage.getRecords().isEmpty()) {
            return PageResult.empty(resultPage != null ? resultPage.getTotal() : 0L);
        }

        // 5. 将 PO 列表转换为领域对象列表
        List<ManifestOperationLog> domainList = converter.toEntityList(resultPage.getRecords());

        // 6. 构建并返回领域对象的分页结果
        return new PageResult<>(domainList, resultPage.getTotal());
    }
} 