package cn.ysatnaf.domain.manifest.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 目的地常量类
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum Destination {

    TOKYO(1, "东京"),
    OSAKA(2, "大阪"),
    FUKUOKA(3, "福岡"),
    NAGOYA(4, "名古屋"),
    KOBE(5, "神戸"),
    SAPPORO(6, "札幌"),
    HOKKAIDO(7, "北海道"),
    OKINAWA(8, "沖縄"),
    KANAGAWA(9, "神奈川"),
    NIIGATA(10, "新潟"),
    ;
    private final Integer code;
    private final String name;
}
