package cn.ysatnaf.infrastructure.persistent.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> Hang
 */
@Data
@TableName("tb_tracking")
public class TrackingPO extends BasePO {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long manifestId;

    private Integer status;

    private String track;

    private String place;

    private Date time;

    private Long operatorId;

}
