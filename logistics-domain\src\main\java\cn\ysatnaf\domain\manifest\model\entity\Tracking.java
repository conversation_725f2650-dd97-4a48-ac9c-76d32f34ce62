package cn.ysatnaf.domain.manifest.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> Hang
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Tracking {

    private Long id;

    /**
     * 物流单号
     */
    private String trackingNumber;

    @Schema(description = "关联运单ID")
    private Long manifestId;

    @Schema(description = "轨迹状态")
    private Integer status;

    @Schema(description = "轨迹内容")
    private String track;

    @Schema(description = "发生地")
    private String place;

    @Schema(description = "发生事件")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    @Schema(description = "操作者ID")
    private Long operatorId;

    @Schema(description = "操作者昵称")
    private String operatorName;

    @Schema(description = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
