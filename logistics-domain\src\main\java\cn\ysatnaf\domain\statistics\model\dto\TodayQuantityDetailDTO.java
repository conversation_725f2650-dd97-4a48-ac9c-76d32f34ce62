package cn.ysatnaf.domain.statistics.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class TodayQuantityDetailDTO {
    // 总数量
    private Integer totalQuantity;

    // 与昨日相比变化百分比
    private BigDecimal compareYesterday;


    /**
     * 昨日运单数量
     */
    private Integer yesterdayQuantity;
    /**
     * 昨日与前天相比变化百分比
     */
    private BigDecimal yesterdayCompare;

    // 时段分布数据
    private List<HourDistributionDTO> hourDistribution;

    // 今日模板类型分布
    private List<TemplateDistributionDTO> templateDistribution;

    // 今日与昨日对比
    private ComparisonDTO comparison;

    @Data
    public static class HourDistributionDTO {
        private String hour;    // 格式: "10:00"
        private Integer count;  // 该小时运单数
    }

    @Data
    public static class TemplateDistributionDTO {
        private String name;    // 模板类型
        private Integer value;  // 数量
        private BigDecimal percentage; // 百分比
    }

    @Data
    public static class ComparisonDTO {
        private List<String> types;          // 模板类型
        private List<Integer> todayCounts;   // 今日各类型数量
        private List<Integer> yesterdayCounts; // 昨日各类型数量
    }
}