package cn.ysatnaf.domain.excel.serviice.generator;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomsDocumentType {
    TOKYO(1), OSAKA(2),
    ;

    private final Integer code;

    public static CustomsDocumentType getByValue(Integer destination) {
        for (CustomsDocumentType value : CustomsDocumentType.values()) {
            if (value.getCode().equals(destination)) {
                return value;
            }
        }
        throw new RuntimeException("找不到对应的类型");
    }
}
