package cn.ysatnaf.infrastructure.persistent.dao.trackingnumber;

import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberChannelPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 运单号渠道数据访问接口
 * <AUTHOR>
 */
@Mapper
public interface TrackingNumberChannelDao extends BaseMapper<TrackingNumberChannelPO> {

    // BaseMapper 提供了基本的 CRUD 方法
    // 如果需要自定义 SQL 查询，可以在这里添加方法并在 Mapper XML 中实现

} 