package cn.ysatnaf.domain.manifest.model.req;

import cn.ysatnaf.domain.manifest.model.valobj.StatisticsTimeType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "运单统计入参")
@Data
public class ManifestStatisticsReq {

    @Schema(description = "要查看的用户ID，管理员如果不传，则为查看全部，普通用户不传，默认都是自己的发货量")
    private Long userId;

    @Schema(description = "时间统计类型, 年：YEAR 月：MONTH 日：DAY")
    private StatisticsTimeType timeType;

    @Schema(description = "统计时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date time;
}
