package cn.ysatnaf.domain.manifest.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Data
public class InvoiceGenerationTaskSubmitReq {
    @Schema(description = "所选ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> ids;

    @Schema(description = "揽件时间范围开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickUpTimeFrom;

    @Schema(description = "揽件时间范围结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickUpTimeTo;

    @Schema(description = "运费模板类型: 1-普通模板;2-带电模板;3-投函模板")
    private List<Integer> shippingFeeTemplateTypes;
}
