package cn.ysatnaf.domain.manifest.service.prereport.cache;

import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 预报上传缓存
 */
@RequiredArgsConstructor
@Component
public class ManifestPreReportCache {

    public static final String PRE_REPORT_UPLOAD_KEY_PREFIX = "pre_report_upload_";

    private final StringRedisTemplate redisTemplate;

    /**
     * 保存预报上下文
     * 缓存12个小时
     * @param context 预报上下文
     */
    public void set(ManifestPreReportContext context) {
        redisTemplate.opsForValue().set(
                PRE_REPORT_UPLOAD_KEY_PREFIX + context.getBatchId(),
                JSON.toJSONString(context, SerializerFeature.DisableCircularReferenceDetect),
                12,
                TimeUnit.HOURS);
    }

    /**
     * 获取预报上下文
     * @param batchId 批次号
     * @return 预报上下文
     */
    public ManifestPreReportContext get(String batchId) {
        String cacheJson = redisTemplate.opsForValue().get(PRE_REPORT_UPLOAD_KEY_PREFIX + batchId);
        if (StrUtil.isBlank(cacheJson)) {
            return null;
        }
        return JSON.parseObject(cacheJson, ManifestPreReportContext.class);
    }

    /**
     * 删除预报上下文缓存
     * @param batchId 批次号
     */
    public void remove(String batchId) {
        redisTemplate.delete(PRE_REPORT_UPLOAD_KEY_PREFIX + batchId);
    }
}
