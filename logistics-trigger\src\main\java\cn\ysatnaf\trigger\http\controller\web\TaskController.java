package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.manifest.model.req.InvoiceGenerationTaskSubmitReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillInvoiceExportReq;
import cn.ysatnaf.domain.task.model.po.TaskPO;
import cn.ysatnaf.domain.task.model.req.TaskListReq;
import cn.ysatnaf.domain.task.service.TaskService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR> Hang
 */
@Tag(name = "任务管理")
@RequestMapping("/web/task")
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
public class TaskController {

    private final TaskService taskService;

    @PostMapping("/list")
    @Operation(summary = "任务列表")
    public CommonResult<PageResult<TaskPO>> list(@RequestBody @Validated TaskListReq req) {
        return CommonResult.success(taskService.list(req));
    }

    @Operation(summary = "提交主提单发票导出任务")
    @PostMapping("/invoice/masterBillExport")
    public CommonResult<Long> submitMasterBillInvoiceExportTask(@RequestBody @Validated MasterBillInvoiceExportReq req) {
        Long taskId = taskService.createMasterBillInvoiceExportTask(req);
        return CommonResult.success(taskId);
    }

}
