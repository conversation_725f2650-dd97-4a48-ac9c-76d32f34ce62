package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;
import cn.ysatnaf.infrastructure.persistent.po.ReceiverAreaPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * ReceiverAreaConverter
 *
 * <AUTHOR>
 * @date 2023/12/22 14:05
 */
@Mapper
public interface ReceiverAreaConverter {
    ReceiverAreaConverter INSTANCE = Mappers.getMapper(ReceiverAreaConverter.class);

    ReceiverAreaEntity convert(ReceiverAreaPO po);

    List<ReceiverAreaEntity> convert(List<ReceiverAreaPO> poList);
}
