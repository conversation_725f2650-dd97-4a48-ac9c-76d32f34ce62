package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.cost.model.entity.CostRuleEntity;
import cn.ysatnaf.domain.cost.repository.CostRuleRepository;
import cn.ysatnaf.infrastructure.persistent.converter.CostRuleConverter;
import cn.ysatnaf.infrastructure.persistent.dao.CostRuleDao;
import cn.ysatnaf.infrastructure.persistent.po.CostRulePO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class CostRuleRepositoryImpl implements CostRuleRepository {

    private final CostRuleDao costRuleDao;

    @Override
    public void insert(CostRuleEntity costRuleEntity) {
        costRuleDao.insert(CostRuleConverter.INSTANCE.toPO(costRuleEntity));
    }

    @Override
    public void updateById(CostRuleEntity costRuleEntity) {
        costRuleDao.updateById(CostRuleConverter.INSTANCE.toPO(costRuleEntity));
    }

    @Override
    public PageResult<CostRuleEntity> page(Integer pageNo, Integer pageSize) {
        Page<CostRulePO> priceRulePOPage = costRuleDao.selectPage(new Page<>(pageNo, pageSize), null);
        if (CollUtil.isEmpty(priceRulePOPage.getRecords())) {
            return PageResult.empty(priceRulePOPage.getTotal());
        }

        List<CostRuleEntity> priceRuleEntities = CostRuleConverter.INSTANCE.toEntityList(priceRulePOPage.getRecords());
        return new PageResult<>(priceRuleEntities, priceRulePOPage.getTotal());
    }

    @Override
    public CostRuleEntity getById(Long id) {
        return CostRuleConverter.INSTANCE.toEntity(costRuleDao.selectById(id));
    }

    @Override
    public void deleteById(Long id) {
        costRuleDao.deleteById(id);
    }

    @Override
    public void deleteByPlatformType(Integer platformType) {
        LambdaQueryWrapper<CostRulePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CostRulePO::getPlatformType, platformType);
        costRuleDao.delete(lambdaQueryWrapper);
    }

    @Override
    public void insertBatch(List<CostRuleEntity> costRuleEntities) {
        costRuleDao.insertBatchSomeColumn(CostRuleConverter.INSTANCE.toPOList(costRuleEntities));
    }

    @Override
    public List<CostRuleEntity> list() {
        return CostRuleConverter.INSTANCE.toEntityList(costRuleDao.selectList(null));
    }

    @Override
    public List<CostRuleEntity> listByPlatformType(Integer platformType) {
        return CostRuleConverter.INSTANCE.toEntityList(costRuleDao.selectList(new LambdaQueryWrapper<CostRulePO>()
                .eq(CostRulePO::getPlatformType, platformType)));
    }
}
