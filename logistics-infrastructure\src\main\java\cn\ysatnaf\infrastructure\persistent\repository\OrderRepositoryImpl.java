package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.order.model.entity.OrderEntity;
import cn.ysatnaf.domain.order.repository.OrderRepository;
import cn.ysatnaf.infrastructure.persistent.converter.OrderConverter;
import cn.ysatnaf.infrastructure.persistent.dao.OrderDao;
import cn.ysatnaf.infrastructure.persistent.po.OrderPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class OrderRepositoryImpl implements OrderRepository {

    private final OrderDao orderDao;

    @Override
    public void insert(OrderEntity orderEntity) {
        OrderPO orderPO = OrderConverter.INSTANCE.entity2po(orderEntity);
        orderDao.insert(orderPO);
        orderEntity.setId(orderPO.getId());
    }

    @Override
    public PageResult<OrderEntity> search(String openid, String expressNo, String orderNo, Integer status, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<OrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(openid), OrderPO::getOpenid, openid);
        wrapper.eq(status != null, OrderPO::getStatus, status);
        wrapper.like(StrUtil.isNotBlank(expressNo), OrderPO::getExpressNo, expressNo);
        wrapper.like(StrUtil.isNotBlank(orderNo), OrderPO::getOrderNo, orderNo);
        wrapper.orderByDesc(OrderPO::getCreateTime);
        Page<OrderPO> orderPOPage = orderDao.selectPage(new Page<>(pageNo, pageSize), wrapper);

        if (orderPOPage.getRecords().isEmpty()) {
            return new PageResult<>(orderPOPage.getTotal());
        }

        return new PageResult<>(OrderConverter.INSTANCE.entityList2poList(orderPOPage.getRecords()), orderPOPage.getTotal());
    }

    @Override
    public OrderEntity getById(Long orderId) {
        return OrderConverter.INSTANCE.po2entity(orderDao.selectById(orderId));
    }

    @Override
    public void updateById(OrderEntity orderEntity) {
        orderDao.updateById(OrderConverter.INSTANCE.entity2po(orderEntity));
    }

    @Override
    public PageResult<OrderEntity> pageMpOrder(Integer status, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<OrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjUtil.isNotNull(status), OrderPO::getStatus, status);
        Page<OrderPO> page = orderDao.selectPage(new Page<>(pageNo, pageSize), wrapper);
        List<OrderPO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return new PageResult<>(page.getTotal());
        }
        List<OrderEntity> orderEntities = OrderConverter.INSTANCE.po2entityList(records);
        return new PageResult<>(orderEntities, page.getTotal());
    }

    @Override
    public Integer countByStatus(Integer status) {
        LambdaQueryWrapper<OrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjUtil.isNotNull(status), OrderPO::getStatus, status);
        return Math.toIntExact(orderDao.selectCount(wrapper));
    }
}
