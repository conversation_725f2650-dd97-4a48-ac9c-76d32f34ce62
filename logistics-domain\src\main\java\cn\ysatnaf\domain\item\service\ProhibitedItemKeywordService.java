package cn.ysatnaf.domain.item.service;

import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordAddReq;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordDTO;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordPageReq;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordUpdateReq;
import cn.ysatnaf.types.common.PageResult; // 使用确认的路径

import java.util.List;

/**
 * 违禁关键词服务接口
 */
public interface ProhibitedItemKeywordService {

    /**
     * 添加违禁关键词
     *
     * @param req 请求参数
     * @return 新增记录的ID
     */
    Long addKeyword(ProhibitedItemKeywordAddReq req);

    /**
     * 更新违禁关键词
     *
     * @param req 请求参数
     * @return 是否成功
     */
    boolean updateKeyword(ProhibitedItemKeywordUpdateReq req);

    /**
     * 删除违禁关键词
     *
     * @param id 要删除的ID
     * @return 是否成功
     */
    boolean deleteKeyword(Long id);

    /**
     * 根据ID获取违禁关键词详情
     *
     * @param id ID
     * @return DTO 或 null
     */
    ProhibitedItemKeywordDTO getKeywordById(Long id);

    /**
     * 分页查询违禁关键词
     *
     * @param req 分页及查询参数
     * @return 分页结果 DTO 列表
     */
    PageResult<ProhibitedItemKeywordDTO> pageKeywords(ProhibitedItemKeywordPageReq req);

    /**
     * 获取所有启用的违禁关键词列表
     *
     * @return 启用的关键词字符串列表
     */
    List<String> listActiveKeywords();

    /**
     * 检查给定的物品名称是否包含任何启用的违禁关键词
     *
     * @param itemName 物品名称
     * @return 如果包含违禁词，则返回第一个匹配到的违禁词；否则返回 null
     */
    String checkItemName(String itemName);

} 