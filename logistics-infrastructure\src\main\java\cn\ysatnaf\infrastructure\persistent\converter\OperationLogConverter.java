package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.log.model.entity.OperationLogEntity;
import cn.ysatnaf.infrastructure.persistent.po.OperationLogPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OperationLogConverter {
    OperationLogConverter INSTANCE = Mappers.getMapper(OperationLogConverter.class);

    OperationLogPO toPO(OperationLogEntity entity);

    List<OperationLogPO> toPOList(List<OperationLogEntity> operationLogEntities);

    OperationLogEntity toEntity(OperationLogPO po);

    List<OperationLogEntity> toEntityList(List<OperationLogPO> records);
}
