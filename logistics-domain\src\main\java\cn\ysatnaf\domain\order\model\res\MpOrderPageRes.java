package cn.ysatnaf.domain.order.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * MpOrderPageRes
 *
 * <AUTHOR> Hang
 * @date 2024/2/20 9:14
 */
@Schema(description = "分页查询公众号订单返回参数")
@Data
public class MpOrderPageRes {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "快递单号（佐川号码）")
    private String expressNo;

    @Schema(description = "寄件人名称")
    private String senderName;

    @Schema(description = "寄件人电话")
    private String senderPhone;

    @Schema(description = "寄件人省份")
    private String senderProvinceName;

    @Schema(description = "寄件人城市")
    private String senderCityName;

    @Schema(description = "寄件人区县")
    private String senderDistrictName;

    @Schema(description = "寄件人详细地址")
    private String senderAddressDetail;

    @Schema(description = "订单状态:1-待取件;2-待支付;-1-已取消")
    private Integer status;

    @Schema(description = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime paymentTime;
}
