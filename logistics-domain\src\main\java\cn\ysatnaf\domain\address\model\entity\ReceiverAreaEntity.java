package cn.ysatnaf.domain.address.model.entity;

import lombok.Data;

/**
 * ReceiverAreaEntity
 *
 * <AUTHOR>
 * @date 2023/12/22 11:58
 */
@Data
public class ReceiverAreaEntity {
    /**
     * ID
     */
    private Long id;

    /**
     * 邮编
     */
    private String zipCode;
    /**
     * 日本都道府县级名称
     */
    private String prefectureName;
    /**
     * 日本市区町村级名称
     */
    private String municipalName;
    /**
     * 日本丁目级名称
     */
    private String localitiesName;

    /**
     * 日本都道府县级名称(英文)
     */
    private String prefectureEnName;
    /**
     * 日本市区町村级名称(英文)
     */
    private String municipalEnName;
    /**
     * 日本丁目级名称(英文)
     */
    private String localitiesEnName;

    public String getEnglishArea() {
        return prefectureEnName + " " + municipalEnName + " " + localitiesEnName;
    }

}
