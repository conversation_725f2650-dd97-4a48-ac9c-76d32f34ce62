package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.manifest.model.entity.Tracking;
import cn.ysatnaf.infrastructure.persistent.po.TrackingPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Mapper
public interface TrackingConverter {
    TrackingConverter INSTANCE = Mappers.getMapper(TrackingConverter.class);

    TrackingPO toTrackingPO(Tracking tracking);

    List<TrackingPO> toTrackingPOList(List<Tracking> trackingList);

    Tracking toTracking(TrackingPO trackingPO);

    List<Tracking> toTrackingList(List<TrackingPO> trackingPOList);
}
