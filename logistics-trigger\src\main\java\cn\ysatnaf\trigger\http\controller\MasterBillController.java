package cn.ysatnaf.trigger.http.controller;

import cn.ysatnaf.domain.manifest.model.req.MasterBillCreateReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillExportReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillPageReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillUpdateReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillGenerateBarcodePdfReq;
import cn.ysatnaf.domain.manifest.model.res.MasterBillDetailRes;
import cn.ysatnaf.domain.manifest.service.MasterBillService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 主提单控制器
 * <AUTHOR>
 */
@Tag(name = "主提单管理")
@RestController
@RequestMapping("/api/masterBill")
@RequiredArgsConstructor
@Slf4j
public class MasterBillController {

    private final MasterBillService masterBillService;

    @Operation(summary = "创建主提单")
    @PostMapping("/create")
    public CommonResult<Long> create(@RequestBody @Validated MasterBillCreateReq req) {
        log.info("创建主提单请求: {}", req);
        Long masterBillId = masterBillService.create(req);
        return CommonResult.success(masterBillId);
    }

    @Operation(summary = "更新主提单")
    @PostMapping("/update")
    public CommonResult<Boolean> update(@RequestBody @Validated MasterBillUpdateReq req) {
        log.info("更新主提单请求: {}", req);
        boolean success = masterBillService.update(req);
        return CommonResult.success(success);
    }

    @Operation(summary = "删除主提单")
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> delete(@Parameter(description = "主提单ID") @PathVariable Long id) {
        log.info("删除主提单, id: {}", id);
        boolean success = masterBillService.delete(id);
        return CommonResult.success(success);
    }

    @Operation(summary = "获取主提单详情")
    @GetMapping("/{id}")
    public CommonResult<MasterBillDetailRes> getDetail(@Parameter(description = "主提单ID") @PathVariable Long id) {
        log.info("获取主提单详情, id: {}", id);
        MasterBillDetailRes detail = masterBillService.getDetail(id);
        return CommonResult.success(detail);
    }

    @Operation(summary = "分页查询主提单")
    @PostMapping("/page")
    public CommonResult<PageResult<MasterBillDetailRes>> page(@RequestBody MasterBillPageReq req) {
        log.info("分页查询主提单请求: {}", req);
        PageResult<MasterBillDetailRes> pageResult = masterBillService.page(req);
        return CommonResult.success(pageResult);
    }

    @Operation(summary = "运单关联主提单")
    @PostMapping("/assignManifest")
    public CommonResult<Boolean> assignManifest(
            @Parameter(description = "运单ID") @RequestParam Long manifestId,
            @Parameter(description = "主提单ID") @RequestParam Long masterBillId) {
        log.info("运单关联主提单, manifestId: {}, masterBillId: {}", manifestId, masterBillId);
        boolean success = masterBillService.assignManifest(manifestId, masterBillId);
        return CommonResult.success(success);
    }

    @Operation(summary = "更新主提单统计信息")
    @PostMapping("/{id}/updateStatistics")
    public CommonResult<Boolean> updateStatistics(@Parameter(description = "主提单ID") @PathVariable Long id) {
        log.info("更新主提单统计信息, id: {}", id);
        boolean success = masterBillService.updateStatistics(id);
        return CommonResult.success(success);
    }
    
    @Operation(summary = "导出日本海关文件")
    @PostMapping("/exportCustomsDocument")
    public void exportCustomsDocument(@RequestBody @Validated MasterBillExportReq req, HttpServletResponse response) {
        log.info("导出日本海关文件请求: {}", req);
        masterBillService.exportCustomsDocument(req, response);
    }

    @Operation(summary = "根据提单生成条码PDF")
    @PostMapping("/generateBarcodePdf")
    public void generateBarcodePdf(@RequestBody @Validated MasterBillGenerateBarcodePdfReq req, HttpServletResponse response) {
        log.info("根据提单生成条码PDF请求: {}", req);
        masterBillService.generateBarcodePdf(req, response);
    }
} 