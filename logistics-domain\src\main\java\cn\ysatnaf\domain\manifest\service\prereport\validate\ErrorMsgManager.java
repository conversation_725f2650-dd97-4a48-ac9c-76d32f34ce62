package cn.ysatnaf.domain.manifest.service.prereport.validate;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ErrorMsgManager {

    /**
     * 错误信息模版语句
     */
    private final static String ERROR_MSG_TEMPLATE = "第%d行错误：%s \r\n";

    /**
     * 错误信息列表
     */
    private List<String> errorMessages;

    public ErrorMsgManager() {
        this.errorMessages = new ArrayList<>();
    }

    /**
     * 是否存在校验错误
     */
    public boolean hasError() {
        return !errorMessages.isEmpty();
    }

    /**
     * 添加错误信息
     * @param errorMsgBuilder 错误信息构造器
     */
    public void addErrorMsg(ErrorMsgBuilder errorMsgBuilder) {
        errorMessages.add(String.format(ERROR_MSG_TEMPLATE, errorMsgBuilder.getRowNumber(), errorMsgBuilder.getErrorMsg()));
    }
}
