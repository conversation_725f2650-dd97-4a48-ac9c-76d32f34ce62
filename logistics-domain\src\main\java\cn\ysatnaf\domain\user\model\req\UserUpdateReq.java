package cn.ysatnaf.domain.user.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "更新用户入参")
@Data
public class UserUpdateReq {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long id;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "buzhidao")
    @Length(min = 4, max = 16, message = "密码长度为 4-16 位")
    private String password;

    @Schema(description = "昵称")
    @Length(min = 2, max = 16)
    private String nickname;

    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "账号状态：0-禁用；1-启用")
    private Integer status;

    @Schema(description = "操作密码")
    @NotBlank(message = "操作密码不能为空")
    private String ownPassword;
}
