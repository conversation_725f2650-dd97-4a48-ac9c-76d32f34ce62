<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
        <parent>
        <artifactId>logistics</artifactId>
        <groupId>cn.ysatnaf</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>logistics-infrastructure</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- 系统模块 -->
        <dependency>
            <groupId>cn.ysatnaf</groupId>
            <artifactId>logistics-domain</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>logistics-infrastructure</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>create-from-project</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
