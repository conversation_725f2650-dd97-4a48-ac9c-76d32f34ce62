package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.log.model.entity.OperationLogEntity;
import cn.ysatnaf.domain.log.model.req.OperationLogLogReq;
import cn.ysatnaf.domain.log.model.req.OperationLogPageReq;
import cn.ysatnaf.domain.log.model.res.PageOperationLogRes;
import cn.ysatnaf.domain.log.service.OperationLogService;
import cn.ysatnaf.domain.system.model.entity.SystemInfo;
import cn.ysatnaf.domain.system.model.req.SystemInfoUpdateReq;
import cn.ysatnaf.domain.system.service.SystemService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;

/**
 * <AUTHOR> Hang
 */
@Tag(name = "系统管理")
@RequestMapping("/web/system")
@Slf4j
@RestController
@RequiredArgsConstructor
public class SystemController {

    private final SystemService systemService;

    private final OperationLogService operationLogService;

    @Operation(summary = "更新系统信息")
    @PermitAll
    @PostMapping("/updateInfo")
    public CommonResult<Boolean> updateInfo(@RequestBody SystemInfoUpdateReq req) {
        return CommonResult.success(systemService.updateInfo(req));
    }

    @Operation(summary = "获取系统信息")
    @PermitAll
    @PostMapping("/getInfo")
    public CommonResult<SystemInfo> getInfo() {
        return CommonResult.success(systemService.getInfo());
    }

    @Operation(summary = "查询操作日志")
    @PermitAll
    @PostMapping("/pageOperationLog")
    public CommonResult<PageResult<PageOperationLogRes>> pageOperationLog(@RequestBody OperationLogPageReq req) {
        return CommonResult.success(operationLogService.pageLog(req));
    }

    @Operation(summary = "记录日志")
    @PermitAll
    @PostMapping("/log")
    public CommonResult<Boolean> log(@RequestBody OperationLogLogReq req) {
        return CommonResult.success(operationLogService.log(req));
    }

}
