package cn.ysatnaf.infrastructure.persistent.repository.trackingnumber;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberImportBatch;
import cn.ysatnaf.domain.trackingnumber.repository.TrackingNumberImportBatchRepository;
import cn.ysatnaf.infrastructure.persistent.converter.trackingnumber.TrackingNumberImportBatchConverter;
import cn.ysatnaf.infrastructure.persistent.dao.trackingnumber.TrackingNumberImportBatchDao;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberImportBatchPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 单号导入批次记录仓库实现
 * <AUTHOR>
 */
@Repository
public class TrackingNumberImportBatchRepositoryImpl extends ServiceImpl<TrackingNumberImportBatchDao, TrackingNumberImportBatchPO> implements TrackingNumberImportBatchRepository {

    private final TrackingNumberImportBatchConverter converter = TrackingNumberImportBatchConverter.INSTANCE;

    @Override
    public TrackingNumberImportBatch save(TrackingNumberImportBatch batch) {
        TrackingNumberImportBatchPO batchPO = converter.toPO(batch);
        // 使用 ServiceImpl 的 saveOrUpdate，可以处理插入和更新
        saveOrUpdate(batchPO);
        // 将包含生成ID的PO转换回Entity返回
        return converter.toEntity(batchPO);
    }

    @Override
    public TrackingNumberImportBatch findById(Long id) {
        // 使用 ServiceImpl 的 getById
        TrackingNumberImportBatchPO batchPO = getById(id);
        return converter.toEntity(batchPO);
    }

    @Override
    public PageResult<TrackingNumberImportBatch> pageQuery(Long uploaderId,
                                                             Long channelId,
                                                             LocalDateTime startTime,
                                                             LocalDateTime endTime,
                                                             int pageNo,
                                                             int pageSize) {
        LambdaQueryWrapper<TrackingNumberImportBatchPO> queryWrapper = Wrappers.lambdaQuery();
        // 添加查询条件
        queryWrapper.eq(uploaderId != null, TrackingNumberImportBatchPO::getUploaderId, uploaderId)
                .eq(channelId != null, TrackingNumberImportBatchPO::getChannelId, channelId)
                .ge(startTime != null, TrackingNumberImportBatchPO::getImportTime, startTime)
                .le(endTime != null, TrackingNumberImportBatchPO::getImportTime, endTime)
                .orderByDesc(TrackingNumberImportBatchPO::getImportTime); // 按导入时间降序排序

        // 创建MyBatis Plus分页对象
        Page<TrackingNumberImportBatchPO> page = new Page<>(pageNo, pageSize);
        // 执行分页查询 (ServiceImpl 提供了 page 方法)
        Page<TrackingNumberImportBatchPO> pageResultPO = page(page, queryWrapper);

        // 处理查询结果
        List<TrackingNumberImportBatchPO> records = pageResultPO.getRecords();
        if (CollUtil.isEmpty(records)) {
            // 如果结果为空，返回包含总数的空分页结果
            return PageResult.empty(pageResultPO.getTotal());
        }

        // 将PO列表转换为Entity列表
        List<TrackingNumberImportBatch> entityList = converter.toEntityList(records);
        // 返回包含Entity列表和总数的分页结果
        return new PageResult<>(entityList, pageResultPO.getTotal());
    }
} 