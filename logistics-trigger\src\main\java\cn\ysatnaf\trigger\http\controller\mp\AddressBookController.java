package cn.ysatnaf.trigger.http.controller.mp;

import cn.ysatnaf.domain.address.model.entity.AddressBookEntity;
import cn.ysatnaf.domain.address.service.AddressBookService;
import cn.ysatnaf.trigger.http.adapter.AddressBookAdapter;
import cn.ysatnaf.trigger.http.req.address.AddressBookAddReq;
import cn.ysatnaf.trigger.http.req.address.AddressBookDeleteReq;
import cn.ysatnaf.trigger.http.req.address.AddressBookPageReq;
import cn.ysatnaf.trigger.http.req.address.AddressBookUpdateReq;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;

/**
 * AddressBookController
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 10:17
 */
@Tag(name = "地址簿相关")
@RequestMapping("/mp/addressBook")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class AddressBookController {

    private final AddressBookService addressBookService;

    @PostMapping("/add")
    @PermitAll
    @Operation(summary = "添加地址")
    public CommonResult<Boolean> add(@RequestBody @Validated AddressBookAddReq req) {
        AddressBookEntity addressBookEntity = AddressBookAdapter.INSTANCE.req2Entity(req);
        return CommonResult.success(addressBookService.add(addressBookEntity));
    }

    @PostMapping("/delete")
    @PermitAll
    @Operation(summary = "删除地址")
    public CommonResult<Boolean> delete(@RequestBody @Validated AddressBookDeleteReq req) {
        return CommonResult.success(addressBookService.delete(req.getId()));
    }

    @PostMapping("/update")
    @PermitAll
    @Operation(summary = "更新地址")
    public CommonResult<Boolean> update(@RequestBody @Validated AddressBookUpdateReq req) {
        AddressBookEntity addressBookEntity = AddressBookAdapter.INSTANCE.req2Entity(req);
        return CommonResult.success(addressBookService.update(addressBookEntity));
    }

    @PostMapping("/getDefault/{openid}")
    @PermitAll
    @Operation(summary = "获取默认地址")
    public CommonResult<AddressBookEntity> getDefault(@PathVariable("openid") String openid) {
        return CommonResult.success(addressBookService.getDefault(openid));
    }

    @PostMapping("/page")
    @PermitAll
    @Operation(summary = "分页查询地址簿")
    public CommonResult<PageResult<AddressBookEntity>> page(@RequestBody @Validated AddressBookPageReq req) {
        return CommonResult.success(addressBookService.page(req.getOpenid(), req.getPageNo(), req.getPageSize()));
    }
}
