package cn.ysatnaf.trigger.http.req.address;

import cn.ysatnaf.types.validation.Phone;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * AddressBookAddReq
 *
 * <AUTHOR>
 * @date 2023/12/22 10:20
 */
@Schema(description = "编辑地址 入参")
@Data
public class AddressBookUpdateReq {

    @Schema(description = "地址簿ID")
    private Long id;

    @Schema(description = "姓名")
    @NotBlank
    private String name;

    @Schema(description = "性别")
    private Integer gender;

    @Schema(description = "手机号")
    @Phone
    private String phone;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "区县编码")
    private String districtCode;

    @Schema(description = "区县名称")
    private String districtName;

    @Schema(description = "街道代码")
    private String streetCode;

    @Schema(description = "街道名称")
    private String streetName;
    @Schema(description = "居委/社区")
    private String committeeCode;

    @Schema(description = "居委/社区名称")
    private String committeeName;

    @Schema(description = "详细地址")
    private String detail;

    @Schema(description = "标签")
    private String label;

    @Schema(description = "是否默认地址")
    private Boolean isDefault;
}
