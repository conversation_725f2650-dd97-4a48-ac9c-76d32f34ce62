package cn.ysatnaf.infrastructure.persistent.convert;

import cn.ysatnaf.domain.manifest.model.entity.MasterBill;
import cn.ysatnaf.infrastructure.persistent.dataobject.MasterBillDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 主提单DO与Entity转换类
 * <AUTHOR>
 */
@Mapper
public interface MasterBillConvert {

    MasterBillConvert INSTANCE = Mappers.getMapper(MasterBillConvert.class);

    /**
     * DO转Entity
     */
    MasterBill convert(MasterBillDO bean);

    /**
     * Entity转DO
     */
    MasterBillDO convertDO(MasterBill bean);

    /**
     * DO列表转Entity列表
     */
    List<MasterBill> convertList(List<MasterBillDO> beans);
} 