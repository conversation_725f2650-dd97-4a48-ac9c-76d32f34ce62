package cn.ysatnaf.domain.statistics.model.req;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class MonthlyQuantityReq {
    @Pattern(regexp = "^\\d{4}-(0[1-9]|1[0-2])$", message = "月份格式应为YYYY-MM")
    private String month; // 格式YYYY-MM
    
    @Min(0)
    @Max(23)
    private Integer startHour = 0; // 默认0点

    // 获取统计起始时间
    public LocalDateTime getStartTime() {
        LocalDate baseDate = StrUtil.isBlank(month) ?
            LocalDate.now().withDayOfMonth(1) :
            LocalDate.parse(month + "-01");
        return baseDate.atTime(startHour, 0);
    }

    // 获取统计结束时间
    public LocalDateTime getEndTime() {
        return getStartTime().plusMonths(1);
    }
}