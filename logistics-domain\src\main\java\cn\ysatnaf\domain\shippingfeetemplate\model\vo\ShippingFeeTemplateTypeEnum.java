package cn.ysatnaf.domain.shippingfeetemplate.model.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 运费模板类型枚举类
 */
@Getter
@AllArgsConstructor
public enum ShippingFeeTemplateTypeEnum {

    /**
     * 模板类型 1-普通 2-带电 3-投函
     */
    GENERAL(1,"普通模板", ""),

    ELECTRONICS(2,"带电模板", "带电"),

    SMALL_PARCEL(3,"投函模板", "投函"),

    /**
     * 普通模版，佐川大阪
     */
    GENERAL_OSAKA(4,"佐川大阪模板", "佐川大阪"),
    SMALL_PARCEL_OSAKA(5,"投函模板（大阪）", "投函大阪"),
    SPECIAL(6,"特殊模板", "特殊"),
    ;

    private final Integer code;

    private final String desc;

    private final String shortName;

    public static ShippingFeeTemplateTypeEnum getByCode(Integer code) {
        for (ShippingFeeTemplateTypeEnum e : ShippingFeeTemplateTypeEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return GENERAL;
    }
}
