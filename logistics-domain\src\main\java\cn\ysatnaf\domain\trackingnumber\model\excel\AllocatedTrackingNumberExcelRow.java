package cn.ysatnaf.domain.trackingnumber.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
// 移除不再需要的 import
// import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// import java.time.LocalDateTime;

/**
 * 用于导出已分配单号的Excel行数据模型 (只包含单号)
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AllocatedTrackingNumberExcelRow {

    @ExcelProperty("单号") // 可以保留表头，或者如果不需要表头，移除此注解
    private String trackingNumber;

    // 移除 carrierName 和 allocationTime 字段
    /*
    @ExcelProperty("承运商")
    private String carrierName;

    @ExcelProperty("分配时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss") // 指定日期时间格式
    private LocalDateTime allocationTime;
    */

} 