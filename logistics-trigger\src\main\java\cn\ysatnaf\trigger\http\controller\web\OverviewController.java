package cn.ysatnaf.trigger.http.controller.web;


import cn.ysatnaf.domain.statistics.model.dto.*;
import cn.ysatnaf.domain.statistics.model.req.CompanyRankingReq;
import cn.ysatnaf.domain.statistics.model.req.MonthlyQuantityReq;
import cn.ysatnaf.domain.statistics.model.vo.*;
import cn.ysatnaf.domain.statistics.service.impl.OverviewService;
import cn.ysatnaf.types.common.CommonResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/statistics/overview")
@RequiredArgsConstructor
public class OverviewController {

    private final OverviewService overviewService;

    @GetMapping("/summary")
    public CommonResult<SummaryVO> getSummary(@RequestParam(required = false, defaultValue = "18") Integer startHour) {
        if (startHour < 0 || startHour > 23) {
            throw new IllegalArgumentException("startHour 参数必须在 0-23 之间");
        }
        return CommonResult.success(overviewService.getSummary(startHour));
    }

    @GetMapping("/today")
    public CommonResult<TodayVO> getTodaySummary(@RequestParam(required = false, defaultValue = "18") Integer startHour) {
        if (startHour < 0 || startHour > 23) {
            throw new IllegalArgumentException("startHour 参数必须在 0-23 之间");
        }
        return CommonResult.success(overviewService.getTodaySummary(startHour));
    }


    @GetMapping("/templates/today")
    public CommonResult<List<TemplateDailyVO>> getDailyTemplateStats(@RequestParam(required = false, defaultValue = "18") Integer startHour) {
        return CommonResult.success(overviewService.getDailyTemplateStats(startHour));
    }

    @GetMapping("/templates/proportion")
    public CommonResult<List<TemplateProportionVO>> getTemplateProportion() {
        return CommonResult.success(overviewService.getTemplateProportion());
    }

    @GetMapping("/trend/last30days")
    public CommonResult<TrendVO> getLast30DaysTrend(@RequestParam(required = false, defaultValue = "18") Integer startHour) {
        return CommonResult.success(overviewService.getLast30DaysTrend(startHour));
    }

    @GetMapping("/companies/ranking")
    public CommonResult<List<CompanyRankingDTO>> getCompanyRanking(CompanyRankingReq req) {
        List<CompanyRankingDTO> ranking = overviewService.getCompanyRanking(req);
        return CommonResult.success(ranking);
    }

    @GetMapping("/amount/distribution")
    public CommonResult<List<AmountDistributionDTO>> getDistribution() {
        List<AmountDistributionDTO> data = overviewService.getAmountDistribution();
        return CommonResult.success(data);
    }

    @GetMapping("/monthly-quantity")
    public CommonResult<MonthlyQuantityDTO> getMonthlyQuantity(@Valid MonthlyQuantityReq req) {
        return CommonResult.success(overviewService.getMonthlyQuantity(req));
    }

    @GetMapping("/monthly-amount")
    public CommonResult<MonthlyAmountDTO> getMonthlyAmount(
            @Valid MonthlyQuantityReq req) {
        return CommonResult.success(overviewService.getMonthlyAmount(req));
    }

    @GetMapping("/today-quantity-detail")
    public CommonResult<TodayQuantityDetailDTO> getTodayQuantityDetail(@RequestParam(defaultValue = "18") int startHour) {
        TodayQuantityDetailDTO detail = overviewService.getTodayQuantityDetail(startHour);
        return CommonResult.success(detail);
    }

    @GetMapping("/today-amount-detail")
    public CommonResult<TodayAmountDetailDTO> getTodayAmountDetail(@RequestParam(defaultValue = "18") int startHour) {
        TodayAmountDetailDTO detail = overviewService.getTodayAmountDetail(startHour);
        return CommonResult.success(detail);
    }
}
