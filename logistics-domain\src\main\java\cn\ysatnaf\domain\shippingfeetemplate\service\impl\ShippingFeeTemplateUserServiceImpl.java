package cn.ysatnaf.domain.shippingfeetemplate.service.impl;

import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplatePO;
import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplateUserPO;
import cn.ysatnaf.domain.shippingfeetemplate.repository.ShippingFeeTemplateUserRepository;
import cn.ysatnaf.domain.shippingfeetemplate.service.ShippingFeeTemplateUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@Service
public class ShippingFeeTemplateUserServiceImpl implements ShippingFeeTemplateUserService {

    private final ShippingFeeTemplateUserRepository shippingFeeTemplateUserRepository;

    @Override
    public ShippingFeeTemplateUserPO getByUserIdAndType(Long userId, Integer type) {
        return shippingFeeTemplateUserRepository.getByUserIdAndType(userId, type);
    }

    @Override
    public void bind(List<ShippingFeeTemplatePO> templates, Long userId) {
        for (ShippingFeeTemplatePO template : templates) {
            Long templateId = template.getId();
            Integer type = template.getType();
            // 查看是否已经存在绑定关系
            ShippingFeeTemplateUserPO templateUserPO = shippingFeeTemplateUserRepository.getByUserIdAndType(userId, type);
            if (templateUserPO != null) {
                templateUserPO.setTemplateId(templateId);
                shippingFeeTemplateUserRepository.updateById(templateUserPO);
            } else {
                templateUserPO = ShippingFeeTemplateUserPO.builder()
                        .userId(userId)
                        .templateId(templateId)
                        .type(type).build();
                shippingFeeTemplateUserRepository.insert(templateUserPO);
            }
        }
    }

    @Override
    public List<ShippingFeeTemplateUserPO> getByUserIds(List<Long> userIds) {
        return shippingFeeTemplateUserRepository.getByUserIds(userIds);
    }
}
