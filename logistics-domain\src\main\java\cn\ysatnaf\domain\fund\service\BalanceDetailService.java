package cn.ysatnaf.domain.fund.service;

import cn.ysatnaf.domain.fund.model.req.BalanceDetailExportReq;
import cn.ysatnaf.domain.fund.model.req.BalanceDetailPageReq;
import cn.ysatnaf.domain.fund.model.res.BalanceDetailPageRes;
import cn.ysatnaf.types.common.PageResult;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
public interface BalanceDetailService {
    PageResult<BalanceDetailPageRes> pageBalanceDetails(BalanceDetailPageReq req);

    void record(Long accountId, BigDecimal originalBalance, BigDecimal newBalance, String remark);

    void record(Long accountId, BigDecimal originalBalance, BigDecimal newBalance, String remark, Long manifestId);

    void exportBalanceDetails(BalanceDetailExportReq req, HttpServletResponse response);
}
