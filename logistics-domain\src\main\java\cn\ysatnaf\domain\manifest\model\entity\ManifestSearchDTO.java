package cn.ysatnaf.domain.manifest.model.entity;

import cn.ysatnaf.types.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * ManifestEntity
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 15:11
 */
@Data
@Builder
@AllArgsConstructor
public class ManifestSearchDTO {

    private ManifestSearchDTO() {
    }

    private Long id;

    /**
     * 小包运单号码
     * HAWB
     */
    private String hawb;

    /**
     * P'KG
     * 小包个数
     * 电商小包只限1个
     */
    private Integer pKg;

    /**
     * WT(KG)
     * 小包重量
     * 只留小数点后一位，后面舍去（向下取整）
     */
    private BigDecimal weight;

    /**
     * 通过descriptionZh翻译成的英文品名，导出海关文件时使用
     * DESCRIPTION
     * 品名
     * 只能使用半角符号
     */
    private String description;

    /**
     * VALUE
     * 金额
     * 1000-6000随机整十的数
     */
    private Integer value;

    /**
     * CNEE COMPANY'S NAME
     * 收件人
     * 卖家人名只能用英文写法（日文名需要转换成罗马拼音）
     */
    private String cneeNameEn;

    /**
     * CNEE ADDRESS
     * 收件地址 如：福岡県 久留米市 山川神代1-1-3
     */
    private String cneeAddressEn;

    /**
     * PHONE NO.
     * 收件人电话号码
     * "注：日本手机号码只有4中开头，080、090、070、050，一共11位。
     * 没有手机只有座机的客人，座机号是0开头的10位号码。
     * "
     */
    private String phoneNo;

    /**
     * SHIPPER'S NAME
     * 发件代理名称
     */
    private String shipperName;

    /**
     * SHIPPER'S ADDRESS
     * 发件代理地址
     */
    private String shipperAddress;

    /**
     * CC
     * 一律填写PP
     */
    private String cc;

    /**
     * BillNO2
     * 佐川号码
     */
    private String billNo2;

    /**
     * CC Money
     * 一律为0
     */
    private Integer ccMoney;

    /**
     * HF NO
     * 与BILLNO2号码一致
     */
    private String hfNo;

    /**
     * M3/KG
     * 体积重量
     */
    private BigDecimal m3Kg;

    /**
     * Cnee Zip
     * 收件人邮编
     * 一律为7位数字
     */
    private String cneeZip;

    /**
     * Cnee Company
     * 收件人名（汉字）
     */
    private String cneeCompany;

    /**
     * Cnee Address
     */
    private String cneeAddress;

    /**
     * Cnee Name
     * 收件人名
     */
    private String cneeName;

    /**
     * Cnee Tel
     * 收件人联系电话
     */
    private String cneeTel;

    /**
     * HAWB
     * 佐川号码
     */
    private String hawb2;

    /**
     * LWH
     * 长宽高
     */
    private String lwh;

    /**
     * PackageNO
     * 保税库要求的区分装袋的号码
     */
    private String packageNo;

    /**
     * 运单所属用户ID
     */
    private Long userId;

    /**
     * 运单状态
     */
    private Integer status;

    /**
     * 运单来源
     */
    private Integer sourceType;

    /**
     * 数据创建者ID
     */
    private Long creatorId;

    public static final String SHIPPER_NAME = "Fuzhou Sufeng Information Technology Co., Ltd";
    public static final String SHIPPER_ADDRESS = "1902, 19th Floor, Building B, Netcom Smart Center, No. 11 Keji East Road, Shangjie Town, Minhou County, Fuzhou City";

    public static ManifestSearchDTO of(Manifest manifest) {
        return ManifestSearchDTO.builder()
                .id(manifest.getId())
                .hawb(manifest.getOrderNo())
                .pKg(1)
                .weight(manifest.getWeight().compareTo(BigDecimal.valueOf(0.1)) < 0 ? BigDecimal.valueOf(0.10) : manifest.getWeight())
                .description(manifest.getDescription())
                .value(manifest.getValue().setScale(0, RoundingMode.HALF_UP).intValue())
                .cneeNameEn(manifest.getReceiverEnName())
                .cneeAddressEn(manifest.getPrefectureEnName() + " " + manifest.getMunicipalEnName() + " " + manifest.getLocalitiesEnName() + " " + StringUtil.extractNumbersAndHyphens(manifest.getReceiverAddress()))
                .phoneNo(manifest.getReceiverPhone())
                .shipperName(SHIPPER_NAME)
                .shipperAddress(SHIPPER_ADDRESS)
                .cc("PP")
                .billNo2(manifest.getTrackingNumber())
                .ccMoney(0)
                .hfNo(manifest.getOrderNo())
                .m3Kg(manifest.getEffectiveWeight())
                .cneeZip(manifest.getReceiverZipCode())
                .cneeCompany(manifest.getReceiverName())
                .cneeAddress(StringUtil.toHalfWidth(manifest.getReceiverAddress().replaceAll("\r\n", " ").replaceAll("\n", " ")))
                .cneeName(manifest.getReceiverName())
                .cneeTel(manifest.getReceiverPhone())
                .hawb2(manifest.getTrackingNumber())
                .lwh("")
                .packageNo(manifest.getPackageNo())
                .userId(manifest.getUserId())
                .status(manifest.getStatus())
                .sourceType(manifest.getSourceType())
                .creatorId(manifest.getCreatorId())
                .build();
    }

}
