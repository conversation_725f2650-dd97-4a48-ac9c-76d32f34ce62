package cn.ysatnaf.domain.manifest.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 主提单生成条码PDF请求DTO
 * <AUTHOR>
 */
@Data
public class MasterBillGenerateBarcodePdfReq {

    /**
     * 主提单ID(必填)
     */
    @NotNull(message = "主提单ID不能为空")
    private Long masterBillId;

    /**
     * 目的地 1-东京 2-大阪(必填)
     */
    @NotNull(message = "目的地不能为空")
    private Integer destination;

    /**
     * 运费模板类型列表(必填)
     * 1-普通 2-带电 3-投函
     */
    @NotNull(message = "运费模板类型不能为空")
    private List<Integer> shippingFeeTemplateTypes;
} 