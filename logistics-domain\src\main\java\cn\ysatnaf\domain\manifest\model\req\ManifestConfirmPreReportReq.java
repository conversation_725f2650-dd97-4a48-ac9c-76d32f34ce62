package cn.ysatnaf.domain.manifest.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 确认预报入参
 */
@Data
public class ManifestConfirmPreReportReq {

    /**
     * 批次号
     */
    @Schema(description = "批次号", required = true)
    @NotBlank(message = "批次号不能为空")
    private String batchId;

    /**
     * 是否覆盖预报过的数据
     */
    @Schema(description = "是否覆盖预报过的数据", required = true)
    @NotNull(message = "是否覆盖不能为空")
    private Boolean overwrite;
}
