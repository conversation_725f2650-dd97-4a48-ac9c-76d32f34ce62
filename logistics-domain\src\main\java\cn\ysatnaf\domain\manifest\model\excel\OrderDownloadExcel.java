package cn.ysatnaf.domain.manifest.model.excel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderDownloadExcel {

    private String expressNumber;

    private String sawagaNumber;

    private String orderNumber;

    private BigDecimal itemWeight;

    private Integer itemQuantity;

    private BigDecimal itemPrice;

    private String receiverZipCode;

    private String receiverName;

    private String receiverAddress;

    private String receiverPhone;

    private String itemName;
}
