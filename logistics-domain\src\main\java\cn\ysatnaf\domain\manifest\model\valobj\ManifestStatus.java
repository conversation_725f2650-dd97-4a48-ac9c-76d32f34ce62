package cn.ysatnaf.domain.manifest.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ManifestStatus {

    /**
     * 0-全部
     * 1-待揽件
     * 2-已揽件
     * 3-已发货
     * 4-已送达
     */

    ALL(0, "全部"),
    PENDING_PICKUP(1, "待揽件"),
    PICKED_UP(2, "已揽件"),
    SHIPPED(3, "已发货"),
    DELIVERED(4, "已送达");

    private final Integer code;
    private final String message;
}
