package cn.ysatnaf.domain.auth.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "网页登录返回信息")
@Data
public class AuthWebLoginRes {

    @Schema(description = "登录凭证")
    private String token;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "角色: 1-管理员; 2-普通用户; 3-快递员")
    private Long roleId;

    @Schema(description = "性别: 1-男; 2-女")
    private Integer gender;
}
