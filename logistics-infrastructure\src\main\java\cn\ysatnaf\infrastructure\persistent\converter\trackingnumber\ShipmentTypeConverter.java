package cn.ysatnaf.infrastructure.persistent.converter.trackingnumber;

import cn.ysatnaf.domain.trackingnumber.model.entity.ShipmentType;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.ShipmentTypePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 货物类型对象转换器
 * <AUTHOR>
 */
@Mapper
public interface ShipmentTypeConverter {

    ShipmentTypeConverter INSTANCE = Mappers.getMapper(ShipmentTypeConverter.class);

    ShipmentTypePO toPO(ShipmentType shipmentType);

    ShipmentType toEntity(ShipmentTypePO shipmentTypePO);
    
    List<ShipmentType> toEntityList(List<ShipmentTypePO> shipmentTypePOs);
    
    List<ShipmentTypePO> toPOList(List<ShipmentType> shipmentTypes);

} 