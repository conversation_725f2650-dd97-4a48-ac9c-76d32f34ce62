package cn.ysatnaf.domain.manifest.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "物流统计返回参数")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManifestStatisticsRes {

    @Schema(description = "发货量")
    private Integer shipmentVolume;

    @Schema(description = "发货金额")
    private BigDecimal shipmentValue;
}
