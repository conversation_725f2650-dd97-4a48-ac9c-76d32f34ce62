package cn.ysatnaf.domain.manifest.model.req;

import cn.ysatnaf.types.common.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Hang
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询运单列表入参")
@Data
public class ManifestSearchReq extends PageParam {

    @Schema(description = "运单号")
    private String expressNumber;

    @Schema(description = "佐川单号")
    private String sawagaNumber;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "创建时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;
}
