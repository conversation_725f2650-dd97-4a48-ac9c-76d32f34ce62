package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.infrastructure.persistent.po.CostRulePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CostRuleDao extends BaseMapper<CostRulePO> {
    void insertBatchSomeColumn(@Param("list") List<CostRulePO> list);
}
