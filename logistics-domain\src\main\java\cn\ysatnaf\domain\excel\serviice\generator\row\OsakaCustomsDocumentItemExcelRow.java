package cn.ysatnaf.domain.excel.serviice.generator.row;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class OsakaCustomsDocumentItemExcelRow {
    private Integer no;
    /**
     * "ハウス番号
     * 分单号
     * (INVOICENo)
     * hawb"
     */
    private String hawb;
    private String itemNo;
    private String ctnNo;
    private String hsCode;
    private BigDecimal dutyRate;
    private String quantityUnitCode1;
    private String quantityUnitCode2;
    private String asinCode;

    // 商品名称（通关用）
    private String productNameCustoms;

    /**
     * 品名（英語のみ）
     * 商品名（仅限英语）
     * (インボイス用/生成发票用)
     * cmn_inv
     */
    private String productNameInvoice;

    // 商品名称（日语/中文）
    private String productNameLocal;

    private Integer quantity;
    private BigDecimal totalPrice;
    private BigDecimal netWeightPerUnit;
    private BigDecimal totalGrossWeight;
    private BigDecimal volume;
    private Integer cartonQuantity;
    private String originCountry = "CN";
    private String material = "plastic";
    private String productUrl;
    private String productImageUrl;
    private String serialNumber;

}
