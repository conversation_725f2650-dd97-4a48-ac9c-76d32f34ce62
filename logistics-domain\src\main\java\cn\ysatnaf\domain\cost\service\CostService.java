package cn.ysatnaf.domain.cost.service;

import cn.ysatnaf.domain.cost.model.entity.CostRuleEntity;
import cn.ysatnaf.domain.cost.model.req.CostCalculateReq;
import cn.ysatnaf.domain.cost.model.req.CostRuleUpdateReq;
import cn.ysatnaf.domain.cost.model.res.CostCalculateRes;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
public interface CostService {

    void update(CostRuleUpdateReq req);

    List<CostRuleEntity> list();

    CostCalculateRes calculate(CostCalculateReq req);
}
