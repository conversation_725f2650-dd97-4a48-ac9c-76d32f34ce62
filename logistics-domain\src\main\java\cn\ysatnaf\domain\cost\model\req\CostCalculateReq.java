package cn.ysatnaf.domain.cost.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "计算价格入参")
@Data
public class CostCalculateReq {

    @Schema(description = "计价平台类型 1-网页 2-公众号")
    @NotNull
    private Integer platformType;

    @Schema(description = "物品重量（KG）")
    @NotNull
    private BigDecimal weight;

    @Schema(description = "物品长度（厘米）")
    private BigDecimal length;

    @Schema(description = "物品宽度（厘米）")
    private BigDecimal width;

    @Schema(description = "物品高度（厘米）")
    private BigDecimal height;
}
