package cn.ysatnaf.domain.order.model.req;

import cn.ysatnaf.types.common.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * MpOrderPageReq
 *
 * <AUTHOR> <PERSON>
 * @date 2024/2/20 9:22
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分页获取订单 入参")
@Data
public class MpOrderPageReq extends PageParam {

    @Schema(description = "订单状态：不传为全部；1-待取件；2-待支付；-1-已取消")
    private Integer status;
}
