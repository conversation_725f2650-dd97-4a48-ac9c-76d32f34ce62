package cn.ysatnaf.domain.shippingfee.service.impl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.ysatnaf.domain.shippingfee.model.req.ShippingFeeCalculateReq;
import cn.ysatnaf.domain.shippingfee.model.res.ShippingFeeCalculateRes;
import cn.ysatnaf.domain.shippingfee.service.ShippingFeeService;
import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplatePO;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.domain.shippingfeetemplate.service.ShippingFeeTemplateService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

@AllArgsConstructor
@Service
public class ShippingFeeServiceImpl implements ShippingFeeService {

    private final ShippingFeeTemplateService shippingFeeTemplateService;

    @Override
    public ShippingFeeCalculateRes calculate(ShippingFeeCalculateReq req) {
        ShippingFeeTemplatePO shippingFeeTemplatePO = shippingFeeTemplateService.get(req.getTemplateId(), req.getType(), req.getUserId());
        if (shippingFeeTemplatePO == null) {
            throw new ServiceException("不存在的运费模板，请联系管理员进行设置");
        }
        BigDecimal price = doCalculate(req.getWeight(), shippingFeeTemplatePO);
        ShippingFeeCalculateRes result = ShippingFeeCalculateRes.builder().cost(price).build();
        // 判断是否计算体积重量
        // 如果是投函模板，不计算
        if (shippingFeeTemplatePO.getType().equals(ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode())) {
            return result;
        }
        // 如果长宽高不全，不计算
        if (!ObjUtil.isAllNotEmpty(req.getLength(), req.getWidth(), req.getHeight())) {
            return result;
        }
        // 如果三边和没有超过阈值，不计算
        if (req.getLength().add(req.getWidth()).add(req.getHeight()).compareTo(shippingFeeTemplatePO.getThreeSidesStart()) < 0) {
            return result;
        }
        // 如果轻抛系数为0或者为空，不计算
        if (shippingFeeTemplatePO.getBulkCoefficient() == null || shippingFeeTemplatePO.getBulkCoefficient().equals(0)) {
            return result;
        }

        // 计算体积重量：长 * 宽 * 高 / 轻抛系数
        BigDecimal volumetricWeight = req.getLength()
                .multiply(req.getWidth())
                .multiply(req.getHeight())
                .divide(BigDecimal.valueOf(shippingFeeTemplatePO.getBulkCoefficient()), 2, RoundingMode.HALF_UP);
        BigDecimal volumetricPrice = doCalculate(volumetricWeight, shippingFeeTemplatePO);
        result.setDimensionalWeight(volumetricWeight);
        result.setCost(NumberUtil.max(price, volumetricPrice));
        return result;
    }

    /**
     * 计算价格
     * @param weight 重量
     * @param shippingFeeTemplatePO 运费模板
     * @return 价格
     */
    private static BigDecimal doCalculate(BigDecimal weight, ShippingFeeTemplatePO shippingFeeTemplatePO) {
        BigDecimal price;
        // 获取重量，判断是否超过首重
        if (weight.compareTo(shippingFeeTemplatePO.getFirstWeightRange()) <= 0) {
            // 没有超过首重，按照首重价格计算
            price = shippingFeeTemplatePO.getFirstWeightPrice();
        } else {
            // 超过首重，计算超过首重多少，计算续重
            BigDecimal overFirstWeight = weight.subtract(shippingFeeTemplatePO.getFirstWeightRange());
            // 超过部分除以续重区间大小，得到的结果再乘以续重每区间多少元
            BigDecimal intervals = overFirstWeight.divide(shippingFeeTemplatePO.getContinuedWeightInterval(), 0, RoundingMode.CEILING);
            price = shippingFeeTemplatePO.getFirstWeightPrice().add(intervals.multiply(shippingFeeTemplatePO.getContinuedWeightPrice()));
        }
        return price;
    }
}
