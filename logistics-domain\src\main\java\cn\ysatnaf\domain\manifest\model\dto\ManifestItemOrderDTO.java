package cn.ysatnaf.domain.manifest.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/1/31
 */
@Schema(description = "订单中的物品信息 DTO (包含违禁和映射信息)")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManifestItemOrderDTO {
    /**
     * 原始物品名称
     */
    @Schema(description = "原始物品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "原始物品名称不能为空")
    private String name;

    /**
     * 数量
     */
    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    /**
     * 重量
     */
    @Schema(description = "重量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "重量不能为空")
    private BigDecimal weight;

    /**
     * 单价
     */
    @Schema(description = "单价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单价不能为空")
    private BigDecimal price;

    /**
     * 是否包含违禁词
     */
    @Schema(description = "是否包含违禁词", defaultValue = "false")
    private Boolean isProhibited = false;

    /**
     * 映射后的合规名称 (如果存在且启用)
     */
    @Schema(description = "映射后的合规名称 (如果存在且启用)")
    private String mappedName;

    /**
     * 检测到的违禁关键词 (第一个)
     */
    @Schema(description = "检测到的违禁关键词 (第一个)")
    private String detectedKeyword;
}
