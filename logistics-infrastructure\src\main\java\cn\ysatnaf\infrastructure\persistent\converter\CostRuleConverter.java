package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.cost.model.entity.CostRuleEntity;
import cn.ysatnaf.infrastructure.persistent.po.CostRulePO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Mapper
public interface CostRuleConverter {
    CostRuleConverter INSTANCE = Mappers.getMapper(CostRuleConverter.class);

    CostRulePO toPO(CostRuleEntity entity);
    List<CostRulePO> toPOList(List<CostRuleEntity> costRuleEntities);

    CostRuleEntity toEntity(CostRulePO costRulePO);

    List<CostRuleEntity> toEntityList(List<CostRulePO> costRulePOList);

}
