package cn.ysatnaf.infrastructure.persistent.po.trackingnumber;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 承运商持久化对象
 * <AUTHOR>
 */
@Data
@TableName("carriers")
public class CarrierPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 承运商代码 (例如: SAGAWA, YAMATO, JP_POST)
     */
    private String code;

    /**
     * 承运商名称 (例如: 佐川急便, 黑猫宅急便, 日本邮政)
     */
    private String name;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 