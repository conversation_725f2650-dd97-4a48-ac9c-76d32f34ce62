package cn.ysatnaf.domain.manifest.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> Hang
 * 物流面单生成入参
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WayBillImageGenDTO {

    /**
     * 佐川单号
     */
    private String sawagaNumber;

    /**
     * 系统订单号
     */
    private String orderNo;

    /**
     * 物品名称
     */
    private String itemNames;

    /**
     * 数量
     */
    private Integer itemQuantity;

    private Integer shippingFeeTemplateType;

    /**
     * 物品英文名
     */
    private String itemEnNames;

    private String packageNo;

    /**
     * 收件人名字
     */
    private String receiverName;

    /**
     * 收件人电话
     */
    private String receiverPhone;

    /**
     * 收件人邮编
     */
    private String receiverZipCode;

    /**
     * 收件人地址
     */
    private String receiverAddress;

    /**
     * 收件人英文地址
     */
    private String receiverEnAddress;

    /**
     * 收件人邮编，格式化
     */
    public String getFormattedZipCode() {
        return receiverZipCode.substring(0, 3) + "-" + receiverZipCode.substring(3);
    }
}
