package cn.ysatnaf.domain.parcelsorting.repository;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingPackagePO;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
public interface ParcelSortingPackageRepository {
    ParcelSortingPackagePO getByExpressNumber(String expressNumber);

    ParcelSortingPackagePO getByOrderNo(String orderNo);

    void create(ParcelSortingPackagePO parcelSortingPackagePO);

    ParcelSortingPackagePO getByBoxIdAndExpressNumber(Long boxId, String expressNumber);

    ParcelSortingPackagePO getByBoxIdAndOrderNo(Long boxId, String orderNo);

    void deleteById(Long id);

    void updateByIds(List<Long> ids, Long boxId);

    List<ParcelSortingPackagePO> listByBoxId(Long boxId);

    void deleteByBoxId(Long boxId);

    List<ParcelSortingPackagePO> listByBoxIds(List<Long> boxIds);

    List<ParcelSortingPackagePO> listByExpressNumbers(Collection<String> expressNumbers);

    ParcelSortingPackagePO getByManifestId(Long id);

    /**
     * 根据 Manifest ID 列表查询包裹信息
     * @param manifestIds Manifest ID 列表
     * @return 包裹列表
     */
    List<ParcelSortingPackagePO> listByManifestIds(List<Long> manifestIds);

    /**
     * 批量创建包裹信息
     * @param packages 需要创建的包裹列表
     * @return 成功插入的记录数
     */
    int batchCreate(List<ParcelSortingPackagePO> packages);

    /**
     * 根据箱子ID列表统计包裹数量
     * @param boxIds 箱子ID列表
     * @return 包裹总数
     */
    Integer countByBoxIds(List<Long> boxIds);

    ParcelSortingPackagePO getById(Long boxId);

    /**
     * 根据装箱记录ID查询所有关联的包裹（不论是否已分配箱子）
     * @param recordId 装箱记录ID
     * @return 包裹列表
     */
    List<ParcelSortingPackagePO> listByRecordId(Long recordId);
}
