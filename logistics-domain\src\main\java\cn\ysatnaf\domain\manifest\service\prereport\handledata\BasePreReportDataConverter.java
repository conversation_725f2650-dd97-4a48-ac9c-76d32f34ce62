package cn.ysatnaf.domain.manifest.service.prereport.handledata;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;
import cn.ysatnaf.domain.address.service.ReceiverAreaService;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.domain.manifest.model.aggregate.ManifestAggregate;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import cn.ysatnaf.domain.manifest.model.entity.OrderNo;
import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import cn.ysatnaf.domain.manifest.model.valobj.ManifestStatus;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportContext;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

public abstract class BasePreReportDataConverter implements ManifestPreReportDataConverter{
    
    protected ManifestPreReportContext context;
    
    protected ReceiverAreaService receiverAreaService;
    
    @Override
    public List<ManifestAggregate> convert(List<ManifestPreReportRow> rows) {
        List<ManifestAggregate> result = new ArrayList<>();

        // 将预录入数据按照物流单号分组
        Map<String, List<ManifestPreReportRow>> expressNumber2RowsMap = rows
                .stream()
                .collect(Collectors.groupingBy(ManifestPreReportRow::getExpressNumber));

        // 遍历分组结果，生成聚合对象
        expressNumber2RowsMap.forEach((expressNumber, groupRows) -> {
            // 创建基础的运单对象
            Manifest manifest = buildBasicManifest(expressNumber, groupRows);

            // 设置运单国际单号以及来源类型
            setInternationalExpressNumber(manifest, expressNumber);
            setSourceType(manifest);

            // 统计物品信息，创建物品列表
            List<ManifestItem> manifestItems = buildManifestItems(groupRows);

            // 组装运单聚合对象
            ManifestAggregate manifestAggregate = ManifestAggregate.builder()
                    .manifest(manifest)
                    .manifestItems(manifestItems).build();
            result.add(manifestAggregate);
        });
        return result;
    }

    /**
     * 统计物品信息，创建物品列表
     * @param groupRows 数据
     * @return 物品列表
     */
    private List<ManifestItem> buildManifestItems(List<ManifestPreReportRow> groupRows) {
        // 叠加相同商品的数量价格和重量
        Map<String, Integer> itemName2Quantity = new HashMap<>();
        Map<String, BigDecimal> itemName2Price = new HashMap<>();
        Map<String, BigDecimal> itemName2Weight = new HashMap<>();

        for (ManifestPreReportRow row : groupRows) {
            String itemName = row.getItemName();
            itemName2Quantity.merge(itemName, row.getQuantity(), Integer::sum);
            itemName2Price.merge(itemName, row.getPrice(), (a, b) -> b.add(a));
            itemName2Weight.merge(itemName, row.getWeight(), (a, b) -> b.add(a));
        }
        // 去重记录货物名
        Set<String> uniqueItemName = new HashSet<>();
        List<ManifestItem> manifestItems = new ArrayList<>();
        groupRows.forEach(row -> {
            if (uniqueItemName.contains(row.getItemName())) {
                return;
            }
            manifestItems.add(ManifestItem.builder()
                    .name(row.getItemName())
//                    .nameEn(TranslationUtil.translateFromChineseToEnglish(row.getItemName()))
                    .quantity(itemName2Quantity.get(row.getItemName()))
                    .weight(itemName2Weight.get(row.getItemName()))
                    .price(itemName2Price.get(row.getItemName())).build());
            uniqueItemName.add(row.getItemName());
        });
        return manifestItems;
    }

    /**
     * 创建基础运单对象
     * @param expressNumber 运单号
     * @param groupRows 数据
     * @return 运单对象
     */
    private Manifest buildBasicManifest(String expressNumber, List<ManifestPreReportRow> groupRows) {
        // 获取第一条数据，作为基础数据
        ManifestPreReportRow firstRow = groupRows.get(0);

        // 设置偏远费
        List<ReceiverAreaEntity> areas = receiverAreaService.getByZipCode(firstRow.getReceiverZipCode().replace("-", ""));
        BigDecimal remoteAreaSurcharge = null;
        if (CollUtil.isNotEmpty(areas) && areas.get(0).getPrefectureName().equals("沖縄県")) {
            remoteAreaSurcharge = BigDecimal.valueOf(100);
        }

        // todo 是否所有类型的订单都要直接设置偏远费?
        // 生成系统订单号
        OrderNo orderNo = new OrderNo(expressNumber);

        // 获取当前操作用户
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();

        // 创建运单对象
        return Manifest.builder()
                .expressNumber(firstRow.getExpressNumber())
                .orderNumber(firstRow.getOrderNumber().toUpperCase())
                .orderNo(orderNo.getOrderNo())
                .receiverName(firstRow.getReceiverName())
                .receiverAddress(firstRow.getReceiverAddress())
                .receiverPhone(firstRow.getReceiverPhone())
                .receiverZipCode(firstRow.getReceiverZipCode())
                .userId(context.getUser().getId())
                .status(ManifestStatus.PENDING_PICKUP.getCode())
                .remoteAreaSurcharge(remoteAreaSurcharge)
                .isOnline(false)
                .isDelete(false)
                .createTime(LocalDateTime.now())
                .creatorId(loginUser.getId()).build();
    }

    /**
     * 设置来源类型
     */
    protected abstract void setSourceType(Manifest manifest);

    /**
     * 设置国际单号
     */
    protected abstract void setInternationalExpressNumber(Manifest manifest, String expressNumber);
}
