package cn.ysatnaf.trigger.http.controller.web;

import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.user.model.req.*;
import cn.ysatnaf.domain.user.model.res.UserInfoRes;
import cn.ysatnaf.domain.user.model.res.UserInfoSimpleRes;
import cn.ysatnaf.domain.user.model.valobj.RoleVO;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.exception.ServiceException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;

/**
 * <AUTHOR> Hang
 */
@Tag(name = "用户管理")
@RequestMapping("/web/user")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @PostMapping("/add")
    @PermitAll
    @Operation(summary = "新增用户")
    public CommonResult<Boolean> add(@RequestBody @Validated UserAddReq req) {
        if (req.getRoleId().equals(RoleVO.COURIER.getCode()) && StrUtil.isBlank(req.getMobileNumber())) {
            throw new ServiceException("创建快递员时手机号不能为空");
        }
        userService.add(req);
        return CommonResult.success(true);
    }

    @PostMapping("/resetPassword")
    @PermitAll
    @Operation(summary = "重置密码")
    public CommonResult<Boolean> resetPassword(@RequestBody @Validated ResetPasswordReq req) {
        userService.resetPassword(req);
        return CommonResult.success(true);
    }

    @PostMapping("/update")
    @PermitAll
    @Operation(summary = "更新用户")
    public CommonResult<Boolean> update(@RequestBody @Validated UserUpdateReq req) {
        userService.update(req);
        return CommonResult.success(true);
    }

    @PostMapping("/remark")
    @Operation(summary = "备注")
    public CommonResult<Boolean> remark(@RequestBody @Validated UserRemarkReq req) {
        userService.remark(req);
        return CommonResult.success(true);
    }

    @PostMapping("/edit")
    @PermitAll
    @Operation(summary = "修改个人信息")
    public CommonResult<Boolean> edit(@RequestBody @Validated UserEditReq req) {
        userService.edit(req);
        return CommonResult.success(true);
    }

    @PostMapping("/page")
    @PermitAll
    @Operation(summary = "用户列表")
    public CommonResult<PageResult<UserInfoRes>> page(@RequestBody @Validated UserPageReq req) {
        return CommonResult.success(userService.page(req));
    }

    @PostMapping("/pageUserSimple")
    @PermitAll
    @Operation(summary = "用户列表（简略信息）")
    public CommonResult<PageResult<UserInfoSimpleRes>> pageUserSimple(@RequestBody @Validated UserPageReq req) {
        return CommonResult.success(userService.pageUserSimple(req));
    }

    @PostMapping("/get")
    @PermitAll
    @Operation(summary = "获取用户详情")
    public CommonResult<UserInfoRes> get(@RequestBody @Validated UserGetReq req) {
        return CommonResult.success(userService.get(req.getId()));
    }

    @PostMapping("/delete")
    @PermitAll
    @Operation(summary = "删除用户")
    public CommonResult<Boolean> delete(@RequestBody @Validated UserDeleteReq req) {
        userService.delete(req.getId(), req.getOwnPassword());
        return CommonResult.success(true);
    }

    @PostMapping("/updatePassword")
    @PermitAll
    @Operation(summary = "修改密码")
    public CommonResult<Boolean> updatePassword(@RequestBody @Validated UserPasswordUpdateReq req) {
        return CommonResult.success(userService.updatePassword(req));
    }
}
