package cn.ysatnaf.domain.manifest.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationFormExcelDatumV3 {

    @ExcelProperty("大包裹号")
    private String packageNumber;

    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("运单号")
    private String expressNumber;

    @ExcelProperty("商品名称")
    private String goodsDescription;

    @ExcelProperty("商品数量")
    private Integer quantity;

    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    @ExcelProperty("总价")
    private BigDecimal totalPrice;

    @ExcelProperty("币制")
    private String currencySystem = "CNY";

    @ExcelProperty("总毛重")
    private BigDecimal weight;

    @ExcelProperty("成交单位")
    private String transactionUnit = "件";

}
