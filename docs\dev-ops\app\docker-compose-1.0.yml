# /usr/local/bin/docker-compose -f /docs/dev-ops/environment/environment-docker-compose-2.4.yml up -d
# 命令执行 docker-compose up -d
version: '3.9'
services:
  zebra-express-hub-api-app:
    image: system/zebra-express-hub:1.0-SNAPSHOT
    container_name: zebraExpressHub
    restart: on-failure
    networks:
      - my-network
    ports:
      - "8091:8091"
    environment:
      - TZ=PRC
      - SERVER_PORT=8091
      - SPRING_PROFILES_ACTIVE=test
    volumes:
      - ./log:/data/log
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  mysql:
    image: mysql:8.0.32
    container_name: mysql
    hostname: mysql
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    environment:
      TZ: Asia/Shanghai
      #      MYSQL_ALLOW_EMPTY_PASSWORD: 'yes' # 可配置无密码，注意配置 SPRING_DATASOURCE_PASSWORD=
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_USER: ch
      MYSQL_PASSWORD: 123456
    networks:
      - my-network
    depends_on:
      - mysql-job-dbdata
    ports:
      - "3336:3306"
    volumes:
      - ./sql:/docker-entrypoint-initdb.d
    volumes_from:
      - mysql-job-dbdata

  # 自动加载数据
  mysql-job-dbdata:
    image: alpine:3.18.2
    container_name: mysql-job-dbdata
    volumes:
      - /var/lib/mysql

  redis:
    image: redis:7.2.0
    container_name: redis
    restart: always
    hostname: redis
    ports:
      - 6379:6379
    volumes:
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - my-network
    healthcheck:
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 3

  networks:
    my-network:
      driver: bridge