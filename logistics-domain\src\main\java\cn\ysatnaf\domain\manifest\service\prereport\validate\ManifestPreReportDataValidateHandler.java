package cn.ysatnaf.domain.manifest.service.prereport.validate;

import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import cn.ysatnaf.domain.manifest.service.ManifestDataValidateService;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 预报单数据校验处理器
 */
@RequiredArgsConstructor
@Component
public class ManifestPreReportDataValidateHandler {

    private final ManifestDataValidateService manifestDataValidateService;

    /**
     * 处理数据校验流程
     * 1. 拿到已经在库的订单号，用于后续判断是否重复
     */
    public void handle(ManifestPreReportContext context) {
        List<ManifestPreReportRow> data = context.getOriginalData();

        for (int i = 0; i < data.size(); i++) {
            // 创建校验器
            Integer rowNumber = i + 1;
            ManifestPreReportRow row = data.get(i);
            ManifestPreReportDataValidator validator = new ManifestPreReportDataValidator(row, rowNumber, manifestDataValidateService);

            // 校验数据
            validator.validate(context);
            // 校验是否通过且不重复，如果校验通过，加入到非重复数据中，校验不通过，记录错误信息
            if (validator.isValid()) {
                if (!context.getPreReportedExpressNumbers().contains(row.getExpressNumber())) {
                    context.recordNotPreReportedData(row);
                }
            } else {
                context.getErrorMsgManager().addErrorMsg(validator.getErrorMsgBuilder());
            }
        }
    }
}
