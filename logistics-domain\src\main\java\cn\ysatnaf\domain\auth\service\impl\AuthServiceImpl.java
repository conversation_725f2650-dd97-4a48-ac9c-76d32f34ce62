package cn.ysatnaf.domain.auth.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.model.valobj.CommonStatusVO;
import cn.ysatnaf.domain.user.model.valobj.SocialTypeVO;
import cn.ysatnaf.domain.auth.service.AuthService;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.common.ErrorCode;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * AuthServiceImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/21 18:38
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl implements AuthService {

    private final WxMpService wxMpService;

    private final UserService userService;

    private final StringRedisTemplate stringredisTemplate;

    @Override
    public String wechatMpLogin(String code) {
        WxOAuth2AccessToken accessToken;
        WxOAuth2UserInfo userInfo;
        try {
            accessToken = wxMpService.getOAuth2Service().getAccessToken(code);
            userInfo = wxMpService.getOAuth2Service().getUserInfo(accessToken, null);
            UserEntity userEntity = userService.findByOpenid(userInfo.getOpenid());
            if (userEntity == null) {
                UserEntity newUser = new UserEntity();
                newUser.setNickname(userInfo.getNickname());
                newUser.setOpenid(userInfo.getOpenid());
                newUser.setSocialType(SocialTypeVO.WECHAT_MP.getType());
                newUser.setCity(userInfo.getCity());
                newUser.setProvince(userInfo.getProvince());
                newUser.setCountry(userInfo.getCountry());
                newUser.setAvatar(userInfo.getHeadImgUrl());
                newUser.setGender(userInfo.getSex());
                newUser.setStatus(CommonStatusVO.ENABLE.getStatus());
                userService.add(newUser);
            }
            // 生成token，保存用户信息到redis中
            String token = IdUtil.randomUUID();
            stringredisTemplate.opsForValue().set(token, JSON.toJSONString(userEntity));
            return token;
        } catch (WxErrorException e) {
            log.error("微信公众号获取用户信息错误：", e);
            throw new ServiceException(new ErrorCode(9999, "服务器异常"));
        }
    }
}
