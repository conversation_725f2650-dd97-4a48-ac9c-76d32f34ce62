package cn.ysatnaf.domain.parcelsorting.repository;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingBoxPO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Hang
 */
public interface ParcelSortingDetailRepository {
    List<ParcelSortingBoxPO> listByRecordId(Long recordId);

    void create(ParcelSortingBoxPO parcelSortingBoxPO);

    ParcelSortingBoxPO getById(Long id);

    void updateById(ParcelSortingBoxPO detail);

    void delete(Long id);

    Integer countById(Long boxId);

    List<ParcelSortingBoxPO> listByIds(Set<Long> boxIds);
}
