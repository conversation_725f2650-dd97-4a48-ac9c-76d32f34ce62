package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.parcelsorting.model.entity.ParcelSortingRecord;
import cn.ysatnaf.domain.parcelsorting.repository.ParcelSortingRecordRepository;
import cn.ysatnaf.infrastructure.persistent.converter.ParcelSortingRecordConverter;
import cn.ysatnaf.infrastructure.persistent.dao.ParcelSortingRecordDao;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingRecordPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR> Hang
 */
@RequiredArgsConstructor
@Repository
public class ParcelSortingRecordRepositoryImpl implements ParcelSortingRecordRepository {

    private final ParcelSortingRecordDao parcelSortingRecordDao;
    private final ParcelSortingRecordConverter parcelSortingRecordConverter;

    @Override
    public Integer countByRecordName(String recordName) {
        LambdaQueryWrapper<ParcelSortingRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingRecordPO::getRecordName, recordName);
        wrapper.eq(ParcelSortingRecordPO::getIsDelete, false);
        return Math.toIntExact(parcelSortingRecordDao.selectCount(wrapper));
    }

    @Override
    public void createRecord(String recordName, Long masterBillId) {
        parcelSortingRecordDao.insert(ParcelSortingRecordPO.builder()
                .recordName(recordName)
                .masterBillId(masterBillId)
                .isDelete(false).build());
    }

    @Override
    public void delete(Long id) {
        parcelSortingRecordDao.updateById(ParcelSortingRecordPO.builder()
                .id(id)
                .isDelete(true)
                .build());
    }

    @Override
    public ParcelSortingRecordPO getByRecordName(String recordName) {
        LambdaQueryWrapper<ParcelSortingRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingRecordPO::getRecordName, recordName);
        wrapper.eq(ParcelSortingRecordPO::getIsDelete, false);
        return parcelSortingRecordDao.selectOne(wrapper);
    }

    @Override
    public void update(Long id, String recordName, Long masterBillId) {
        parcelSortingRecordDao.updateById(ParcelSortingRecordPO.builder()
                .id(id)
                .recordName(recordName)
                .masterBillId(masterBillId)
                .build());
    }

    @Override
    public PageResult<ParcelSortingRecordPO> list(Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<ParcelSortingRecordPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ParcelSortingRecordPO::getIsDelete, false);
        wrapper.orderByDesc(ParcelSortingRecordPO::getCreateTime);
        Page<ParcelSortingRecordPO> page = parcelSortingRecordDao.selectPage(new Page<>(pageNo, pageSize), wrapper);
        if (CollUtil.isEmpty(page.getRecords())) {
            return new PageResult<>(page.getTotal());
        }
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public void save(ParcelSortingRecord record) {
        ParcelSortingRecordPO parcelSortingRecordPO = parcelSortingRecordConverter.toPo(record);
        parcelSortingRecordDao.insert(parcelSortingRecordPO);
    }

    @Override
    public ParcelSortingRecordPO getById(Long id) {
        return parcelSortingRecordDao.selectById(id);
    }
}
