package cn.ysatnaf.domain.auth.adatper;

import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.model.res.UserInfoRes;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Mapper
public interface UserAdapter {
    UserAdapter INSTANCE = Mappers.getMapper(UserAdapter.class);


    LoginUserEntity entity2loginUser(UserEntity userEntity);

    UserInfoRes entity2userInfoRes(UserEntity userEntity);

    List<UserInfoRes> entityList2userInfoResList(List<UserEntity> list);
}
