package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.auth.repository.UserRepository;
import cn.ysatnaf.infrastructure.persistent.converter.UserConverter;
import cn.ysatnaf.infrastructure.persistent.dao.UserDao;
import cn.ysatnaf.infrastructure.persistent.po.UserPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * UserRepositoryImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/21 19:19
 */
@Repository
@RequiredArgsConstructor
public class UserRepositoryImpl implements UserRepository {

    private final UserDao userDao;


    @Override
    public UserEntity findByOpenId(String openid) {
        LambdaQueryWrapper<UserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPO::getOpenid, openid);
        return UserConverter.INSTANCE.po2entity(userDao.selectOne(wrapper));
    }

    @Override
    public UserEntity insert(UserEntity newUser) {
        UserPO userPO = UserConverter.INSTANCE.entity2po(newUser);
        userDao.insert(userPO);
        return UserConverter.INSTANCE.po2entity(userPO);
    }

    @Override
    public UserEntity getByUsername(String username) {
        LambdaQueryWrapper<UserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPO::getUsername, username);
        return UserConverter.INSTANCE.po2entity(userDao.selectOne(wrapper));
    }

    @Override
    public void updateById(UserEntity userEntity) {
        userDao.updateById(UserConverter.INSTANCE.entity2po(userEntity));
    }

    @Override
    public PageResult<UserEntity> page(Integer pageNo, Integer pageSize) {
        Page<UserPO> userPOPage = userDao.selectPage(new Page<>(pageNo, pageSize), null);
        if (CollUtil.isEmpty(userPOPage.getRecords())) {
            return PageResult.empty(userPOPage.getTotal());
        }
        List<UserEntity> userEntityList = UserConverter.INSTANCE.poList2entityList(userPOPage.getRecords());
        return new PageResult<>(userEntityList, userPOPage.getTotal());
    }

    @Override
    public UserEntity getById(Long id) {
        return UserConverter.INSTANCE.po2entity(userDao.selectById(id));
    }

    @Override
    public void deleteById(Long id) {
        userDao.deleteById(id);
    }

    @Override
    public List<UserEntity> listByIds(Set<Long> creatorIds) {
        return UserConverter.INSTANCE.poList2entityList(userDao.selectBatchIds(creatorIds));
    }

    @Override
    public UserEntity getByOpenid(String openid) {
        LambdaQueryWrapper<UserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPO::getOpenid, openid);
        return UserConverter.INSTANCE.po2entity(userDao.selectOne(wrapper));
    }

    @Override
    public UserEntity getByMobileNumber(String mobileNumber) {
        LambdaQueryWrapper<UserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserPO::getPhone, mobileNumber);
        return UserConverter.INSTANCE.po2entity(userDao.selectOne(wrapper));
    }
}
