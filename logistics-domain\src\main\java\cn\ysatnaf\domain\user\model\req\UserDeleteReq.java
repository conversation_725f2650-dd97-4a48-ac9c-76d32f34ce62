package cn.ysatnaf.domain.user.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "删除用户入参")
@Data
public class UserDeleteReq {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用户ID不能为空")
    private Long id;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String ownPassword;
}
