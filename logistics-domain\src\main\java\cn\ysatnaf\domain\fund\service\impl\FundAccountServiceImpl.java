package cn.ysatnaf.domain.fund.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.domain.fund.adapter.FundAccountAdapter;
import cn.ysatnaf.domain.fund.model.dto.ModifyBalanceDTO;
import cn.ysatnaf.domain.fund.model.entity.FundAccountEntity;
import cn.ysatnaf.domain.fund.model.req.FundAccountGetReq;
import cn.ysatnaf.domain.fund.model.req.FundAccountRechargeReq;
import cn.ysatnaf.domain.fund.model.res.FundAccountGetRes;
import cn.ysatnaf.domain.fund.repository.FundAccountRepository;
import cn.ysatnaf.domain.fund.service.BalanceDetailService;
import cn.ysatnaf.domain.fund.service.FundAccountService;
import cn.ysatnaf.domain.log.model.entity.OperationLogEntity;
import cn.ysatnaf.domain.log.model.valobj.OperationType;
import cn.ysatnaf.domain.log.service.OperationLogService;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class FundAccountServiceImpl implements FundAccountService {

    private final FundAccountRepository fundAccountRepository;

    private final BalanceDetailService balanceDetailService;

    private final UserService userService;

    private final OperationLogService operationLogService;

    @Override
    public FundAccountGetRes get(FundAccountGetReq req) {
        return getByUserId(req.getUserId());
    }

    @Override
    public FundAccountGetRes getByUserId(Long userId) {
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        if (!(loginUser.ifSuperAdmin() && ObjUtil.isNotNull(userId))) {
            userId = loginUser.getId();
        }
        FundAccountEntity fundAccountEntity = fundAccountRepository.getByUserId(userId);
        return FundAccountAdapter.INSTANCE.toRes(fundAccountEntity);
    }

    @Override
    public void init(Long userId) {
        FundAccountEntity fundAccountEntity = new FundAccountEntity();
        fundAccountEntity.setUserId(userId);
        fundAccountEntity.setBalance(BigDecimal.ZERO);
        fundAccountRepository.insert(fundAccountEntity);
    }

    @Override
    public void deleteByUserId(Long userId) {
        fundAccountRepository.deleteByUserId(userId);
    }

    @Override
    @Transactional
    public Boolean cost(Long userId, BigDecimal cost, Long manifestId) {
        FundAccountEntity fundAccount = fundAccountRepository.getByUserId(userId);
        if (fundAccount.getBalance().compareTo(cost) < 0) {
            return false;
        } else {
            balanceDetailService.record(fundAccount.getId(), fundAccount.getBalance(), fundAccount.getBalance().subtract(cost), "运费", manifestId);
            // 记录明细
            fundAccount.setBalance(fundAccount.getBalance().subtract(cost));
            fundAccountRepository.updateById(fundAccount);
            return true;
        }
    }

    @Override
    @Transactional
    public Boolean change(ModifyBalanceDTO modifyBalanceDTO) {
        FundAccountEntity fundAccount = fundAccountRepository.getByUserId(modifyBalanceDTO.getUserId());

        balanceDetailService.record(fundAccount.getId(),
                fundAccount.getBalance(),
                fundAccount.getBalance().add(modifyBalanceDTO.getChangeBalance()),
                modifyBalanceDTO.getReason(),
                modifyBalanceDTO.getManifestId());
        // 记录明细
        fundAccount.setBalance(fundAccount.getBalance().add(modifyBalanceDTO.getChangeBalance()));
        fundAccountRepository.updateById(fundAccount);
        return true;
    }

    @Override
    @Transactional
    public Boolean recharge(FundAccountRechargeReq req) {
        userService.validateSuperAdmin();
        Long id = LoginUserHolder.getLoginUser().getId();
        UserEntity loginUser = userService.getById(id);
        if (!loginUser.validatePassword(req.getOwnPassword())) {
            throw new ServiceException("密码错误");
        }
        FundAccountEntity fundAccountEntity = fundAccountRepository.getById(req.getAccountId());
        BigDecimal balance = Optional.ofNullable(fundAccountEntity.getBalance()).orElse(BigDecimal.ZERO);
        BigDecimal newBalance = balance.add(req.getAmount());
        fundAccountEntity.setBalance(newBalance);

        // 记录操作日志
        // 记录日志
        OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                .operatorId(id)
                .userId(fundAccountEntity.getUserId())
                .operationType(OperationType.ACCOUNT_RECHARGE.getValue())
                .operation(OperationType.ACCOUNT_RECHARGE.getDescription())
                .originalInfo(balance.toString())
                .newInfo(newBalance.toString())
                .build();

        operationLogService.log(operationLogEntity);
        // 记录明细
        balanceDetailService.record(fundAccountEntity.getId(), balance, newBalance, "充值");
        // 更新账户信息
        fundAccountRepository.updateById(fundAccountEntity);
        return true;
    }

    @Override
    public List<FundAccountEntity> listByUserIds(List<Long> userIds) {
        return fundAccountRepository.listByUserIds(userIds);
    }
}
