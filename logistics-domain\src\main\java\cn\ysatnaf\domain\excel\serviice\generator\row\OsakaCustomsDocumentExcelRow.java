package cn.ysatnaf.domain.excel.serviice.generator.row;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class OsakaCustomsDocumentExcelRow {

    /**
     * 行号
     */
    private Integer no;

    /**
     * マスタ番号主单号materB/L
     */
    private String materBL;

    /**
     * ハウス番号分单号houseB/L
     */
    private String houseBL;

    /**
     * 取引区分法人向け(B2B)：1法人(B2B)：1個人向け(B2C)：2个人(B2C)：2
     * 固定填2
     */
    private Integer transactionType = 2;

    /**
     * 配送会社派送公司delivery_comp
     * 佐川填S8，投函填S9
     */
    private String deliveryComp;

    /**
     * 送り状番号运单号tracking_no
     * 佐川填单号，邮政不用填
     */
    private String trackingNo;

    /**
     * 法人番号（あれば輸出入者符号）法人号码（优先填写进口商符号）_imc
        空着
     */
    private String imc;

    /**
     * 輸入者郵便番号进口商邮编号码_imy
     */
    private String imyZipCode;

    /**
     * 輸入者名进口商名字imn_jp
     * 收件人日文名
     */
    private String imnJp;

    /**
     * 輸入者进口商名字_imn
     * 收件人英文名
     */
    private String imn;

    /**
     * 輸入者住所进口商地址iad_jp
     * 收件人地址
     */
    private String iadJp;

    /**
     * 輸入者住所进口商地址_iad
     * 收件人地址英文
     */
    private String iadEn;

    /**
     * 輸入者電話番号进口商电话号码_imt
     * 收件人电话
     */
    private String imtPhone;

    /**
     * 仕出人名发件方名字_epn
     * 发件人日文名
     */
    private String epn = "Fuzhou Sufeng Information Technology Co., Ltd";

    /**
     * 仕出人住所发件方地址_ead
     * 发件人地址英文
     */
    private String ead = "1902, 19th Floor, Building B, Netcom Smart Center, No. 11 Keji East Road, Shangjie Town, Minhou County, Fuzhou City";

    /**
     * 仕出人電話番号发件方电话号码_stl
     * 发件人电话 固定 8613055591234
     */
    private String stlPhone = "8613055591234";

    /**
     * 貨物重量（グロス）货物总重量_gw
     */
    private BigDecimal grossWeight;

    // Weight unit (e.g. KGM)
    private String grossWeightUnit = "KGM";

    /**
     * Invoice price type code:
     * D/CIF etc.
     */
    private String invoicePriceTypeCode = "D";

    /**
     * Trade terms code:
     * CIF/FOB etc.
     */
    private String tradeTermsCode = "CIF";

    // Currency code (e.g. JPY)
    private String currencyCode = "JPY";

    // Origin country code (e.g. CN)
    private String originCountryCode = "CN";

    // Quantity of goods
    private Integer goodsQuantity = 1;

    // Quantity unit (e.g. CT)
    private String goodsQuantityUnit = "CT";

    // Volume 空着
    private BigDecimal totalVolume;

    // Product unit 空着
    private String goodsUnit;

    // All charges 空着
    private BigDecimal allCharges;

    /**
     * Payment account type:
     * S8/S9
     * 空着
     */
    private String paymentAccountType;

    // Packing number 空着
    private String packingNo;
}
