CREATE TABLE problem_manifest_tickets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '工单主键ID',
    manifest_id BIGINT NOT NULL COMMENT '关联的运单ID (FK -> tb_manifest.id)',
    tracking_number VARCHAR(64) NULL COMMENT '关联运单的物流单号 (冗余)',
    customer_account_id BIGINT NULL COMMENT '关联运单所属的客户ID (冗余, FK -> customer_accounts.id)',
    problem_type_code VARCHAR(32) NOT NULL COMMENT '问题类型代码 (发生问题时的类型)',
    problem_description TEXT NULL COMMENT '系统检测到的或首次记录的问题描述',
    status VARCHAR(32) NOT NULL DEFAULT 'PENDING_ACTION' COMMENT '当前工单处理状态 (例如: PENDING_ACTION, IN_PROGRESS, RESOLVED, CLOSED_UNRESOLVED)',
    priority TINYINT NULL COMMENT '问题优先级 (可选, 例如 1-高, 2-中, 3-低)',
    assigned_to_user_id BIGINT NULL COMMENT '处理人ID (FK -> internal_users.id, 可选)',
    remarks TEXT NULL COMMENT '处理备注',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '工单创建时间 (问题首次记录时间)',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '工单最后更新时间',
    resolved_time DATETIME NULL COMMENT '解决时间',

    -- 索引
    INDEX idx_ticket_manifest_id (manifest_id),
    INDEX idx_ticket_tracking_number (tracking_number),
    INDEX idx_ticket_customer_id (customer_account_id),
    INDEX idx_ticket_status (status),
    INDEX idx_ticket_assigned_user (assigned_to_user_id),
    INDEX idx_ticket_problem_type (problem_type_code),
    INDEX idx_ticket_created_at (create_time)

) COMMENT = '问题运单处理工单表' COLLATE = utf8mb4_general_ci; 