package cn.ysatnaf.domain.manifest.model.res;

import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "确认预报返回参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ManifestConfirmPreReportRes {

    @Schema(description = "数量")
    private Integer count;
}
