package cn.ysatnaf.domain.manifest.service.prereport.storage;

import cn.hutool.core.date.DateUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Date;

/**
 * 预报单文件存储
 */
@Slf4j
@Component
public class ManifestPreReportFileStorage implements FileStorage{

    private static final String PRE_REPORT_MANIFEST_FILE_STORAGE_PATH = "./data/upload/";

    @Override
    public String store(MultipartFile file) {
        // 拼接存储目录: 指定目录/年/月/日
        String storePath = PRE_REPORT_MANIFEST_FILE_STORAGE_PATH +
                DateUtil.year(new Date()) + "/" +
                DateUtil.month(new Date()) + "/" +
                DateUtil.dayOfMonth(new Date()) + "/";

        // 判断目录是否存在，若不存在则创建
        Path filePath = Paths.get(storePath);
        if (!Files.exists(filePath)) {
            try {
                Files.createDirectories(filePath);
            } catch (IOException e) {
                log.error("创建目录失败", e);
                throw new ServiceException("保存文件时创建目录失败，请联系管理员");
            }
        }

        // 拼接文件名：登录用户名_时间戳_源文件名
        String fileName = LoginUserHolder.getLoginUser().getNickname()
                + "_" + System.currentTimeMillis()
                + "_" + file.getOriginalFilename();

        // 完整路径
        storePath += fileName;
        // 保存文件
        Path targetLocation = Paths.get(storePath);
        try {
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            log.error("保存文件失败", e);
            throw new ServiceException("保存文件失败，请联系管理员");
        }
        return storePath;
    }
}
