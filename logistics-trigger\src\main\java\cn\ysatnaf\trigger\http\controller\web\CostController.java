package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.cost.model.entity.CostRuleEntity;
import cn.ysatnaf.domain.cost.model.req.CostCalculateReq;
import cn.ysatnaf.domain.cost.model.req.CostRuleUpdateReq;
import cn.ysatnaf.domain.cost.model.res.CostCalculateRes;
import cn.ysatnaf.domain.cost.service.CostService;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "计价相关")
@RequestMapping("/web/cost")
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
public class CostController {

    private final CostService costService;

    @PostMapping("/updateRule")
    @PermitAll
    @Operation(summary = "更新计价规则")
    public CommonResult<Boolean> update(@RequestBody @Validated CostRuleUpdateReq req) {
        costService.update(req);
        return CommonResult.success(true);
    }

    @PostMapping("/listRule")
    @PermitAll
    @Operation(summary = "计价规则列表")
    public CommonResult<List<CostRuleEntity>> list() {
        return CommonResult.success(costService.list());
    }

    @PostMapping("/calculate")
    @PermitAll
    @Operation(summary = "计算价格")
    public CommonResult<CostCalculateRes> calculate(@RequestBody @Validated CostCalculateReq req) {
        return CommonResult.success(costService.calculate(req));
    }
}