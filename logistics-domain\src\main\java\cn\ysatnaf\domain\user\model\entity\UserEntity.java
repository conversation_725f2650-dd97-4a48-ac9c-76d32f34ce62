package cn.ysatnaf.domain.user.model.entity;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.ysatnaf.domain.user.model.valobj.CommonStatusVO;
import cn.ysatnaf.domain.user.model.valobj.GenderVO;
import cn.ysatnaf.domain.user.model.valobj.RoleVO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * UserEntity
 *
 * <AUTHOR>
 * @date 2023/12/21 11:27
 */
@Data
public class UserEntity {

    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 加密后的密码
     */
    private String password;
    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 第三方平台唯一标识
     */
    private String openid;

    /**
     * 第三方平台类型 SocialType
     */
    private Integer socialType;

    /**
     * 备注
     */
    private String remark;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 手机号码
     */
    private String phone;

    /**
     * city	普通用户个人资料填写的城市
     */
    private String city;

    /**
     * province	普通用户个人资料填写的省份
     */
    private String province;
    /**
     * country	国家，如中国为CN
     */
    private String country;
    /**
     * 用户性别
     *
     * 枚举类 {@link GenderVO}
     */
    private Integer gender;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 帐号状态
     *
     * 枚举 {@link CommonStatusVO}
     */
    private Integer status;

    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 最后登录IP
     */
    private String loginIp;
    /**
     * 最后登录时间
     */
    private LocalDateTime loginDate;

    public Boolean ifCourier() {
        return ObjUtil.equal(this.roleId, RoleVO.COURIER.getCode());
    }

    public Boolean validatePassword(String password) {
        return StrUtil.equals(DigestUtil.md5Hex(password), this.password);
    }

    public boolean ifSuperAdmin() {
        return roleId.equals(RoleVO.SUPER_ADMIN.getCode());
    }

    public boolean ifAdmin() {
        return roleId.equals(RoleVO.ADMIN.getCode()) || roleId.equals(RoleVO.SUPER_ADMIN.getCode());
    }

    public boolean ifLocalUser() {
        return roleId.equals(RoleVO.LOCAL_USER.getCode());
    }

    public boolean ifNetworkUser() {
        return roleId.equals(RoleVO.NETWORK_USER.getCode());
    }
}
