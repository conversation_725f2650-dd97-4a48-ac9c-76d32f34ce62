package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.manifest.model.entity.Tracking;
import cn.ysatnaf.domain.manifest.repository.TrackingRepository;
import cn.ysatnaf.infrastructure.persistent.converter.TrackingConverter;
import cn.ysatnaf.infrastructure.persistent.dao.TrackingDao;
import cn.ysatnaf.infrastructure.persistent.po.TrackingPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class TrackingRepositoryImpl implements TrackingRepository {

    private final TrackingDao trackingDao;

    @Override
    public void insertBatch(List<Tracking> trackingToSave) {
        trackingDao.insertBatchSomeColumn(TrackingConverter.INSTANCE.toTrackingPOList(trackingToSave));
    }

    @Override
    public void save(Tracking tracking) {
        trackingDao.insert(TrackingConverter.INSTANCE.toTrackingPO(tracking));
    }

    @Override
    public List<Tracking> getByManifestId(Long manifestId) {
        LambdaQueryWrapper<TrackingPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TrackingPO::getManifestId, manifestId);
        wrapper.orderByDesc(TrackingPO::getTime).orderByDesc(TrackingPO::getCreateTime);
        List<TrackingPO> trackingPOList = trackingDao.selectList(wrapper);
        return TrackingConverter.INSTANCE.toTrackingList(trackingPOList);
    }

    @Override
    public List<Tracking> getByManifestIds(List<Long> manifestIds) {
        LambdaQueryWrapper<TrackingPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TrackingPO::getManifestId, manifestIds);
        wrapper.orderByDesc(TrackingPO::getTime).orderByDesc(TrackingPO::getCreateTime);
        List<TrackingPO> trackingPOList = trackingDao.selectList(wrapper);
        return TrackingConverter.INSTANCE.toTrackingList(trackingPOList);
    }
}
