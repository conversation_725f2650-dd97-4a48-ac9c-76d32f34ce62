package cn.ysatnaf.domain.manifest.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
public interface ExpressNumberService {

    String getOrderNumber(String finalSawagaNumber);

    /**
     * 获取佐川物流订单号
     * @return
     */
    String getSawagaNumber();

    List<String> getInternationalNumberBatch(Integer count);

    /**
     * 导入佐川物流订单号
     * @param file txt文件
     * @return
     */
    Integer importSawaga(MultipartFile file);

    Integer countLeftSawagaNumber();

    void revertSawagaNumber(String expressNo);
}
