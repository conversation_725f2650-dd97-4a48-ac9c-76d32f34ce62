package cn.ysatnaf.domain.parcelsorting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "转箱入参")
@Data
public class ParcelSortingChangeBoxReq {

    @Schema(description = "分箱包裹ID列表")
    @NotEmpty
    private List<Long> ids;

    @Schema(description = "要转换的箱子ID")
    @NotNull
    private Long boxId;
}
