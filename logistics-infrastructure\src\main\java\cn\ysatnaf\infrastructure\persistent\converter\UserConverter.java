package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.infrastructure.persistent.po.UserPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * UserConverter
 *
 * <AUTHOR>
 * @date 2023/12/21 20:11
 */
@Mapper
public interface UserConverter {
    UserConverter INSTANCE = Mappers.getMapper(UserConverter.class);

    UserEntity po2entity(UserPO po);

    UserPO entity2po(UserEntity userEntity);

    List<UserEntity> poList2entityList(List<UserPO> records);
}
