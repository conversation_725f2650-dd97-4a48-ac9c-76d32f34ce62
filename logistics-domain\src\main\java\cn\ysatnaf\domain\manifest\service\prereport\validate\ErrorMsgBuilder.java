package cn.ysatnaf.domain.manifest.service.prereport.validate;

import lombok.Getter;

/**
 * 错误信息构造器
 */
public class ErrorMsgBuilder {

    private static final String EXPRESS_NUMBER_EMPTY_MSG = "【快递单号为空】";
    private static final String EXPRESS_NUMBER_DUPLICATE_MSG = "【重复上传：快递单号%s重复，对应订单已揽件，无法覆盖】";
    private static final String ORDER_NUMBER_DUPLICATE_MSG = "【重复上传：订单号%s重复，对应订单已揽件，无法覆盖】";
    private static final String SYSTEM_STOCK_NOT_ENOUGH_MSG = "【系统内部物流单号库存不足，请联系管理员】";
    private static final String MERCHANT_ORDER_NUMBER_EMPTY_MSG = "【商店订单号为空】";
    private static final String WEIGHT_EMPTY_MSG = "【重量为空】";
    private static final String WEIGHT_NOT_POSITIVE_MSG = "【重量必须大于0】";
    private static final String QUANTITY_EMPTY_MSG = "【数量为空】";
    private static final String QUANTITY_NOT_POSITIVE_MSG = "【数量必须大于0】";
    private static final String PRICE_EMPTY_MSG = "【价值为空】";
    private static final String RECEIVER_ZIP_CODE_EMPTY_MSG = "【邮编为空】";
    private static final String RECEIVER_ZIP_CODE_FORBIDDEN_MSG = "【邮编%s禁止预报】";
    private static final String RECEIVER_ZIP_CODE_NOT_EXIST_MSG = "【邮编%s不存在】";
    private static final String RECEIVER_NAME_EMPTY_MSG = "【收件人为空】";
    private static final String RECEIVER_PHONE_EMPTY_MSG = "【收件人电话为空】";
    private static final String RECEIVER_PHONE_FORMAT_ERROR_MSG = "【电话号码%s格式错误】";
    private static final String ITEM_NAME_EMPTY_MSG = "【物品中文名称为空】";

    /**
     * 数据行号
     */
    @Getter
    private final Integer rowNumber;

    /**
     * 用于存储、拼接错误信息
     */
    private final StringBuilder stringBuilder;

    public ErrorMsgBuilder(Integer rowNumber) {
        this.rowNumber = rowNumber;
        this.stringBuilder = new StringBuilder();
    }

    /**
     * 拼接错误信息
     * @param errorMsg 错误信息
     */
    public void appendErrorMsg(String errorMsg) {
        stringBuilder.append(errorMsg);
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorMsg() {
        return stringBuilder.toString();
    }

    /**
     * 快递单号为空
     */
    public void expressNumberEmpty() {
        stringBuilder.append(EXPRESS_NUMBER_EMPTY_MSG);
    }

    /**
     * 快递单号重复
     * @param expressNumber 快递单号
     */
    public void expressNumberDuplicate(String expressNumber) {
        stringBuilder.append(String.format(EXPRESS_NUMBER_DUPLICATE_MSG, expressNumber));
    }

    /**
     * 订单号重复
     * @param orderNumber 订单号
     */
    public void orderDuplicate(String orderNumber) {
        stringBuilder.append(String.format(ORDER_NUMBER_DUPLICATE_MSG, orderNumber));
    }

    /**
     * 商家订单号单号为空
     */
    public void merchantOrderNumberEmpty() {
        stringBuilder.append(MERCHANT_ORDER_NUMBER_EMPTY_MSG);
    }


    /**
     * 重量为空
     */
    public void weightEmpty() {
        stringBuilder.append(WEIGHT_EMPTY_MSG);
    }

    /**
     * 重量必须大于0
     */
    public void weightNotPositive() {
        stringBuilder.append(WEIGHT_NOT_POSITIVE_MSG);
    }

    /**
     * 数量为空
     */
    public void quantityEmpty() {
        stringBuilder.append(QUANTITY_EMPTY_MSG);
    }

    /**
     * 数量必须大于0
     */
    public void quantityNotPositive() {
        stringBuilder.append(QUANTITY_NOT_POSITIVE_MSG);
    }

    /**
     * 价值为空
     */
    public void priceEmpty() {
        stringBuilder.append(PRICE_EMPTY_MSG);
    }

    /**
     * 邮编为空
     */
    public void zipCodeEmpty() {
        stringBuilder.append(RECEIVER_ZIP_CODE_EMPTY_MSG);
    }

    /**
     * 邮编禁止预报
     */
    public void zipCodeForbidden(String zipCode) {
        stringBuilder.append(String.format(RECEIVER_ZIP_CODE_FORBIDDEN_MSG, zipCode));
    }

    /**
     * 邮编不存在
     * @param receiverZipCode 邮编
     */
    public void zipCodeNotFound(String receiverZipCode) {
        stringBuilder.append(String.format(RECEIVER_ZIP_CODE_NOT_EXIST_MSG, receiverZipCode));
    }

    /**
     * 收件人为空
     */
    public void receiverNameEmpty() {
        stringBuilder.append(RECEIVER_NAME_EMPTY_MSG);
    }

    /**
     * 收件人电话为空
     */
    public void receiverPhoneEmpty() {
        stringBuilder.append(RECEIVER_PHONE_EMPTY_MSG);
    }

    /**
     * 电话号码格式错误
     */
    public void receiverPhoneFormatError(String receiverPhone) {
        stringBuilder.append(String.format(RECEIVER_PHONE_FORMAT_ERROR_MSG, receiverPhone));
    }

    /**
     * 物品中文名称为空
     */
    public void itemNameEmpty() {
        stringBuilder.append(ITEM_NAME_EMPTY_MSG);
    }
}
