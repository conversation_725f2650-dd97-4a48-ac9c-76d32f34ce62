package cn.ysatnaf.domain.parcelsorting.repository;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingRecordPO;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.domain.parcelsorting.model.entity.ParcelSortingRecord;


/**
 * <AUTHOR> Hang
 */
public interface ParcelSortingRecordRepository {
    Integer countByRecordName(String recordName);

    void createRecord(String recordName, Long masterBillId);

    void delete(Long id);

    ParcelSortingRecordPO getByRecordName(String recordName);

    void update(Long id, String recordName, Long masterBillId);

    PageResult<ParcelSortingRecordPO> list(Integer pageNo, Integer pageSize);

    /**
     * 保存装箱记录领域实体
     * @param record 装箱记录实体
     */
    void save(ParcelSortingRecord record);

    /**
     * 根据ID获取装箱记录
     * @param id 记录ID
     * @return 装箱记录PO
     */
    ParcelSortingRecordPO getById(Long id);
}
