package cn.ysatnaf.domain.trackingnumber.model.valobj;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单号状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TrackingNumberStatus {

    AVAILABLE(0, "可用"),
    ALLOCATED(1, "已分配");

    @EnumValue
    private final Integer code;
    private final String description;

    public static TrackingNumberStatus fromCode(Integer code) {
        for (TrackingNumberStatus status : TrackingNumberStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null; // 或者抛出异常
    }
} 