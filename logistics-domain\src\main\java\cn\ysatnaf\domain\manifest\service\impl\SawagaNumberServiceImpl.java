package cn.ysatnaf.domain.manifest.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.manifest.model.entity.SawagaNumberEntity;
import cn.ysatnaf.domain.manifest.repository.SawagaNumberRepository;
import cn.ysatnaf.domain.manifest.service.SawagaNumberService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class SawagaNumberServiceImpl implements SawagaNumberService {

    private final SawagaNumberRepository sawagaNumberRepository;

    @Override
    public void insertBatch(List<SawagaNumberEntity> numbers) {
        sawagaNumberRepository.insertBatch(numbers);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SawagaNumberEntity useOne() {
        SawagaNumberEntity expressNumberEntity = sawagaNumberRepository.getOne();
        if (expressNumberEntity == null) {
            throw new ServiceException(9999, "没有未使用佐川运单号，请导入");
        }
        expressNumberEntity.setUsed(true);
        sawagaNumberRepository.updateById(expressNumberEntity);
        return expressNumberEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SawagaNumberEntity> useBatch(Integer count) {
        List<SawagaNumberEntity> expressNumberEntities = sawagaNumberRepository.getBatch(count);
        if (CollUtil.isEmpty(expressNumberEntities) || expressNumberEntities.size() < count) {
            throw new ServiceException(9999, "系统运单号不足，请联系管理员进行添加");
        }
        expressNumberEntities.forEach(entity -> entity.setUsed(true));
        sawagaNumberRepository.updateBatchById(expressNumberEntities);
        return expressNumberEntities;
    }

    @Override
    public List<SawagaNumberEntity> getBatchSawagaNumber(int size) {
        return sawagaNumberRepository.getBatchSawagaNumberUnused(size);
    }

    @Override
    public void updateBatchById(List<SawagaNumberEntity> batchSawagaNumber) {
        sawagaNumberRepository.updateBatchById(batchSawagaNumber);
    }

    @Override
    public Integer countLeftSawagaNumber() {
        return sawagaNumberRepository.countUsable();
    }

    @Override
    public void revertSawagaNumber(String expressNo) {
        SawagaNumberEntity sawagaNumberEntity = sawagaNumberRepository.getByExpressNo(expressNo);
        sawagaNumberEntity.setUsed(false);
        sawagaNumberRepository.updateById(sawagaNumberEntity);
    }
}
