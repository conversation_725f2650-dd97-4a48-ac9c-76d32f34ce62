package cn.ysatnaf.domain.address.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.ysatnaf.domain.address.model.entity.AddressBookEntity;
import cn.ysatnaf.domain.address.repository.AddressBookRepository;
import cn.ysatnaf.domain.address.service.AddressBookService;
import cn.ysatnaf.types.common.PageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * AddressBookServiceImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 10:42
 */
@Service
@RequiredArgsConstructor
public class AddressBookServiceImpl implements AddressBookService {

    private final AddressBookRepository addressBookRepository;

    @Override
    public Boolean add(AddressBookEntity addressBookEntity) {
        return addressBookRepository.insert(addressBookEntity);
    }

    @Override
    public Boolean delete(Long id) {
        return addressBookRepository.delete(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(AddressBookEntity addressBookEntity) {
        if (Optional.ofNullable(addressBookEntity.getIsDefault()).orElse(false)) {
            List<AddressBookEntity> bookEntities = addressBookRepository.listByOpenid(addressBookEntity.getOpenid());
            if (CollUtil.isNotEmpty(bookEntities)) {
                bookEntities.forEach(entity -> entity.setIsDefault(false));
                addressBookRepository.updateBatchByIds(bookEntities);
            }
        }
        return addressBookRepository.updateById(addressBookEntity);
    }

    @Override
    public PageResult<AddressBookEntity> page(String openid, Integer pageNo, Integer pageSize) {
        return addressBookRepository.page(openid, pageNo, pageSize);
    }

    @Override
    public AddressBookEntity getById(Long sendAddressBookId) {
        return addressBookRepository.getById(sendAddressBookId);
    }

    @Override
    public AddressBookEntity getDefault(String openid) {
        AddressBookEntity defaultBook = addressBookRepository.getDefaultByOpenid(openid);
        if (ObjUtil.isNotEmpty(defaultBook)) {
            return defaultBook;
        }
        return addressBookRepository.getFirstOneByOpenid(openid);
    }
}
