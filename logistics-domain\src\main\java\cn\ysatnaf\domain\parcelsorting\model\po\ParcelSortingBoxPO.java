package cn.ysatnaf.domain.parcelsorting.model.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ParcelSortingDetailPO
 *
 * <AUTHOR> <PERSON>
 * @date 2024/3/29 18:56
 */
@EqualsAndHashCode(callSuper = true)
@TableName("tb_parcel_sorting_box")
@Data
public class ParcelSortingBoxPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long recordId;

    private String boxName;

    private String packageNumber;
}
