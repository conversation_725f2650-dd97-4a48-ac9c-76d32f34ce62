package cn.ysatnaf.domain.statistics.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MonthlyAmountDTO {
    private BigDecimal totalAmount;            // 本月总金额
    private BigDecimal dailyAvgAmount;         // 日均金额
    private Double monthOnMonthChange;         // 总金额环比变化
    private Double avgDailyChange;              // 日均金额环比变化
    private PeakAmountDayDTO peakDay;          // 峰值日数据
    private List<MonthAmountTrendDTO> trends;  // 月度趋势
    private TemplateAmountDistributionDTO templateDistribution; // 模板类型分布
    private RegionalAmountDistributionDTO regionalDistribution; // 区域分布
}