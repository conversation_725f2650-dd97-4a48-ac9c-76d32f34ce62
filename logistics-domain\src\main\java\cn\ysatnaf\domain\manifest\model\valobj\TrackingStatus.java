package cn.ysatnaf.domain.manifest.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TrackingStatus {

    /**
     * 预报 - Forecast
     * 揽收 - Collection
     * 发货 - Dispatch
     * 起飞 - Takeoff
     * 降落 - Landing
     * 清关 - Customs Clearance
     * 派送 - Delivery
     * 送达 - Delivered
     */
    NONE(0, "", "", ""),
    FORECAST(1, "已完成预报", "中国", ""),
    PICKED_UP(2,    "快件已完成分拣（福州处理中心） [当前位置-中国]", "中国", ""),
    SHIPPED(3, "快件已送达机场，等待航班起飞 [当前位置-中国]", "中国", ""),
    TAKEOFF(4, "航班已起飞 [当前位置-中国]", "中国", ""),
    LANDING(5, "航班已抵达机场（提货中） [当前位置-日本]", "日本", ""),
    CUSTOMS_CLEARANCE(6, "清关", "日本", ""),
    PICKUP(8, "⇒集荷 [日本] 【佐川急便】", "日本", ""),
    DELIVERY(9, "↓輸送中 [日本] 【佐川急便】", "日本", ""),
    DELIVERED(10, "⇒配達完了，感谢使用斑马物巢！ [日本]", "日本", ""),
    HELD(11, "⇒保管中 [日本] 【佐川急便】", "日本", ""),
    ATTEMPTED(12, "⇒ご不在 [日本] 【佐川急便】", "日本", ""),
    ON_VEHICLE(13, "⇒配達中 [日本] 【佐川急便】", "日本", "On vehicle for delivery"),
    DELIVERY_COMPLETED(13, "⇒引渡完了 [日本] 【佐川急便】", "日本", "Delivery completed"),
    AVAILABLE_FOR_COLLECTION(14, "⇒営業所受取 [日本] 【佐川急便】", "日本", "Available for collection: The package is available for collection at the Sagawa branch."),
    WAITING_FOR_PICKUP(15, "⇒受取場所保管中\n" +
            "【受取先変更】 [日本] 【佐川急便】", "日本", "Waiting for pickup: Your shipment is available for pickup at your requested location."),
    ON_VEHICLE_BACK_TO_BRANCH(16, "⇒持戻り [日本] 【佐川急便】", "日本", "On vehicle back to branch: On its way back to the Sagawa branch."),
    HELD_2(17, "⇒保管中 [日本] 【佐川急便】", "日本", "The package is being held by our driver. Please contact us to arrange delivery."),
    EXCEPTION(-1, "営業所へお問い合わせください。 [日本] 【佐川急便】", "日本", "Exception: Please contact the Sagawa branch."),
    UNDEFINED(-2, "", "日本", ""),
    ;

    private final Integer value;
    private final String description;
    private final String place;
    private final String keyword;

    public static TrackingStatus matchByValue(Integer value) {
        for (TrackingStatus status : TrackingStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return NONE;
    }

    public static TrackingStatus matchByKeyword(String keyword) {
        for (TrackingStatus status : TrackingStatus.values()) {
            if (status.getKeyword().equals(keyword)) {
                return status;
            }
        }
        return NONE;
    }
}
