package cn.ysatnaf.trigger.http.controller;

import cn.ysatnaf.domain.manifest.model.req.InvoiceGenerationTaskSubmitReq;
import cn.ysatnaf.domain.task.model.po.TaskPO;
import cn.ysatnaf.domain.task.model.req.TaskListReq;
import cn.ysatnaf.domain.task.service.TaskService;
import cn.ysatnaf.types.common.CommonResult;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 任务控制器
 * <AUTHOR>
 */
@Tag(name = "任务管理")
@RestController
@RequestMapping("/api/task")
@RequiredArgsConstructor
public class TasksController {

    private final TaskService taskService;

    @Operation(summary = "提交发票生成任务")
    @PostMapping("/invoice/generate")
    public CommonResult<Long> submitInvoiceGenerationTask(@RequestBody @Validated InvoiceGenerationTaskSubmitReq req) {
        Long taskId = taskService.createGenerateInvoiceTask(req);
        return CommonResult.success(taskId);
    }
    

    @Operation(summary = "获取任务详情")
    @GetMapping("/{taskId}")
    public CommonResult<TaskPO> getTask(@PathVariable Long taskId) {
        TaskPO taskPO = taskService.getById(taskId);
        return CommonResult.success(taskPO);
    }

    @Operation(summary = "查询任务列表")
    @PostMapping("/list")
    public CommonResult<PageResult<TaskPO>> listTask(@RequestBody TaskListReq req) {
        PageResult<TaskPO> pageResult = taskService.list(req);
        return CommonResult.success(pageResult);
    }
} 