CREATE TABLE `address_book` (
                                `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                `openid` VARCHAR(255) DEFAULT NULL COMMENT '用户OPENID',
                                `name` VARCHAR(255) DEFAULT NULL COMMENT '名字',
                                `gender` INT DEFAULT NULL COMMENT '性别',
                                `phone` VARCHAR(255) DEFAULT NULL COMMENT '手机号',
                                `province_code` INT DEFAULT NULL COMMENT '省编码',
                                `province_name` VARCHAR(255) DEFAULT NULL COMMENT '省名称',
                                `city_code` INT DEFAULT NULL COMMENT '市编码',
                                `city_name` VARCHAR(255) DEFAULT NULL COMMENT '市名称',
                                `district_code` INT DEFAULT NULL COMMENT '区县编码',
                                `district_name` VARCHAR(255) DEFAULT NULL COMMENT '区县名称',
                                `street_code` INT DEFAULT NULL COMMENT '街道编码',
                                `street_name` VARCHAR(255) DEFAULT NULL COMMENT '街道名称',
                                `committee_code` INT DEFAULT NULL COMMENT '居委/社区编码',
                                `committee_name` VARCHAR(255) DEFAULT NULL COMMENT '居委/社区名称',
                                `detail` VARCHAR(255) DEFAULT NULL COMMENT '详细地址',
                                `label` VARCHAR(255) DEFAULT NULL COMMENT '标签',
                                `is_default` TINYINT(1) DEFAULT NULL COMMENT '是否默认地址',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AddressBookPO';