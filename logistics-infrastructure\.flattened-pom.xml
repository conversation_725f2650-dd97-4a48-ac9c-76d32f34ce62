<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.ysatnaf</groupId>
    <artifactId>logistics</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <groupId>cn.ysatnaf</groupId>
  <artifactId>logistics-infrastructure</artifactId>
  <version>1.0-SNAPSHOT</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.ysatnaf</groupId>
      <artifactId>logistics-domain</artifactId>
    </dependency>
  </dependencies>
  <build>
    <finalName>logistics-infrastructure</finalName>
    <plugins>
      <plugin>
        <artifactId>maven-archetype-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <goals>
              <goal>create-from-project</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
