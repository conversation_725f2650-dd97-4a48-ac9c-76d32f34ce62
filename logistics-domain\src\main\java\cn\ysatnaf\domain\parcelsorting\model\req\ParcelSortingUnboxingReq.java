package cn.ysatnaf.domain.parcelsorting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "拆箱入参")
@Data
public class ParcelSortingUnboxingReq {

    @Schema(description = "分箱ID")
    @NotNull
    private Long boxId;

    @Schema(description = "物流单号")
    @NotBlank
    private String expressNumber;
}
