package cn.ysatnaf.domain.site.service.impl;

import cn.ysatnaf.domain.site.model.po.SiteCodePO;
import cn.ysatnaf.domain.site.repository.SiteCodeRepository;
import cn.ysatnaf.domain.site.service.SiteCodeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SiteCodeServiceImpl implements SiteCodeService {

    private final SiteCodeRepository siteCodeRepository;

    @Override
    public SiteCodePO getByZipCodeAndType(String receiverZipCode, int siteType) {
        return siteCodeRepository.getByZipCodeAndType(receiverZipCode, siteType);
    }
}
