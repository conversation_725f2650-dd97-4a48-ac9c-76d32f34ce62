package cn.ysatnaf.domain.shippingfeetemplate.model.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@TableName("tb_shipping_fee_template")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ShippingFeeTemplatePO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 首重价格
     */
    private BigDecimal firstWeightPrice;

    /**
     * 首重范围
     */
    private BigDecimal firstWeightRange;

    /**
     * 续重价格
     */
    private BigDecimal continuedWeightPrice;

    /**
     * 续重区间大小
     */
    private BigDecimal continuedWeightInterval;

    /**
     * 轻抛系数
     */
    private Integer bulkCoefficient;

    /**
     * 三边和超过该值开始计算体积重量
     */
    private BigDecimal threeSidesStart;

    /**
     * 模板类型：1-普通模板；2-带电模板；3-投函模板
     */
    private Integer type;

}
