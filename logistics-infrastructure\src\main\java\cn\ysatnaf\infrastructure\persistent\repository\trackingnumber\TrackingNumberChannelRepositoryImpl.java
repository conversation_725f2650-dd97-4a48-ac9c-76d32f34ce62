package cn.ysatnaf.infrastructure.persistent.repository.trackingnumber;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberChannel;
import cn.ysatnaf.domain.trackingnumber.repository.TrackingNumberChannelRepository;
import cn.ysatnaf.infrastructure.persistent.converter.trackingnumber.TrackingNumberChannelConverter;
import cn.ysatnaf.infrastructure.persistent.dao.trackingnumber.TrackingNumberChannelDao;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberChannelPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 运单号渠道仓库实现
 * <AUTHOR>
 */
@Repository
public class TrackingNumberChannelRepositoryImpl extends ServiceImpl<TrackingNumberChannelDao, TrackingNumberChannelPO> implements TrackingNumberChannelRepository {

    private final TrackingNumberChannelConverter converter = TrackingNumberChannelConverter.INSTANCE;

    @Override
    public Optional<TrackingNumberChannel> findById(Long id) {
        TrackingNumberChannelPO po = getById(id);
        return Optional.ofNullable(converter.toEntity(po));
    }

    @Override
    public List<TrackingNumberChannel> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<TrackingNumberChannelPO> poList = listByIds(ids);
        return converter.toEntityList(poList);
    }

    @Override
    public Optional<TrackingNumberChannel> findByChannelCode(String channelCode) {
        LambdaQueryWrapper<TrackingNumberChannelPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TrackingNumberChannelPO::getChannelCode, channelCode)
                    .last("LIMIT 1");
        TrackingNumberChannelPO po = getOne(queryWrapper);
        return Optional.ofNullable(converter.toEntity(po));
    }

    @Override
    public Optional<TrackingNumberChannel> findByCombination(Long carrierId, Long locationId, Long shipmentTypeId) {
         LambdaQueryWrapper<TrackingNumberChannelPO> queryWrapper = Wrappers.lambdaQuery();
         queryWrapper.eq(TrackingNumberChannelPO::getCarrierId, carrierId)
                     .eq(TrackingNumberChannelPO::getLocationId, locationId)
                     .eq(TrackingNumberChannelPO::getShipmentTypeId, shipmentTypeId)
                     .last("LIMIT 1");
        TrackingNumberChannelPO po = getOne(queryWrapper);
        return Optional.ofNullable(converter.toEntity(po));
    }

    @Override
    public List<TrackingNumberChannel> findAllActiveChannels() {
        LambdaQueryWrapper<TrackingNumberChannelPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(TrackingNumberChannelPO::getIsActive, true);
        List<TrackingNumberChannelPO> poList = list(queryWrapper);
        return converter.toEntityList(poList);
    }

    @Override
    public TrackingNumberChannel save(TrackingNumberChannel channel) {
        TrackingNumberChannelPO po = converter.toPO(channel);
        saveOrUpdate(po);
        return converter.toEntity(po);
    }
} 