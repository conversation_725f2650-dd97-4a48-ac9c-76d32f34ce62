package cn.ysatnaf.domain.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.auth.adatper.UserAdapter;
import cn.ysatnaf.domain.auth.repository.UserRepository;
import cn.ysatnaf.domain.fund.model.entity.FundAccountEntity;
import cn.ysatnaf.domain.fund.model.res.FundAccountGetRes;
import cn.ysatnaf.domain.fund.service.FundAccountService;
import cn.ysatnaf.domain.log.model.entity.OperationLogEntity;
import cn.ysatnaf.domain.log.model.valobj.OperationType;
import cn.ysatnaf.domain.log.service.OperationLogService;
import cn.ysatnaf.domain.manifest.service.ManifestService;
import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplateUserPO;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.domain.shippingfeetemplate.service.ShippingFeeTemplateUserService;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.model.req.*;
import cn.ysatnaf.domain.user.model.res.UserInfoRes;
import cn.ysatnaf.domain.user.model.res.UserInfoSimpleRes;
import cn.ysatnaf.domain.user.model.valobj.CommonStatusVO;
import cn.ysatnaf.domain.user.model.valobj.RoleVO;
import cn.ysatnaf.domain.user.model.valobj.SocialTypeVO;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.common.ErrorCodeConstants;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.exception.ServiceException;
import cn.ysatnaf.types.util.ServiceExceptionUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * UserServiceImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/21 19:17
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;

    private final ShippingFeeTemplateUserService shippingFeeTemplateUserService;

    @Override
    public UserEntity findByOpenid(String openid) {
        if (openid != null) {
            return userRepository.findByOpenId(openid);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(UserEntity newUser) {
        validateSuperAdmin();
        UserEntity userEntity = userRepository.insert(newUser);
        OperationLogEntity operation = OperationLogEntity.builder()
                .operatorId(LoginUserHolder.getLoginUser().getId())
                .userId(userEntity.getId())
                .operationType(OperationType.USER_CREATE.getValue())
                .operation("创建用户")
                .newInfo(JSON.toJSONString(userEntity))
                .build();
        OperationLogService operationLogService = SpringUtil.getBean(OperationLogService.class);
        operationLogService.log(operation);
    }

    @Override
    public UserEntity findByUsername(String username) {
        return userRepository.getByUsername(username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(UserAddReq req) {
        validateSuperAdmin();
        Long id = LoginUserHolder.getLoginUser().getId();
        UserEntity loginUser = userRepository.getById(id);
        if (!loginUser.validatePassword(req.getOwnPassword())) {
            throw new ServiceException("密码错误");
        }
        // 判断用户名是否重复
        UserEntity userInDb = userRepository.getByUsername(req.getUsername());
        if (ObjUtil.isNotNull(userInDb)) {
            throw new ServiceException("用户名已存在");
        }
        UserEntity userEntity = new UserEntity();
        userEntity.setUsername(req.getUsername());
        String password = req.getPassword();
        // 加密密码
        password = DigestUtil.md5Hex(password);
        userEntity.setPassword(password);
        userEntity.setNickname(req.getNickname());
        userEntity.setRoleId(req.getRoleId());
        userEntity.setStatus(CommonStatusVO.ENABLE.getStatus());
        userEntity.setPhone(req.getMobileNumber());

        userEntity = userRepository.insert(userEntity);

        FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
        // 初始化账户
        fundAccountService.init(userEntity.getId());

        // 记录日志
        OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                .operatorId(id)
                .userId(userEntity.getId())
                .operationType(OperationType.USER_CREATE.getValue())
                .operation(OperationType.USER_CREATE.getDescription())
                .newInfo(JSON.toJSONString(userEntity))
                .build();

        OperationLogService operationLogService = SpringUtil.getBean(OperationLogService.class);
        operationLogService.log(operationLogEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(UserUpdateReq req) {
        validateSuperAdmin();
        UserEntity loginUser = userRepository.getById(LoginUserHolder.getLoginUser().getId());
        if (!loginUser.validatePassword(req.getPassword())) {
            throw new ServiceException("密码错误");
        }
        UserEntity originalUser = userRepository.getById(req.getId());
        UserEntity userEntity = new UserEntity();
        userEntity.setId(req.getId());
        userEntity.setPassword(DigestUtil.md5Hex(req.getPassword()));
        userEntity.setNickname(req.getNickname());
        userEntity.setRoleId(req.getRoleId());
        userEntity.setStatus(req.getStatus());
        userRepository.updateById(userEntity);
        // 记录日志
        OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                .operatorId(LoginUserHolder.getLoginUser().getId())
                .userId(userEntity.getId())
                .operationType(OperationType.USER_UPDATE.getValue())
                .operation(OperationType.USER_UPDATE.getDescription())
                .originalInfo(JSON.toJSONString(originalUser))
                .newInfo(JSON.toJSONString(userEntity))
                .build();

        OperationLogService operationLogService = SpringUtil.getBean(OperationLogService.class);
        operationLogService.log(operationLogEntity);
    }

    @Override
    public PageResult<UserInfoRes> page(UserPageReq req) {
        validateSuperAdmin();
        PageResult<UserEntity> userEntityPageResult = userRepository.page(req.getPageNo(), req.getPageSize());
        if (CollUtil.isEmpty(userEntityPageResult.getList())) {
            return PageResult.empty();
        }
        List<UserInfoRes> userInfoResList = UserAdapter.INSTANCE.entityList2userInfoResList(userEntityPageResult.getList());
        // 查询对应用户的账户余额
        FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
        List<Long> userIds = userInfoResList.stream()
                .map(UserInfoRes::getId)
                .collect(Collectors.toList());
        List<FundAccountEntity> fundAccountEntities = fundAccountService.listByUserIds(userIds);
        Map<Long, FundAccountEntity> userIdAccountMap = fundAccountEntities.stream().collect(Collectors.toMap(FundAccountEntity::getUserId, account -> account));

        Map<Long, Map<Integer, Long>> userId2Type2TemplateIdMap = new HashMap<>();
        // 查询用户运费模板
        List<ShippingFeeTemplateUserPO> shippingFeeTemplateUserPOList = shippingFeeTemplateUserService.getByUserIds(userIds);
        if (CollUtil.isNotEmpty(shippingFeeTemplateUserPOList)) {
            Map<Long, List<ShippingFeeTemplateUserPO>> userIdTemplates = shippingFeeTemplateUserPOList.stream().collect(Collectors.groupingBy(ShippingFeeTemplateUserPO::getUserId));
            userIdTemplates.forEach((userId, templates) -> {
                Map<Integer, Long> type2TemplateIdMap = templates.stream().collect(Collectors.toMap(ShippingFeeTemplateUserPO::getType, ShippingFeeTemplateUserPO::getTemplateId));
                userId2Type2TemplateIdMap.put(userId, type2TemplateIdMap);
            });
        }
        userInfoResList.forEach(userInfoRes -> {
            FundAccountEntity fundAccountEntity = userIdAccountMap.get(userInfoRes.getId());
            userInfoRes.setAccountId(fundAccountEntity.getId());
            userInfoRes.setBalance(fundAccountEntity.getBalance());
            Map<Integer, Long> type2TemplateIdMap = userId2Type2TemplateIdMap.get(userInfoRes.getId());
            if (CollUtil.isNotEmpty(type2TemplateIdMap)) {
                userInfoRes.setGeneralTemplateId(type2TemplateIdMap.get(ShippingFeeTemplateTypeEnum.GENERAL.getCode()));
                userInfoRes.setElectronicsTemplateId(type2TemplateIdMap.get(ShippingFeeTemplateTypeEnum.ELECTRONICS.getCode()));
                userInfoRes.setSmallParcelTemplateId(type2TemplateIdMap.get(ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode()));
            }
        });
        return new PageResult<>(userInfoResList, userEntityPageResult.getTotal());
    }

    @Override
    public UserInfoRes get(Long id) {
        validateSuperAdmin();
        UserEntity userEntity = userRepository.getById(id);
        UserInfoRes userInfoRes = UserAdapter.INSTANCE.entity2userInfoRes(userEntity);
        FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
        FundAccountGetRes account = fundAccountService.getByUserId(userInfoRes.getId());
        userInfoRes.setAccountId(account.getId());
        userInfoRes.setBalance(account.getBalance());
        return userInfoRes;
    }

    @Override
    public UserEntity getById(Long userId) {
        return userRepository.getById(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long userId, String password) {
        // 校验权限，只有管理员可以操作
        validateSuperAdmin();
        UserEntity loginUser = userRepository.getById(LoginUserHolder.getLoginUser().getId());
        if (!loginUser.validatePassword(password)) {
            throw new ServiceException("密码错误");
        }
        UserEntity user = userRepository.getById(userId);
        userRepository.deleteById(userId);
        FundAccountService fundAccountService = SpringUtil.getBean(FundAccountService.class);
        fundAccountService.deleteByUserId(userId);

        // 删除运单
        ManifestService manifestService = SpringUtil.getBean(ManifestService.class);
        manifestService.deleteByUserId(userId);
        // 记录日志
        OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                .operatorId(LoginUserHolder.getLoginUser().getId())
                .userId(userId)
                .operationType(OperationType.USER_DELETE.getValue())
                .operation(OperationType.USER_DELETE.getDescription())
                .originalInfo(JSON.toJSONString(user))
                .build();

        OperationLogService operationLogService = SpringUtil.getBean(OperationLogService.class);
        operationLogService.log(operationLogEntity);
    }

    @Override
    public boolean updatePassword(UserPasswordUpdateReq req) {
        // 确认密码
        if (!StrUtil.equals(req.getPassword(), req.getConfirmPassword())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.PASSWORD_CONFORMED_FAILED);
        }

        // 获取当前用户
        Long id = LoginUserHolder.getLoginUser().getId();
        UserEntity userEntity = userRepository.getById(id);

        // 加密入参密码，校验是否一致
        String oldPassword = DigestUtil.md5Hex(req.getOldPassword());
        if (!StrUtil.equals(oldPassword, userEntity.getPassword())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.USER_PASSWORD_FAILED);
        }

        // 设置密码为新的密码，然后更新
        userEntity.setPassword(DigestUtil.md5Hex(req.getPassword()));
        userRepository.updateById(userEntity);
        return true;
    }

    @Override
    public void validateSuperAdmin() {
        if (!LoginUserHolder.getLoginUser().ifSuperAdmin()) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.HAS_NO_AUTHORITY);
        }
    }

    @Override
    public void validateAdmin() {
        if (!LoginUserHolder.getLoginUser().ifAdmin()) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.HAS_NO_AUTHORITY);
        }
    }

    @Override
    public void edit(UserEditReq req) {
        Long userId = LoginUserHolder.getLoginUser().getId();
        UserEntity userEntity = userRepository.getById(userId);
        userEntity.setNickname(req.getNickname());
        userRepository.updateById(userEntity);
    }

    @Override
    public void resetPassword(ResetPasswordReq req) {
        UserEntity user = userRepository.getById(req.getUserId());
        user.setPassword(DigestUtil.md5Hex("banma123456"));
        userRepository.updateById(user);
    }

    @Override
    public PageResult<UserInfoSimpleRes> pageUserSimple(UserPageReq req) {
        PageResult<UserEntity> userEntityPageResult = userRepository.page(req.getPageNo(), req.getPageSize());
        if (CollUtil.isEmpty(userEntityPageResult.getList())) {
            return PageResult.empty();
        }
        List<UserInfoSimpleRes> result = userEntityPageResult.getList().stream()
                .map(user -> UserInfoSimpleRes.builder().id(user.getId()).username(user.getUsername()).build())
                .collect(Collectors.toList());
        return new PageResult<>(result, userEntityPageResult.getTotal());
    }

    @Override
    public List<UserEntity> listBatchByIds(Set<Long> creatorIds) {
        if (CollUtil.isEmpty(creatorIds)) {
            return Collections.emptyList();
        }
        return userRepository.listByIds(creatorIds);
    }

    @Override
    public void saveByMp(WxOAuth2UserInfo userInfo) {
        // 查询数据库判断openid是否已经存在
        UserEntity userEntity = userRepository.getByOpenid(userInfo.getOpenid());

        if (ObjUtil.isNull(userEntity)) {
            userEntity = new UserEntity();
        }

        userEntity.setNickname(userInfo.getNickname());
        userEntity.setOpenid(userInfo.getOpenid());
        userEntity.setSocialType(SocialTypeVO.WECHAT_MP.getType());
        userEntity.setCity(userInfo.getCity());
        userEntity.setProvince(userInfo.getProvince());
        userEntity.setCountry(userInfo.getCountry());
        userEntity.setGender(userInfo.getSex());
        userEntity.setAvatar(userInfo.getHeadImgUrl());
        userEntity.setStatus(CommonStatusVO.ENABLE.getStatus());
        userEntity.setRoleId(RoleVO.MP_USER.getCode());
        userEntity.setLoginDate(LocalDateTime.now());

        // 已经存在则更新
        if (ObjUtil.isNotNull(userEntity)) {
            userRepository.updateById(userEntity);
        }
        // 不存在插入
        else {
            userRepository.insert(userEntity);
        }
    }

    @Override
    public void bindCourier(String openid, String mobileNumber) {
        // 通过手机号查询账户
        UserEntity userEntity = userRepository.getByMobileNumber(mobileNumber);
        if (ObjUtil.isNull(userEntity)) {
            throw new ServiceException("没有查询到该手机号码");
        }
        if (!userEntity.ifCourier()) {
            throw new ServiceException("该用户不是快递员");
        }
        userEntity.setOpenid(openid);
        userRepository.updateById(userEntity);
    }

    @Override
    public void remark(UserRemarkReq req) {
        UserEntity userEntity = userRepository.getById(req.getUserId());
        userEntity.setRemark(req.getRemark());
        userRepository.updateById(userEntity);
    }
}
