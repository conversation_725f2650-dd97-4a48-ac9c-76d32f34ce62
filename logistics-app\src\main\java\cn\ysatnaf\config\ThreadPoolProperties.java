package cn.ysatnaf.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "async.thread-pool")
public class ThreadPoolProperties {

    private PoolConfig query;

    @Data
    public static class PoolConfig {
        private int coreSize;
        private int maxSize;
        private int queueCapacity;
        private long keepAliveSeconds;
    }
}
