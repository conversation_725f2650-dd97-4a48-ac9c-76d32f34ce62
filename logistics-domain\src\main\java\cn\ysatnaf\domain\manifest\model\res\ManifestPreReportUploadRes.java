package cn.ysatnaf.domain.manifest.model.res;

import cn.ysatnaf.domain.manifest.model.excel.ManifestPreReportRow;
import cn.ysatnaf.types.common.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "预报预览返回参数")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ManifestPreReportUploadRes {

    @Schema(description = "批次号")
    private String batchId;

    @Schema(description = "上传总数")
    private Integer uploadCount;

    @Schema(description = "未预报数据数量")
    private Integer notPreReportedCount;

    @Schema(description = "已预报数据数量")
    private Integer preReportedCount;

    @Schema(description = "错误信息")
    private List<String> errors;
}
