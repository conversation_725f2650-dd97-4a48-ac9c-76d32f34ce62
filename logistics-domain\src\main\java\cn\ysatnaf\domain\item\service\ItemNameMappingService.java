package cn.ysatnaf.domain.item.service;

import cn.ysatnaf.domain.item.model.dto.ItemNameMappingAddReq;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingDTO;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingPageReq;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingUpdateReq;
import cn.ysatnaf.types.common.PageResult; // 使用确认的路径

import java.util.Optional;

/**
 * 物品名称映射服务接口
 */
public interface ItemNameMappingService {

    /**
     * 添加物品名称映射
     * @param req 请求参数
     * @return 新增记录的ID
     */
    Long addMapping(ItemNameMappingAddReq req);

    /**
     * 更新物品名称映射
     * @param req 请求参数
     * @return 是否成功
     */
    boolean updateMapping(ItemNameMappingUpdateReq req);

    /**
     * 删除物品名称映射
     * @param id 要删除的ID
     * @return 是否成功
     */
    boolean deleteMapping(Long id);

    /**
     * 根据ID获取物品名称映射详情
     * @param id ID
     * @return DTO 或 null
     */
    ItemNameMappingDTO getMappingById(Long id);

    /**
     * 分页查询物品名称映射
     * @param req 分页及查询参数
     * @return 分页结果 DTO 列表
     */
    PageResult<ItemNameMappingDTO> pageMappings(ItemNameMappingPageReq req);

    /**
     * 根据原始物品名称查找启用的映射后的名称
     * 如果找到映射，则增加使用次数并更新最后使用时间
     * @param originalName 原始物品名称
     * @return Optional<String> 包含映射后的名称，如果未找到或未启用则为空
     */
    Optional<String> findAndUseActiveMappedName(String originalName);

    /**
     * 添加或更新物品名称映射 (Upsert)
     * 基于 originalName 判断是新增还是更新
     * @param req 包含 originalName, mappedName, isActive, remarks 的请求
     * @return 创建或更新后的映射记录的ID
     */
    Long upsertMapping(ItemNameMappingAddReq req);

} 