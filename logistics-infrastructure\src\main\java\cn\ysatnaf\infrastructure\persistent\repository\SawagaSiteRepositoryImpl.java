package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.sawaga.model.po.SawagaSitePO;
import cn.ysatnaf.domain.sawaga.repository.SawagaSiteRepository;
import cn.ysatnaf.infrastructure.persistent.dao.SawagaSiteDao;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class SawagaSiteRepositoryImpl implements SawagaSiteRepository {

    private final SawagaSiteDao sawagaSiteDao;

    @Override
    public SawagaSitePO getBySiteName(String siteName) {
        LambdaQueryWrapper<SawagaSitePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SawagaSitePO::getSiteName, siteName);
        return sawagaSiteDao.selectOne(wrapper);
    }
}
