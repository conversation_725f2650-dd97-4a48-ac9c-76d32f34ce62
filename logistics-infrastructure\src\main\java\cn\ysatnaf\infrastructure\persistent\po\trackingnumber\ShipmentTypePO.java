package cn.ysatnaf.infrastructure.persistent.po.trackingnumber;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 货物类型持久化对象
 * <AUTHOR>
 */
@Data
@TableName("shipment_types")
public class ShipmentTypePO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型代码 (例如: NORMAL, BATTERY, POSTAL)
     */
    private String code;

    /**
     * 类型名称 (例如: 普通货物, 带电货物, 投函)
     */
    private String name;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 