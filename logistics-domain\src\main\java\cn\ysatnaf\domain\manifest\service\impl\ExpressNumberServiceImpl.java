package cn.ysatnaf.domain.manifest.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.manifest.model.entity.SawagaNumberEntity;
import cn.ysatnaf.domain.manifest.service.ExpressNumberService;
import cn.ysatnaf.domain.manifest.service.SawagaNumberService;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExpressNumberServiceImpl implements ExpressNumberService {

    private final SawagaNumberService sawagaNumberService;

    private final UserService userService;

    @Override
    public String getOrderNumber(String finalSawagaNumber) {
        return "BM" + DateUtil.format(new Date(), "dd") + finalSawagaNumber;
    }

    @Override
    public String getSawagaNumber() {
        SawagaNumberEntity number = sawagaNumberService.useOne();

        return number.getExpressNumber();
    }

    @Override
    public List<String> getInternationalNumberBatch(Integer count) {
        List<SawagaNumberEntity> numbers = sawagaNumberService.useBatch(count);

        return numbers.stream().map(SawagaNumberEntity::getExpressNumber).collect(Collectors.toList());
    }

    @Override
    public Integer importSawaga(MultipartFile file) {
        List<SawagaNumberEntity> numbers = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // Assuming each line contains a single integer
                String number = line.trim();
                if (StrUtil.isBlank(number)) {
                    continue;
                }
                SawagaNumberEntity sawagaNumberEntity = new SawagaNumberEntity();
                sawagaNumberEntity.setExpressNumber(number);
                sawagaNumberEntity.setUsed(false);
                numbers.add(sawagaNumberEntity);
            }
            sawagaNumberService.insertBatch(numbers);
            return numbers.size();
        } catch (IOException | NumberFormatException e) {
            throw new ServiceException(9999, "导入失败");
        }
    }

    @Override
    public Integer countLeftSawagaNumber() {
        userService.validateAdmin();
        return sawagaNumberService.countLeftSawagaNumber();
    }

    @Override
    public void revertSawagaNumber(String expressNo) {
        sawagaNumberService.revertSawagaNumber(expressNo);
    }
}
