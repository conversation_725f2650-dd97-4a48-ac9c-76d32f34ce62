package cn.ysatnaf.domain.manifest.service.impl;

import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import cn.ysatnaf.domain.manifest.repository.ManifestItemRepository;
import cn.ysatnaf.domain.manifest.service.ManifestItemService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class ManifestItemServiceImpl implements ManifestItemService {

    private final ManifestItemRepository manifestItemRepository;

    @Override
    public void insertBatch(List<ManifestItem> manifestItems) {
        manifestItemRepository.insertBatch(manifestItems);
    }

    @Override
    public void deleteByManifestId(Long manifestId) {
        manifestItemRepository.deleteByManifestId(manifestId);
    }

    @Override
    public List<ManifestItem> listByManifestId(Long manifestId) {
        return manifestItemRepository.listByManifestId(manifestId);
    }

    @Override
    public List<ManifestItem> listBatchByManifestIds(Collection<Long> manifestIds) {
        return manifestItemRepository.listBatchByManifestIds(manifestIds);
    }

    @Override
    public void deleteByManifestIds(List<Long> deleteItemManifestIds) {
        manifestItemRepository.deleteByManifestIds(deleteItemManifestIds);
    }
}
