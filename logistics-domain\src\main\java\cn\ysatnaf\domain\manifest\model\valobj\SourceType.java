package cn.ysatnaf.domain.manifest.model.valobj;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SourceType {
    /**
     * 1-管理员录入
     * 2-本地用户录入
     * 3-网络用户录入
     * 4-微信公众号
     */

    ADMIN(1, "管理员录入"),
    LOCAL_USER(2, "本地用户录入"),
    NETWORK_USER(3, "网络用户录入"),
    WECHAT_OFFICIAL_ACCOUNT(4, "微信公众号"),
    ;

    private final Integer code;

    private final String message;
}
