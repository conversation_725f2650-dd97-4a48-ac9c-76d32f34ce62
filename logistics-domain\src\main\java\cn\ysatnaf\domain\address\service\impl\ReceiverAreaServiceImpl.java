package cn.ysatnaf.domain.address.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;
import cn.ysatnaf.domain.address.repository.ReceiverAreaRepository;
import cn.ysatnaf.domain.address.service.ReceiverAreaService;
import cn.ysatnaf.types.util.StringUtil;
import cn.ysatnaf.types.util.TranslationUtil;
import com.google.common.cache.Cache;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ReceiverAreaServiceImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 14:02
 */
@Service
@RequiredArgsConstructor
public class ReceiverAreaServiceImpl implements ReceiverAreaService {

    private final ReceiverAreaRepository receiverAreaRepository;

    private final Cache<String, List<ReceiverAreaEntity>> receiverAreaCache;

    private final TranslationUtil translationUtil;

    @Override
    public List<ReceiverAreaEntity> getByZipCode(String zipCode) {
        List<ReceiverAreaEntity> receiverAreaEntities = receiverAreaCache.getIfPresent(zipCode);
        if (CollUtil.isNotEmpty(receiverAreaEntities)) {
            return receiverAreaEntities;
        }
        receiverAreaEntities = receiverAreaRepository.getByZipCode(zipCode);
        receiverAreaCache.put(zipCode, receiverAreaEntities);
        return receiverAreaEntities;
    }

    @Override
    public ReceiverAreaEntity getReceiverArea(String zipCode, String receiverAddress) {
        List<ReceiverAreaEntity> receiverAreaEntities = getByZipCode(zipCode);
        ReceiverAreaEntity receiverAreaEntity;
        if (receiverAreaEntities.size() == 1) {
            receiverAreaEntity = receiverAreaEntities.get(0);
        } else {
            Optional<ReceiverAreaEntity> areaEntityOptional = receiverAreaEntities.stream().filter(area -> {
                String localitiesName = area.getLocalitiesName();
                localitiesName = localitiesName.replaceAll(" ", "");
                return receiverAddress.contains(localitiesName);
            }).findFirst();
            receiverAreaEntity = areaEntityOptional.orElseGet(() -> receiverAreaEntities.get(0));
        }
        return receiverAreaEntity;
    }

    @Override
    public ReceiverAreaEntity getById(Long receiverAreaId) {
        return receiverAreaRepository.getById(receiverAreaId);
    }

    @Override
    public String getReceiverEnAddress(ReceiverAreaEntity receiverAreaEntity, String receiverAddress) {
        // 截取地址得到门牌号
        String prefectureName = receiverAreaEntity.getPrefectureName()
                .replaceAll(" ", "")
                .replaceAll("　", "")
                .replaceAll("\n", " ")
                .replaceAll("\r\n", " ");
        String municipalName = receiverAreaEntity.getMunicipalName()
                .replaceAll(" ", "")
                .replaceAll("\r\n", " ")
                .replaceAll("\n", " ")
                .replaceAll("　", "");
        String localitiesName = receiverAreaEntity.getLocalitiesName()
                .replaceAll(" ", "")
                .replaceAll("\r\n", " ")
                .replaceAll("\n", " ")
                .replaceAll("　", "");
        String detail = receiverAddress
                .replaceFirst(prefectureName, "")
                .replaceFirst(municipalName, "")
                .replaceFirst(localitiesName, "")
                .replaceAll("\r\n", " ")
                .replaceAll("\n", " ")
                .replaceAll(" ", "");

        return StringUtil.toHalfWidth(receiverAreaEntity.getEnglishArea() + " " + translationUtil.translateAddressFromJapaneseToEnglish(detail));
    }

    @PostConstruct
    public void init() {
        List<ReceiverAreaEntity> receiverAreaEntities = receiverAreaRepository.findAll();
        Map<String, List<ReceiverAreaEntity>> areaEntityMap = receiverAreaEntities.stream().collect(Collectors.groupingBy(ReceiverAreaEntity::getZipCode));
        receiverAreaCache.putAll(areaEntityMap);
    }
}
