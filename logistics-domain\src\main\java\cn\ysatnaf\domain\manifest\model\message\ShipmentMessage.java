package cn.ysatnaf.domain.manifest.model.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ShipmentMessage {

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 运费
     */
    private BigDecimal shippingFee;
}
