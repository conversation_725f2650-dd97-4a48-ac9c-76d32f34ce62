package cn.ysatnaf.domain.shippingfeetemplate.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplatePO;
import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplateUserPO;
import cn.ysatnaf.domain.shippingfeetemplate.model.req.ShippingFeeTemplateBindReq;
import cn.ysatnaf.domain.shippingfeetemplate.model.req.ShippingFeeTemplateCreateReq;
import cn.ysatnaf.domain.shippingfeetemplate.model.req.ShippingFeeTemplateListReq;
import cn.ysatnaf.domain.shippingfeetemplate.model.req.ShippingFeeTemplateUpdateReq;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.domain.shippingfeetemplate.repository.ShippingFeeTemplateRepository;
import cn.ysatnaf.domain.shippingfeetemplate.service.ShippingFeeTemplateService;
import cn.ysatnaf.domain.shippingfeetemplate.service.ShippingFeeTemplateUserService;
import cn.ysatnaf.types.exception.ServiceException;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 运费模板业务层实现类
 */
@Service
@RequiredArgsConstructor
public class ShippingFeeTemplateServiceImpl implements ShippingFeeTemplateService {

    private final ShippingFeeTemplateRepository shippingFeeTemplateRepository;

    private final ShippingFeeTemplateUserService shippingFeeTemplateUserService;

    @Override
    public void create(ShippingFeeTemplateCreateReq req) {
        ShippingFeeTemplatePO shippingFeeTemplatePO = ShippingFeeTemplatePO.builder()
                .name(req.getName())
                .firstWeightPrice(req.getFirstWeightPrice())
                .firstWeightRange(req.getFirstWeightRange())
                .continuedWeightPrice(req.getContinuedWeightPrice())
                .continuedWeightInterval(req.getContinuedWeightInterval())
                .bulkCoefficient(req.getBulkCoefficient())
                .threeSidesStart(req.getThreeSidesStart())
                .type(req.getType()).build();
        shippingFeeTemplateRepository.insert(shippingFeeTemplatePO);
    }

    @Override
    public void update(ShippingFeeTemplateUpdateReq req) {
        ShippingFeeTemplatePO shippingFeeTemplatePO = ShippingFeeTemplatePO.builder()
                .id(req.getId())
                .name(req.getName())
                .firstWeightPrice(req.getFirstWeightPrice())
                .firstWeightRange(req.getFirstWeightRange())
                .continuedWeightPrice(req.getContinuedWeightPrice())
                .continuedWeightInterval(req.getContinuedWeightInterval())
                .bulkCoefficient(req.getBulkCoefficient())
                .threeSidesStart(req.getThreeSidesStart())
                .type(req.getType()).build();
        shippingFeeTemplateRepository.updateById(shippingFeeTemplatePO);
    }

    @Override
    public void delete(Long id) {
        shippingFeeTemplateRepository.deleteById(id);
    }

    @Override
    public List<ShippingFeeTemplatePO> list(ShippingFeeTemplateListReq req) {
        return shippingFeeTemplateRepository.list(req.getType());
    }

    @Override
    public ShippingFeeTemplatePO get(Long templateId, Integer type, Long userId) {
        /*
         * 如果是管理员，并且有指定ID，那么直接按照指定ID查询
         * 如果没有指定ID或者不是管理员，那么按照设定的模板来查
         */
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        ShippingFeeTemplatePO shippingFeeTemplatePO;
        // 是管理员且指定了ID
        if (loginUser.ifAdmin() && templateId != null) {
            shippingFeeTemplatePO = shippingFeeTemplateRepository.getById(templateId);
        } else {
            if (type == null) {
                throw new ServiceException("没有指定模板类型");
            }

            // 普通模版计算价格都按照GENERAL计算，投函同理
            if (type.equals(ShippingFeeTemplateTypeEnum.GENERAL_OSAKA.getCode())) {
                type = ShippingFeeTemplateTypeEnum.GENERAL.getCode();
            } else if (type.equals(ShippingFeeTemplateTypeEnum.SMALL_PARCEL_OSAKA.getCode())) {
                type = ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode();
            }
            userId = userId == null ? loginUser.getId() : userId;
            ShippingFeeTemplateUserPO shippingFeeTemplateUserPO = shippingFeeTemplateUserService.getByUserIdAndType(userId, type);
            if (shippingFeeTemplateUserPO == null) {
                throw new ServiceException("该账户还未设置该类型模板，请联系管理员进行设置");
            }
            shippingFeeTemplatePO = shippingFeeTemplateRepository.getById(shippingFeeTemplateUserPO.getTemplateId());
            if (shippingFeeTemplatePO == null) {
                throw new ServiceException("该账户设置的运费模板已失效，请联系管理员进行重新设置");
            }
        }
        return shippingFeeTemplatePO;
    }

    @Override
    public void bind(ShippingFeeTemplateBindReq req) {
        List<Long> templateIds = Lists.newArrayList(req.getGeneralTemplateId(), req.getElectronicsTemplateId(), req.getSmallParcelTemplateId());
        templateIds = templateIds.stream().filter(Objects::nonNull).collect(Collectors.toList());        // 查询模板类型
        List<ShippingFeeTemplatePO> templates = shippingFeeTemplateRepository.getByIds(templateIds);
        if (CollUtil.isEmpty(templates)) {
            throw new ServiceException("不存在的运费模板");
        }
        shippingFeeTemplateUserService.bind(templates, req.getUserId());
    }
}
