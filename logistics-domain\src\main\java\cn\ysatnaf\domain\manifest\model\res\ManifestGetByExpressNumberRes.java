package cn.ysatnaf.domain.manifest.model.res;

import cn.ysatnaf.domain.manifest.model.dto.ManifestOrderDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Schema(description = "运单信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManifestGetByExpressNumberRes {

    @Schema(description = "运单信息")
    private ManifestOrderDTO manifest;

}
