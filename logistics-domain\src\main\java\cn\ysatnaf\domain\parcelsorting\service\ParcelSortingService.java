package cn.ysatnaf.domain.parcelsorting.service;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingBoxPO;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingPackagePO;
import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingRecordPO;
import cn.ysatnaf.domain.parcelsorting.model.req.ParcelSoringUpdateReq;
import cn.ysatnaf.domain.parcelsorting.model.req.ParcelSortingBoxCreateReq;
import cn.ysatnaf.domain.parcelsorting.model.req.ParcelSortingBoxUpdateReq;
import cn.ysatnaf.domain.parcelsorting.model.req.ParcelSortingBatchBoxingReq;
import cn.ysatnaf.domain.parcelsorting.model.res.ParcelSortingRecordDetailRes;
import cn.ysatnaf.types.common.PageParam;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Hang
 */
public interface ParcelSortingService {

    /**
     * 创建分箱记录
     *
     * @param recordName   记录名称
     * @param masterBillId
     */
    void createRecord(String recordName, Long masterBillId);

    /**
     * 删除分箱记录（软删除）
     *
     * @param id 记录ID
     */
    void deleteRecord(Long id);

    /**
     * 更新分箱记录
     *
     * @param req 请求
     */
    void updateRecord(ParcelSoringUpdateReq req);

    /**
     * 记录列表
     *
     * @return 记录列表
     */
    PageResult<ParcelSortingRecordPO> listRecord(PageParam pageParam);

    void createBox(ParcelSortingBoxCreateReq req);

    void updateBox(ParcelSortingBoxUpdateReq req);

    void deleteBox(Long id);

    List<ParcelSortingBoxPO> listBox(Long recordId);

    Integer boxing(Long boxId, String expressNumber, Long masterBillId);

    Integer unboxing(Long boxId, String expressNumber);

    Boolean changeBox(List<Long> ids, Long boxId);

    List<ParcelSortingPackagePO> listPackage(Long boxId);

    void exportCNCustomsDoc(Long boxId, HttpServletResponse httpServletResponse);

    List<ParcelSortingPackagePO> listPackageByExpressNumbers(Collection<String> expressNumber);

    List<ParcelSortingBoxPO> listBoxByBoxIds(Set<Long> boxIds);

    /**
     * 装箱检测
     * @param trackingNumber 运单号
     * @param masterBillId   主提单ID
     * @return manifestId 运单ID
     */
    Long check(String trackingNumber, Long masterBillId);

    /**
     * 批量装箱
     * @param req 批量装箱请求
     */
    void batchBoxing(ParcelSortingBatchBoxingReq req);

    /**
     * 获取装箱记录详情及分箱列表
     * @param recordId 装箱记录ID
     * @return 记录详情及分箱列表
     */
    ParcelSortingRecordDetailRes getRecordDetail(Long recordId);

    void removePackage(Long boxId);

    /**
     * 根据装箱记录ID查询未装箱的包裹列表
     * @param recordId 装箱记录ID
     * @return 未装箱的 Manifest 对象列表
     */
    List<Manifest> listUnboxedManifestsByRecordId(Long recordId);
}
