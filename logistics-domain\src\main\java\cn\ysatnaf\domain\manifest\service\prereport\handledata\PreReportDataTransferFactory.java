package cn.ysatnaf.domain.manifest.service.prereport.handledata;

import cn.ysatnaf.domain.address.service.ReceiverAreaService;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportContext;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 预报单数据处理器工厂
 */
@RequiredArgsConstructor
@Component
public class PreReportDataTransferFactory {

    private final ReceiverAreaService receiverAreaService;

    /**
     * 根据用户类型获取数据处理器
     *
     * @param context             上下文
     * @return 数据处理器
     */
    public ManifestPreReportDataConverter create(ManifestPreReportContext context) {
        UserEntity user = context.getUser();

        // 网络用户
        if (user.ifNetworkUser()) {
            return new NetworkUserPreReportDataConverter(context, receiverAreaService);
        }
        // 其他用户
        return new CommonPreReportDataConverter(context, receiverAreaService);
    }
}
