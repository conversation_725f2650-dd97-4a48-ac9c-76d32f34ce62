package cn.ysatnaf.domain.image.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.ysatnaf.domain.image.service.WayBillImageService;
import cn.ysatnaf.domain.manifest.model.dto.WayBillImageGenDTO;
import cn.ysatnaf.domain.manifest.model.entity.SawagaSiteCodeEntity;
import cn.ysatnaf.domain.manifest.service.impl.DivisionCodeService;
import cn.ysatnaf.domain.manifest.service.impl.JapaneseShippingLabelGenerator;
import cn.ysatnaf.domain.manifest.service.impl.ShippingLabelGenerator;
import cn.ysatnaf.domain.manifest.service.impl.SortCodeService;
import cn.ysatnaf.domain.sawaga.service.SawagaSiteService;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.domain.site.model.po.SiteCodePO;
import cn.ysatnaf.domain.site.model.vo.SiteTypeEnum;
import cn.ysatnaf.domain.site.service.SiteCodeService;
import cn.ysatnaf.types.exception.ServiceException;
import cn.ysatnaf.types.util.BarcodeImageUtil;
import cn.ysatnaf.types.util.TranslationUtil;
import com.google.zxing.BarcodeFormat;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.geom.Line2D;
import java.awt.geom.Rectangle2D;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WayBillImageServiceImpl implements WayBillImageService {

    private final SawagaSiteService sawagaSiteService;

    private final SiteCodeService siteCodeService;

    private final TranslationUtil translationUtil;

    private final DivisionCodeService divisionCodeService;

    private final SortCodeService sortCodeService;

    @Override
    public void generateWayBillImage(HttpServletResponse httpServletResponse, WayBillImageGenDTO dto) {
        try {
            BufferedImage bufferedImage;
            if (ObjUtil.equal(dto.getShippingFeeTemplateType(), ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode())) {
                // 黑猫面单
                // 黑猫面单的订单号需要替换成SF开头
                dto.setOrderNo(dto.getOrderNo().replaceAll("BM", "SF"));
                bufferedImage = generateNekoposuWayBillImage(dto, 950, 990, 0, 15, 15);
            } else if (ObjUtil.equal(dto.getShippingFeeTemplateType(), ShippingFeeTemplateTypeEnum.GENERAL_OSAKA.getCode())) {
                // 佐川大阪面单
                JapaneseShippingLabelGenerator generator = new JapaneseShippingLabelGenerator(sawagaSiteService);
                generator.generateLabel(dto);
                bufferedImage = generator.getImage();
            } else if (ObjUtil.equal(dto.getShippingFeeTemplateType(), ShippingFeeTemplateTypeEnum.SMALL_PARCEL_OSAKA.getCode())) {
                // 投函大阪面单
                dto.setOrderNo(dto.getOrderNo().replaceAll("BM", "SF"));
                ShippingLabelGenerator shippingLabelGenerator = new ShippingLabelGenerator(sortCodeService, divisionCodeService);
                bufferedImage = shippingLabelGenerator.generate(dto);
            } else {
                // 佐川面单
                bufferedImage = generateSagawaWayBillImage(dto, 780, 1060, 20, 15, 220);
            }

            httpServletResponse.setContentType("image/png"); //通知浏览器以 jpeg 格式打开图片
            httpServletResponse.setHeader("Expires", "-1");   //联合使用以下三个响应消息头，控制浏览器不要缓存。
            httpServletResponse.setHeader("Cache-Control", "no-cache");
            httpServletResponse.setHeader("Pragma", "no-cache");
            ImageIO.write(bufferedImage, "png", httpServletResponse.getOutputStream()); //将图片打给浏览器。
        } catch (Exception e) {
            log.error("生成面单失败: ", e);
            throw new ServiceException("生成面单失败");
        }
    }

    private BufferedImage generateNekoposuWayBillImage(WayBillImageGenDTO dto, int width, int height, int cornerRadius, int margin, int marginBottom) throws Exception {
        BufferedImage image = new BufferedImage(width + 2 * margin, height + margin + marginBottom, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();

        // 启用反锯齿以获得更平滑的边缘
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 设置带有页边距的背景颜色-白色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width + 2 * margin, height + 2 * marginBottom);

        // 创建带有页边距的圆角矩形
        RoundRectangle2D roundedRectangle = new RoundRectangle2D.Float(margin, margin / 2, width, height, cornerRadius, cornerRadius);

        // 绘制带有页边距的圆角矩形边框，并使外框加粗
        // 边框为黑色
        g2d.setColor(Color.BLACK);

        // 定义线条粗细和外边框粗细
        float commonStrokeWidth = 1.2f;
        BasicStroke commonStroke = new BasicStroke(commonStrokeWidth);

        // 设置外框的粗细
        float strokeWidth = 2.0f;
        BasicStroke stroke = new BasicStroke(strokeWidth);
        // 边框加粗
        g2d.setStroke(stroke);
        g2d.draw(roundedRectangle);

        // 重置边框粗细
        g2d.setStroke(commonStroke);

        // 第一行 -----------------------------------------------------------------------------------------------------

        // ネコポス
        g2d.setColor(Color.BLACK);
        g2d.fillRect(margin + 6, margin + 4, 150, 36);
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("微软雅黑", Font.ITALIC, 35));
        g2d.drawString("ネコポス", margin + 10, margin + 35);

        // 头部条形码
        g2d.setColor(Color.BLACK);
        // 店码
        SiteCodePO siteCodePO = siteCodeService.getByZipCodeAndType(dto.getReceiverZipCode(), SiteTypeEnum.NEKOPOSU.getValue());
        String siteCode = siteCodePO.getSiteCode();
        siteCode = siteCode.substring(0, 2) + "-" + siteCode.substring(2, 4) + "-" + siteCode.substring(4, 6);
        try {
            String barcodeSiteCode = "0" + siteCodePO.getSiteCode() + "0";
            BufferedImage bufferedImage = BarcodeImageUtil.generateBarcodeImage(barcodeSiteCode, BarcodeFormat.CODE_128, 320, 75);
            g2d.drawImage(bufferedImage, 160, margin + 4, null);
        } catch (Exception e) {
            log.error("生成【店铺番号】条形码失败:", e);
            throw e;
        }
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 22));
        g2d.drawString("〒 " + dto.getFormattedZipCode(), margin + 350, margin + 105);

        // 送り状番号
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 18));
        g2d.drawString("送り状番号", margin + 780, margin + 110);
        // 6542-4410-0322
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 22));
        String expressNumber = dto.getSawagaNumber();
        expressNumber = expressNumber.substring(0, 4) + "-" + expressNumber.substring(4, 8) + "-" + expressNumber.substring(8);
        g2d.drawString(expressNumber, margin + 740, margin + 135);
        // 受付日
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 18));
        String date = DateUtil.format(new Date(), "yyyy/M/dd");
        g2d.drawString("受付日        " + date, margin + 740, margin + 160);

        BufferedImage bi;
        try (InputStream baseImageStream = new ClassPathResource("images/NekoposuLogo1.png").getInputStream()) {
            bi = ImageIO.read(baseImageStream);
        }

        g2d.drawImage(bi, margin + 760, margin + 200, 180, 60, null);
        g2d.setColor(Color.BLACK);

        // 店码
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 70));
        g2d.drawString(siteCode, 630, margin + 70);

        // お届け先情報
        g2d.fillRect(margin + 30, margin + 90, 30, 170);
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 22));
        g2d.drawString("お", margin + 35, margin + 115);
        g2d.drawString("届", margin + 35, margin + 140);
        g2d.drawString("け", margin + 35, margin + 165);
        g2d.drawString("先", margin + 35, margin + 190);
        g2d.drawString("情", margin + 35, margin + 215);
        g2d.drawString("報", margin + 35, margin + 240);

        // 收件人
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 22));
        g2d.drawString(dto.getReceiverName(), margin + 65, margin + 140);
        // 收件地址
        for (int i = 0; ; i++) {
            int lineLength = 25;
            String subAddress = dto.getReceiverAddress().substring(i * lineLength, Math.min(dto.getReceiverAddress().length(), (i + 1) * lineLength));
            g2d.drawString(subAddress, margin + 65, margin + 170 + i * lineLength);
            if (subAddress.length() < lineLength) {
                break;
            }
        }
        g2d.drawString(dto.getReceiverName() + "                                                様", margin + 65, margin + 250);
        // 第一条横线
        Line2D line = new Line2D.Float(margin, margin + 260, width + margin, margin + 260);
        g2d.draw(line);
        // ご依頼主
        g2d.setColor(Color.BLACK);
        g2d.drawRect(margin + 30, margin + 270, 30, 140);
        g2d.setColor(Color.BLACK);
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 22));
        g2d.drawString("ご", margin + 35, margin + 300);
        g2d.drawString("依", margin + 35, margin + 325);
        g2d.drawString("頼", margin + 35, margin + 350);
        g2d.drawString("主", margin + 35, margin + 375);
        // 依赖主地址
        g2d.drawString("ZEBRA LOGISTIC                            〒 -", margin + 65, margin + 300);
        // 电话
        g2d.drawString("TEL:090-2518-8701", margin + 65, margin + 360);
        // この荷物は郵便物ではありません
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 18));
        g2d.drawString("この荷物は郵便物", margin + 780, margin + 290);
        g2d.drawString("ではありません", margin + 790, margin + 310);
        // 第二条横线
        Line2D line2 = new Line2D.Float(margin + 35, margin + 410, width + margin - 200, margin + 410);
        g2d.draw(line2);

        // 品名
        g2d.drawRect(margin + 30, margin + 420, 30, 70);
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 22));
        g2d.drawString("品", margin + 35, margin + 450);
        g2d.drawString("名", margin + 35, margin + 475);
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 18));
        g2d.drawString(dto.getItemEnNames(), margin + 65, margin + 460);
        // 第三条横线
        Line2D line3 = new Line2D.Float(margin + 35, margin + 490, width + margin - 200, margin + 490);
        g2d.draw(line3);

        // 記事
        g2d.drawRect(margin + 30, margin + 500, 30, 70);
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 22));
        g2d.drawString("記", margin + 35, margin + 530);
        g2d.drawString("事", margin + 35, margin + 555);
        // 第4条横线
        Line2D line4 = new Line2D.Float(margin + 35, margin + 570, width + margin - 200, margin + 570);
        g2d.draw(line4);

        // 送り状使用期限
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 18));
        g2d.drawString("送り状使用期限", margin + 25, margin + 600);
        // 発店コード 132-600
        g2d.drawString("発店コード 132-600", margin + 55, margin + 625);
        // お問い合わせ先0120-11-8010
        g2d.drawString("お問い合わせ先0120-11-8010", margin + 25, margin + 650);
        // ヤマ卜運输株式会社
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 18));
        g2d.drawString("ヤマ卜運輸株式会社", margin + 60, margin + 675);

        // 黑猫单号条形码
        String expressNo = "a" + dto.getSawagaNumber() + "a";
        g2d.setColor(Color.BLACK);
        try {
            // a654244100322a
            BufferedImage bufferedImage = BarcodeImageUtil.generateBarcodeImage(expressNo, BarcodeFormat.CODABAR, 400, 75);
            g2d.drawImage(bufferedImage, margin + 350, margin + 620, null);
        } catch (Exception e) {
            log.error("生成【店铺番号】条形码失败:", e);
            throw e;
        }
        // 黑猫单号
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 22));
        g2d.drawString(expressNo, margin + 450, margin + 720);

        // 斑马单号条形码
        g2d.setColor(Color.BLACK);
        try {
            BufferedImage bufferedImage = BarcodeImageUtil.generateBarcodeImage(dto.getOrderNo(), BarcodeFormat.CODE_128, 400, 75);
            g2d.drawImage(bufferedImage, margin + 60, margin + 800, null);
        } catch (Exception e) {
            log.error("生成【店铺番号】条形码失败:", e);
            throw e;
        }
        // 斑马单号
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 22));
        g2d.drawString(dto.getOrderNo(), margin + 160, margin + 900);
        // Save the image to a file

        // Dispose of the graphics context to free up resources
        g2d.dispose();
        return image;

    }

    private BufferedImage generateSagawaWayBillImage(WayBillImageGenDTO dto, int width, int height, int cornerRadius, int margin, int marginBottom) throws Exception {
        BufferedImage image = new BufferedImage(width + 2 * margin, height + margin + marginBottom, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();

        // 启用反锯齿以获得更平滑的边缘
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 设置带有页边距的背景颜色-白色
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, width + 2 * margin, height + margin + marginBottom);

        // 创建带有页边距的圆角矩形
        RoundRectangle2D roundedRectangle = new RoundRectangle2D.Float(margin, margin, width, height, cornerRadius, cornerRadius);

        // 绘制带有页边距的圆角矩形边框，并使外框加粗
        // 边框为黑色
        g2d.setColor(Color.BLACK);

        // 定义线条粗细和外边框粗细
        float commonStrokeWidth = 1.2f;
        BasicStroke commonStroke = new BasicStroke(commonStrokeWidth);

        // 设置外框的粗细
        float strokeWidth = 2.0f;
        BasicStroke stroke = new BasicStroke(strokeWidth);
        // 边框加粗
        g2d.setStroke(stroke);
        g2d.draw(roundedRectangle);

        // 重置边框粗细
        g2d.setStroke(commonStroke);

        // 第一行 -----------------------------------------------------------------------------------------------------
        int lineHeight = 110;
        Line2D line = new Line2D.Float(margin, margin + lineHeight, width + margin, margin + lineHeight);
        g2d.draw(line);
        // 36-702
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 72));
        List<SawagaSiteCodeEntity> sawagaSiteCode = sawagaSiteService.getByZipCode(dto.getReceiverZipCode());
        g2d.drawString(sawagaSiteCode.get(0).acquireCode(), margin + 120, margin + 80);

        // 第一条竖线
        int verticalLineX = 460 + margin;
        Line2D verticalLine = new Line2D.Float(verticalLineX, margin, verticalLineX, margin + lineHeight);
        g2d.draw(verticalLine);
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 24));
        g2d.drawString("元", verticalLineX + 6, margin + 50);
        g2d.drawString("払", verticalLineX + 6, margin + 80);

        // 第二条竖线
        int verticalLineX2 = 36 + verticalLineX;
        Line2D verticalLine2 = new Line2D.Float(verticalLineX2, margin, verticalLineX2, margin + lineHeight);
        g2d.draw(verticalLine2);

        // 时间、个数、no.
        int heightLineOneRight = lineHeight / 3;
        int x1 = verticalLineX2;
        int y1 = margin + heightLineOneRight;
        int x2 = margin + width;
        int y2 = y1;
        g2d.draw(new Line2D.Float(x1, y1, x2, y2));
        // 时间
        g2d.drawString(DateUtil.formatDateTime(new Date()), x1 + 30, y1 - 6);

        // 右上角第二横
        y1 += heightLineOneRight;
        y2 = y1;
        g2d.draw(new Line2D.Float(x1, y1, x2, y2));
        // 個数
        g2d.drawString("個数", x1 + 18, y1 - 8);


        // 个数的竖线
        x1 += 80;
        x2 = x1;
        y1 -= heightLineOneRight;
        g2d.draw(new Line2D.Float(x1, y1, x2, y2));
        // 1
        g2d.drawString("1", x1 + 100, y2 - 10);

        // No.   1  /  1
        g2d.drawString("No.     1   /   1", x1 - 30, margin + lineHeight - 10);

        // 第二行 -----------------------------------------------------------------------------------------------------
        // 第二行底线
        int lineTwoY = margin + lineHeight + 340;
        Line2D lineTwo = new Line2D.Float(margin, lineTwoY, width + margin, lineTwoY);
        g2d.draw(lineTwo);

        int x = margin + 50;
        y1 = margin + lineHeight;
        y2 = lineTwoY;
        g2d.draw(new Line2D.Float(x, y1, x, y2));
        // お届先
        g2d.drawString("お", margin + 14, margin + lineHeight + 100);
        g2d.drawString("届", margin + 14, margin + lineHeight + 160);
        g2d.drawString("先", margin + 14, margin + lineHeight + 220);

        // 642-0015
        g2d.drawString(dto.getFormattedZipCode(), x + 20, margin + lineHeight + 36);
        // 和歌山县海南市且来29-17
        String receiverAddress = dto.getReceiverAddress();
        if (receiverAddress.length() > 18) {
            g2d.drawString(receiverAddress.substring(0, 18), x + 20, margin + lineHeight + 70);
            g2d.drawString(receiverAddress.substring(18), x + 20, margin + lineHeight + 102);
        } else {
            g2d.drawString(receiverAddress, x + 20, margin + lineHeight + 70);
        }

        // WAKAYAMA KEN KAINAN SHI ASSO29-17
        String receiverEnAddress = dto.getReceiverEnAddress();
        if (receiverEnAddress.length() > 38) {
            g2d.drawString(receiverEnAddress.substring(0, 38), x + 20, margin + lineHeight + 150);
            g2d.drawString(receiverEnAddress.substring(38), x + 20, margin + lineHeight + 182);
        } else {
            g2d.drawString(receiverEnAddress, x + 20, margin + lineHeight + 150);
        }

        // 井道园子
        g2d.drawString(dto.getReceiverName(), x + 20, margin + lineHeight + 226);
        // IMICHI SONOKO
        g2d.drawString(translationUtil.translateFromJapaneseToEnglish(dto.getReceiverName()), x + 20, margin + lineHeight + 260);
        // 井道园子
        g2d.drawString(dto.getReceiverName(), x + 20, margin + lineHeight + 294);
        // TEL: 09040322954
        g2d.drawString("TEL: " + dto.getReceiverPhone(), x + 20, margin + lineHeight + 328);

        // 様
        g2d.setFont(new Font("微软雅黑", Font.BOLD, 52));
        g2d.drawString("様", margin + 600, lineTwoY - 20);

        // 右上角长方形
        g2d.draw(new Rectangle2D.Float(verticalLineX2, margin + lineHeight, margin + width - verticalLineX2, 120));
        // 中间一横
        g2d.draw(new Line2D.Float(verticalLineX2, margin + lineHeight + 30, margin + width, margin + lineHeight + 30));
        // 着店バーコード
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 24));
        g2d.drawString("着店バーコード", verticalLineX2 + 50, margin + lineHeight + 26);
        // 条形码
        try {
            g2d.drawImage(BarcodeImageUtil.generateBarcodeImage(sawagaSiteCode.get(0).acquireBarcodeCode(), BarcodeFormat.CODABAR, 276, 80), verticalLineX2 + 4, margin + lineHeight + 36, null);
        } catch (Exception e) {
            log.error("生成【店铺番号】条形码失败:", e);
            throw e;
        }

        // 第三行 -----------------------------------------------------------------------------------------------------
        // 第三行底线
        int lineThreeY = lineTwoY + 140;
        Line2D lineThree = new Line2D.Float(margin, lineThreeY, width + margin, lineThreeY);
        g2d.draw(lineThree);

        // 东京营业所右侧竖线
        g2d.draw(new Line2D.Float(margin + 260, lineTwoY, margin + 260, lineThreeY));
        // 再往右的竖线
        g2d.draw(new Line2D.Float(margin + 260 + 34, lineTwoY, margin + 260 + 34, lineThreeY));

        // 問合番号
        g2d.drawString("問", margin + 260 + 6, lineTwoY + 34);
        g2d.drawString("合", margin + 260 + 6, lineTwoY + 60);
        g2d.drawString("番", margin + 260 + 6, lineTwoY + 88);
        g2d.drawString("号", margin + 260 + 6, lineTwoY + 112);
        // 东京营业所下方横线
        g2d.draw(new Line2D.Float(margin, lineTwoY + 34, margin + 260, lineTwoY + 34));
        // 東京営業所
        g2d.drawString("東京営業所", margin + 60, lineTwoY + 30);
        // TEL:0570-02-0349
        // FAX:03-3699-2861
        g2d.drawString("TEL:0570-01-0349", margin + 30, lineTwoY + 76);
        g2d.drawString("FAX:03-3640-3045", margin + 30, lineTwoY + 102);

        // 条形码 d560651240656d
        String sawagaNumber = "d" + dto.getSawagaNumber() + "d";
        try {
            g2d.drawImage(BarcodeImageUtil.generateBarcodeImage(sawagaNumber, BarcodeFormat.CODABAR, 453, 86), margin + 260 + 38, lineTwoY + 10, null);
        } catch (Exception e) {
            log.error("生成【佐川运单】条形码失败:", e);
            throw e;
        }
        g2d.drawString(sawagaNumber, margin + 430, lineTwoY + 120);

        // 第四行 -----------------------------------------------------------------------------------------------------
        // 第四行底线
        int lineFourY = lineThreeY + 140;
        Line2D lineFour = new Line2D.Float(margin, lineFourY, width + margin, lineFourY);
        g2d.draw(lineFour);

        // 第一根竖线
        g2d.draw(new Line2D.Float(margin + 40, lineThreeY, margin + 40, lineFourY));
        // ご依頼主
        g2d.drawString("ご", margin + 10, lineThreeY + 34);
        g2d.drawString("依", margin + 10, lineThreeY + 60);
        g2d.drawString("頼", margin + 10, lineThreeY + 86);
        g2d.drawString("主", margin + 10, lineThreeY + 112);

        // シノトランス エア ジャパン㈱成田空港営業所
        // 千葉県成田市古込字込前154番地4
        // 成田国際空港第1貨物代理店ビル315号
        // TEL:03-3635-8481 14273416-000
        // お問い合わせはTEL:0473-32-1183
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 22));
        g2d.drawString("“※商品内容のご確認や返品交換につい", margin + 44, lineThreeY + 30);
        g2d.drawString("ては、ご購入された通販サイト(店舗)まで", margin + 44, lineThreeY + 56);
        g2d.drawString("お問合せいただきますようお願い申し上げ", margin + 44, lineThreeY + 82);
        g2d.drawString("ます。”", margin + 44, lineThreeY + 108);

        // 第二根竖线
        g2d.draw(new Line2D.Float(margin + 520, lineThreeY, margin + 520, lineFourY));
        // 実重量
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 24));
        g2d.drawString("実重量", margin + 520 + 60, lineThreeY + 30);
        // 3
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 74));
        // packageNo
        g2d.drawString(dto.getPackageNo(), margin + 520 + 20, lineThreeY + 116);
        // 右侧实际重量下方横线
        g2d.draw(new Line2D.Float(margin + 520, lineThreeY + 34, margin + width, lineThreeY + 34));

        // 第五行 -----------------------------------------------------------------------------------------------------
        // 第五行底线
        int lineFiveY = lineFourY + 180;
        Line2D lineFive = new Line2D.Float(margin, lineFiveY, width + margin, lineFiveY);
        g2d.draw(lineFive);

        // 品名，備考
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 24));
        g2d.drawString("品", margin + 10, lineFourY + 40);
        g2d.drawString("名", margin + 10, lineFourY + 66);
        g2d.drawString("，", margin + 10, lineFourY + 92);
        g2d.drawString("備", margin + 10, lineFourY + 118);
        g2d.drawString("考", margin + 10, lineFourY + 144);
        // 左侧竖线
        g2d.draw(new Line2D.Float(margin + 40, lineFourY, margin + 40, lineFiveY));
        // Phone case, protective film
        // 1902, 19th Floor, Building B, Netcom
        // Fuzhou Sufeng Information Technology
        // TEL: 0
        g2d.drawString(dto.getItemEnNames(), margin + 48, lineFourY + 30);
//        g2d.drawString("1902, 19th Floor, Building B, Netcom", margin + 48, lineFourY + 72);
//        g2d.drawString("Fuzhou Sufeng Information Technology", margin + 48, lineFourY + 118);
//        g2d.drawString("TEL: 0", margin + 48, lineFourY + 160);

        // 第六行 -----------------------------------------------------------------------------------------------------
        // 第六行底线
        int lineSixY = lineFiveY + 100;
        Line2D lineSix = new Line2D.Float(margin, lineSixY, width + margin, lineSixY);
        g2d.draw(lineSix);

        // suika10001075
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 20));
        g2d.drawString(dto.getOrderNo(), margin + 3, lineFiveY + 60);

        // 中间竖线
        g2d.draw(new Line2D.Float(margin + 260, lineFiveY, margin + 260, lineSixY));
        // ドライバー様へ
        // 住所不明、会社名不明など配達が出来なし場合、
        // 直接弊社にお問合せ願います。
        // シノトランス エア ジャパン㈱
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 20));
        g2d.drawString("ドライバー様へ", margin + 270, lineFiveY + 26);
        g2d.drawString("住所不明、会社名不明など配達が出来なし場合、", margin + 270, lineFiveY + 48);
        g2d.drawString("直接弊社にお問合せ願います。", margin + 270, lineFiveY + 72);

        // 第七行 -----------------------------------------------------------------------------------------------------

        // suika10001075
        g2d.setFont(new Font("微软雅黑", Font.PLAIN, 20));
        g2d.drawString(dto.getOrderNo(), margin + 3, lineSixY + 30);
        // 中间竖线
        g2d.draw(new Line2D.Float(margin + 260, lineSixY, margin + 260, margin + height));

        // 最底下条形码
        try {
            g2d.drawImage(BarcodeImageUtil.generateBarcodeImage(dto.getOrderNo(), BarcodeFormat.CODE_128, 720, 120), margin + 60, margin + height + 20, null);
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, 32));
            g2d.drawString(dto.getOrderNo(), margin + 250, margin + height + 180);
        } catch (Exception e) {
            log.error("生成【自定义订单号】条形码失败:", e);
            throw e;
        }


        // Dispose of the graphics context to free up resources
        g2d.dispose();
        return image;

    }
}
