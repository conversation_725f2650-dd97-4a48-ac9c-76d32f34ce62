package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordPageReq;
import cn.ysatnaf.domain.item.model.entity.ProhibitedItemKeyword;
import cn.ysatnaf.domain.item.repository.ProhibitedItemKeywordRepository;
import cn.ysatnaf.infrastructure.persistent.converter.ProhibitedItemKeywordConverter;
import cn.ysatnaf.infrastructure.persistent.dao.ProhibitedItemKeywordDao;
import cn.ysatnaf.infrastructure.persistent.po.item.ProhibitedItemKeywordPO;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.constants.CacheConstants;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 违禁关键词仓库实现 (带 Redis 缓存)
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ProhibitedItemKeywordRepositoryImpl implements ProhibitedItemKeywordRepository {

    private final ProhibitedItemKeywordDao prohibitedItemKeywordDao;
    private final RedissonClient redissonClient;
    private final ProhibitedItemKeywordConverter converter = ProhibitedItemKeywordConverter.INSTANCE;

    // 定义缓存键和过期时间
    // private static final String ACTIVE_KEYWORDS_CACHE_KEY = CacheConstants.PROHIBITED_KEYWORDS_ACTIVE_LIST; // 原常量不存在
    private static final String ACTIVE_KEYWORDS_CACHE_KEY = "cache:prohibited_keywords:active_list"; // 直接定义缓存键
    private static final long ACTIVE_KEYWORDS_CACHE_TTL = 1; // 缓存时间
    private static final TimeUnit ACTIVE_KEYWORDS_CACHE_TTL_UNIT = TimeUnit.HOURS; // 缓存时间单位

    private void clearActiveKeywordsCache() {
        try {
            redissonClient.getBucket(ACTIVE_KEYWORDS_CACHE_KEY).delete();
            log.info("Cleared active prohibited keywords cache.");
        } catch (Exception e) {
            log.error("Error clearing active prohibited keywords cache", e);
        }
    }

    @Override
    public Long insert(ProhibitedItemKeyword keyword) {
        ProhibitedItemKeywordPO po = converter.toPO(keyword);
        int insertedRows = prohibitedItemKeywordDao.insert(po);
        boolean success = insertedRows > 0;
        if (success && Boolean.TRUE.equals(keyword.getIsActive())) {
            clearActiveKeywordsCache();
        }
        return success ? po.getId() : null;
    }

    @Override
    public boolean updateById(ProhibitedItemKeyword keyword) {
        ProhibitedItemKeyword oldKeyword = findById(keyword.getId()).orElse(null);

        ProhibitedItemKeywordPO po = converter.toPO(keyword);
        boolean success = prohibitedItemKeywordDao.updateById(po) > 0;

        if (success) {
            boolean oldIsActive = oldKeyword != null && Boolean.TRUE.equals(oldKeyword.getIsActive());
            boolean newIsActive = Boolean.TRUE.equals(keyword.getIsActive());
            if (oldIsActive != newIsActive) {
                clearActiveKeywordsCache();
            }
        }
        return success;
    }

    @Override
    public boolean deleteById(Long id) {
        ProhibitedItemKeyword oldKeyword = findById(id).orElse(null);
        boolean success = prohibitedItemKeywordDao.deleteById(id) > 0;
        if (success && oldKeyword != null && Boolean.TRUE.equals(oldKeyword.getIsActive())) {
            clearActiveKeywordsCache();
        }
        return success;
    }

    @Override
    public Optional<ProhibitedItemKeyword> findById(Long id) {
        ProhibitedItemKeywordPO po = prohibitedItemKeywordDao.selectById(id);
        return Optional.ofNullable(converter.toEntity(po));
    }

    @Override
    public Optional<ProhibitedItemKeyword> findByKeyword(String keyword) {
        LambdaQueryWrapper<ProhibitedItemKeywordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProhibitedItemKeywordPO::getKeyword, keyword);
        ProhibitedItemKeywordPO po = prohibitedItemKeywordDao.selectOne(queryWrapper);
        return Optional.ofNullable(converter.toEntity(po));
    }

    @Override
    public PageResult<ProhibitedItemKeyword> page(ProhibitedItemKeywordPageReq req) {
        Page<ProhibitedItemKeywordPO> page = new Page<>(req.getPageNo(), req.getPageSize());
        LambdaQueryWrapper<ProhibitedItemKeywordPO> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.like(StrUtil.isNotBlank(req.getKeyword()), ProhibitedItemKeywordPO::getKeyword, req.getKeyword());
        queryWrapper.eq(req.getIsActive() != null, ProhibitedItemKeywordPO::getIsActive, req.getIsActive());
        queryWrapper.orderByDesc(ProhibitedItemKeywordPO::getCreateTime);

        IPage<ProhibitedItemKeywordPO> poPage = prohibitedItemKeywordDao.selectPage(page, queryWrapper);

        if (poPage == null || poPage.getRecords() == null || poPage.getRecords().isEmpty()) {
            return PageResult.empty();
        }

        List<ProhibitedItemKeyword> entityList = converter.toEntityList(poPage.getRecords());
        return new PageResult<>(entityList, poPage.getTotal());
    }

    @Override
    public List<ProhibitedItemKeyword> listActiveKeywords() {
        RBucket<String> bucket = redissonClient.getBucket(ACTIVE_KEYWORDS_CACHE_KEY);
        String cachedJson = bucket.get();

        if (StrUtil.isNotBlank(cachedJson)) {
            try {
                List<ProhibitedItemKeyword> cachedList = JSON.parseArray(cachedJson, ProhibitedItemKeyword.class);
                log.debug("Cache hit for active prohibited keywords.");
                return cachedList != null ? cachedList : Collections.emptyList();
            } catch (Exception e) {
                log.error("Error deserializing cached active keywords, falling back to DB.", e);
                bucket.deleteAsync();
            }
        }

        log.debug("Cache miss for active prohibited keywords. Querying database.");
        LambdaQueryWrapper<ProhibitedItemKeywordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProhibitedItemKeywordPO::getIsActive, true);
        List<ProhibitedItemKeywordPO> poList = prohibitedItemKeywordDao.selectList(queryWrapper);

        List<ProhibitedItemKeyword> entityList;
        if (CollUtil.isEmpty(poList)) {
            entityList = Collections.emptyList();
        } else {
            entityList = converter.toEntityList(poList);
        }

        try {
            String jsonToCache = JSON.toJSONString(entityList != null ? entityList : Collections.emptyList());
            bucket.set(jsonToCache, ACTIVE_KEYWORDS_CACHE_TTL, ACTIVE_KEYWORDS_CACHE_TTL_UNIT);
            log.info("Cached active prohibited keywords. TTL: {} {}", ACTIVE_KEYWORDS_CACHE_TTL, ACTIVE_KEYWORDS_CACHE_TTL_UNIT);
        } catch (Exception e) {
            log.error("Error caching active prohibited keywords", e);
        }

        return entityList != null ? entityList : Collections.emptyList();
    }
} 