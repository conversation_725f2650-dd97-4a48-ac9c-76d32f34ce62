package cn.ysatnaf.infrastructure.persistent.repository.trackingnumber;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.trackingnumber.model.entity.ShipmentType;
import cn.ysatnaf.domain.trackingnumber.repository.ShipmentTypeRepository;
import cn.ysatnaf.infrastructure.persistent.converter.trackingnumber.ShipmentTypeConverter;
import cn.ysatnaf.infrastructure.persistent.dao.trackingnumber.ShipmentTypeDao;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.ShipmentTypePO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 货物类型仓库实现
 * <AUTHOR>
 */
@Repository
public class ShipmentTypeRepositoryImpl extends ServiceImpl<ShipmentTypeDao, ShipmentTypePO> implements ShipmentTypeRepository {

    private final ShipmentTypeConverter converter = ShipmentTypeConverter.INSTANCE;

    @Override
    public ShipmentType findById(Long id) {
        ShipmentTypePO po = getById(id);
        return converter.toEntity(po);
    }

    @Override
    public ShipmentType findByCode(String code) {
        LambdaQueryWrapper<ShipmentTypePO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ShipmentTypePO::getCode, code).last("LIMIT 1");
        ShipmentTypePO po = getOne(queryWrapper);
        return converter.toEntity(po);
    }

    @Override
    public List<ShipmentType> findAllActive() {
        LambdaQueryWrapper<ShipmentTypePO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ShipmentTypePO::getIsActive, true);
        List<ShipmentTypePO> poList = list(queryWrapper);
        return converter.toEntityList(poList);
    }

    @Override
    public ShipmentType save(ShipmentType shipmentType) {
        ShipmentTypePO po = converter.toPO(shipmentType);
        saveOrUpdate(po);
        return converter.toEntity(po);
    }

    @Override
    public boolean deleteById(Long id) {
        // Consider implementing logical delete if needed
        return removeById(id);
    }
    
    @Override
    public List<ShipmentType> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<ShipmentTypePO> poList = listByIds(ids);
        return converter.toEntityList(poList);
    }
}
