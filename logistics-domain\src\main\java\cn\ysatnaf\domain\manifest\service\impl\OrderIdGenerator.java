package cn.ysatnaf.domain.manifest.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

public class OrderIdGenerator {
    // 基准时间（2020-01-01 00:00:00 UTC）
    private static final long START_TIME = LocalDateTime.of(2020, 1, 1, 0, 0)
            .toInstant(ZoneOffset.UTC).toEpochMilli();

    // 各部分的位数分配
    private static final int TIMESTAMP_BITS = 30;  // 时间差占30位（约34年）
    private static final int MACHINE_ID_BITS = 5;  // 机器ID占5位（支持32台机器）
    private static final int SEQUENCE_BITS = 8;    // 序列号占8位（每毫秒256个）

    // 最大值计算
    private static final long MAX_MACHINE_ID = (1L << MACHINE_ID_BITS) - 1;
    private static final long MAX_SEQUENCE = (1L << SEQUENCE_BITS) - 1;

    private final long machineId;    // 机器标识
    private long lastTimestamp = -1; // 上次生成时间
    private long sequence = 0;       // 序列号

    public OrderIdGenerator(long machineId) {
        if (machineId < 0 || machineId > MAX_MACHINE_ID) {
            throw new IllegalArgumentException("机器ID超出范围 (0 <= id <= " + MAX_MACHINE_ID + ")");
        }
        this.machineId = machineId;
    }

    public synchronized String generate() {
        long currentTimestamp = getCurrentTimestamp();

        if (currentTimestamp < lastTimestamp) {
            throw new RuntimeException("时钟回拨异常");
        }

        // 处理同一毫秒的并发
        if (currentTimestamp == lastTimestamp) {
            sequence = (sequence + 1) & MAX_SEQUENCE;
            if (sequence == 0) { // 当前毫秒序列号用尽
                currentTimestamp = waitNextTimestamp(currentTimestamp);
            }
        } else {
            sequence = 0;
        }

        lastTimestamp = currentTimestamp;

        // 组合各部分生成ID
        long id = (currentTimestamp << (MACHINE_ID_BITS + SEQUENCE_BITS))
                | (machineId << SEQUENCE_BITS)
                | sequence;

        // 通过取模运算确保最终数值为13位
        return String.format("%013d", id % 10_000_000_000_000L);
    }

    // 获取当前相对时间戳
    private long getCurrentTimestamp() {
        long timestamp = System.currentTimeMillis() - START_TIME;
        if (timestamp < 0) {
            throw new RuntimeException("基准时间设置错误");
        }
        return timestamp;
    }

    // 等待下一时间戳
    private long waitNextTimestamp(long currentTimestamp) {
        long timestamp = getCurrentTimestamp();
        while (timestamp <= currentTimestamp) {
            timestamp = getCurrentTimestamp();
        }
        return timestamp;
    }

    public static void main(String[] args) {
        // 使用示例：不同服务实例分配不同machineId
        OrderIdGenerator generator = new OrderIdGenerator(1);
        System.out.println(generator.generate());
    }
}