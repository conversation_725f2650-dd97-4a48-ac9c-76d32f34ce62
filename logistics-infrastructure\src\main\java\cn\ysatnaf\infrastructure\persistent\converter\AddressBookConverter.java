package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.address.model.entity.AddressBookEntity;
import cn.ysatnaf.infrastructure.persistent.po.AddressBookPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * AddressBookConverter
 *
 * <AUTHOR>
 * @date 2023/12/22 10:50
 */
@Mapper
public interface AddressBookConverter {
    AddressBookConverter INSTANCE = Mappers.getMapper(AddressBookConverter.class);

    AddressBookPO entity2po(AddressBookEntity entity);

    AddressBookEntity po2entity(AddressBookPO po);

    List<AddressBookEntity> po2entityList(List<AddressBookPO> addressBookPOList);

    List<AddressBookPO> entity2poList(List<AddressBookEntity> bookEntities);
}
