package cn.ysatnaf.domain.manifest.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;
import cn.ysatnaf.domain.address.service.ReceiverAreaService;
import cn.ysatnaf.domain.manifest.service.ManifestDataValidateService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@RequiredArgsConstructor
@Service
public class ManifestDataValidateServiceImpl implements ManifestDataValidateService {

    private final ReceiverAreaService receiverAreaService;
    @Override
    public String formatAndValidateJpMobile(String receiverPhone) {
        // 去掉带“-”的号码
        receiverPhone = receiverPhone.replaceAll("-", "").replaceAll(" ", "");
        /*
         * "注：日本手机号码只有4中开头，080、090、070、050，一共11位。
         * 没有手机只有座机的客人，座机号是0开头的10位号码。
         * "
         */
        boolean isPhone = StrUtil.startWithAny(receiverPhone, "080", "090", "070", "050")
                && StrUtil.length(receiverPhone) == 11;
        if (isPhone) {
            return receiverPhone;
        }
        boolean isTel = StrUtil.startWith(receiverPhone, "0") && StrUtil.length(receiverPhone) == 10;
        if (isTel) {
            return receiverPhone;
        }
        return null;
    }

    @Override
    public Boolean validateJpZipCode(String zipCode) {
        List<ReceiverAreaEntity> areaEntities = receiverAreaService.getByZipCode(zipCode);
        return CollUtil.isNotEmpty(areaEntities);
    }
}
