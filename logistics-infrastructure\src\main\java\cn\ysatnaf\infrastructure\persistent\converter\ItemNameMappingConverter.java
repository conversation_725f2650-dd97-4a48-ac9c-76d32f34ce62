package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.item.model.entity.ItemNameMapping;
import cn.ysatnaf.infrastructure.persistent.po.item.ItemNameMappingPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 物品名称映射 PO 与 Domain Entity 转换器
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ItemNameMappingConverter {

    ItemNameMappingConverter INSTANCE = Mappers.getMapper(ItemNameMappingConverter.class);

    ItemNameMappingPO toPO(ItemNameMapping entity);

    ItemNameMapping toEntity(ItemNameMappingPO po);

    List<ItemNameMapping> toEntityList(List<ItemNameMappingPO> poList);

} 