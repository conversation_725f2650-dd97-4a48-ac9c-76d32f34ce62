# 斑马物巢系统

默认 Swagger 地址：http://localhost:8091/zebraExpressHub/swagger-ui/index.html

# Manifest 关联预报批次功能实现

## 功能说明

在 confirmPreReport 时，将创建的 manifest 记录与预报批次(pre_registration_batch)建立关联关系，以便后续可以通过批次 ID 查询相关的 manifest 记录。

## 修改内容

1. 实体类修改

   - 在`Manifest.java`中添加`preRegistrationBatchId`字段
   - 在`ManifestPO.java`中添加`preRegistrationBatchId`字段，并添加`@TableField("pre_registration_batch_id")`注解

2. 业务逻辑修改

   - 在`ManifestPreReportServiceImpl.handlePreReportData`方法中，为新创建和更新的 manifest 设置预报批次 ID
   - 将 context 中的 batchId 赋值给 manifest 实体的 preRegistrationBatchId 字段

3. 数据库修改
   - 在`tb_manifest`表中添加`pre_registration_batch_id`字段，类型为 VARCHAR(32)
   - 添加索引`idx_manifest_pre_registration_batch_id`以提高查询性能

## 使用方式

当调用`confirmPreReport`接口时，系统会自动将批次 ID 与相关的 manifest 记录关联起来。
后续可通过批次 ID 查询相关的 manifest 记录。
