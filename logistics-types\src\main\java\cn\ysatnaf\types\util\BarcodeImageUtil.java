package cn.ysatnaf.types.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR> Hang
 */
public class BarcodeImageUtil {
    public static BufferedImage generateBarcodeImage(String barcode, BarcodeFormat barcodeFormat, int width, int height) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            BitMatrix bitMatrix = new MultiFormatWriter().encode(barcode, barcodeFormat, width, height);
            MatrixToImageWriter.writeToStream(bitMatrix, "png", outputStream);
            return ImageIO.read(new ByteArrayInputStream(outputStream.toByteArray()));
        }
    }
}
