package cn.ysatnaf.domain.cost.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "计价区间规则")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class CostRangeRuleDTO {

    @Schema(description = "价格区间下限")
    private BigDecimal floor;

    @Schema(description = "价格区间上限")
    private BigDecimal ceil;

    @Schema(description = "费用")
    private BigDecimal cost;
}
