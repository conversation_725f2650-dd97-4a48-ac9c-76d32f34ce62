package cn.ysatnaf.infrastructure.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * AreaPO
 *
 * <AUTHOR>
 * @date 2023/12/22 10:04
 */
@Data
@TableName("tb_area")
public class AreaPO {

    @TableId(type = IdType.INPUT)
    private Long id;

    /**
     * 名称
     */
    private String name;
    /**
     * 父级区划代码
     */
    private Long pid;
    /**
     * 省代码
     */
    private Long provinceCode;
    /**
     * 市代码
     */
    private Long cityCode;
    /**
     * 区代码
     */
    private Long districtCode;
    /**
     * 街道代码
     */
    private Long streetCode;
    /**
     * 居委/社区
     */
    private Long committeeCode;
    /**
     * 简称
     */
    private String shortName;
    /**
     * 区号
     */
    private String cityNo;
    /**
     * 纬度
     */
    private String lat;
    /**
     * 经度
     */
    private String lng;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 级别: 0-国家 ,1-省, 2-市, 3-区/县, 4-街/镇, 5-社区/居委,
     */
    private Integer level;
}
