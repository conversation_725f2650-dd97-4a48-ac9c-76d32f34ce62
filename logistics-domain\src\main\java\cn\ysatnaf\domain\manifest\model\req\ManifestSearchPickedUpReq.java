package cn.ysatnaf.domain.manifest.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 运单高级查询请求DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "运单高级查询请求")
public class ManifestSearchPickedUpReq {

    @Schema(description = "揽件日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate pickUpDate;

    @Schema(description = "日期间隔点(0-23), 例如：选择了18，那么3月2日18:00之前的记录属于3月1日，3月2日18:00到3月3日18:00之间的属于3月2日")
    private Integer timeIntervalPoint = 18;

    @Schema(description = "提单ID")
    private Long masterBillId;

    @Schema(description = "预报单号")
    private String expressNumber;

    @Schema(description = "国际物流单号")
    private String sawagaNumber;

    @Schema(description = "货物类型")
    private Integer cargoType;

    @Schema(description = "目的地")
    private Integer destination;

    @Schema(description = "页码")
    private Integer pageNo = 1;

    @Schema(description = "每页记录数")
    private Integer pageSize = 10;
} 