package cn.ysatnaf.domain.manifest.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "更新运费 入参")
@Data
public class ManifestUpdateFreightCostReq {

    @Schema(description = "运单ID")
    @NotNull
    private Long manifestId;

    @Schema(description = "基本运费")
    @NotNull
    private BigDecimal cost;

    @Schema(description = "超长费")
    private BigDecimal overLengthSurcharge = BigDecimal.ZERO;

    @Schema(description = "偏远费")
    private BigDecimal remoteAreaSurcharge = BigDecimal.ZERO;

    @Schema(description = "其他运费名称")
    private String otherCostName;

    @Schema(description = "其他运费")
    private BigDecimal otherCost;
}
