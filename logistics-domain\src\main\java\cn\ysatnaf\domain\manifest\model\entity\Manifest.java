package cn.ysatnaf.domain.manifest.model.entity;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.manifest.model.valobj.ManifestStatus;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR> Hang
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Manifest {

    private Long id;
    /**
     * 物流单号
     * 当网络用户录入时，这里填的是国内物流单号
     * 其他情况下填的都是佐川单号
     */
    private String expressNumber;

    /**
     * 佐川单号
     */
    private String sawagaNumber;

    /**
     * 自定义订单号
     */
    private String orderNumber;

    /**
     * 转单号
     */
    private String transferredTrackingNumber;

    /**
     * 系统订单号
     */
    private String orderNo;

    /**
     * 运单维度重量
     */
    private BigDecimal weight;

    /**
     * 运单维度长宽高
     */
    private BigDecimal length;

    private BigDecimal width;

    private BigDecimal height;
    /**
     * M3/KG
     * 运单维度体积重量
     */
    private BigDecimal dimensionalWeight;

    /**
     * 真正扣除的费用，通过重量、体积根据运费模板计算出来的
     * 在导出日本海关文件的时候会对对应账户进行响应的扣费
     */
    private BigDecimal cost;

    /**
     * 超长费
     */
    private BigDecimal overLengthSurcharge;

    /**
     * 偏远费
     */
    private BigDecimal remoteAreaSurcharge;

    private String otherCostName;

    private BigDecimal otherCost;

    /**
     * 随机生成1000-6000之间，只用于海关文件显示
     */
    private BigDecimal value;

    /**
     * 商品描述，把该运单下的所有商品英文名使用","拼接
     */
    private String description;

    /**
     * PHONE NO.
     * 收件人电话号码
     * "注：日本手机号码只有4中开头，080、090、070、050，一共11位。
     * 没有手机只有座机的客人，座机号是0开头的10位号码。
     * "
     */
    private String receiverPhone;

    /**
     * Cnee Zip
     * 收件人邮编
     * 一律为7位数字
     */
    private String receiverZipCode;

    /**
     * Cnee Address
     */
    private String receiverAddress;

    /**
     * Cnee Name
     * 收件人名
     */
    private String receiverName;

    /**
     * CNEE COMPANY'S NAME
     * 收件人
     * 卖家人名只能用英文写法（日文名需要转换成罗马拼音）
     */
    private String receiverEnName;

    /**
     * CNEE ADDRESS
     * 收件地址 如：福岡県 久留米市 山川神代1-1-3
     */
    private String receiverEnAddress;

    /**
     * 日本都道府县级名称
     */
    private String prefectureName;
    /**
     * 日本市区町村级名称
     */
    private String municipalName;
    /**
     * 日本丁目级名称
     */
    private String localitiesName;

    /**
     * 日本都道府县级名称(英文)
     */
    private String prefectureEnName;
    /**
     * 日本市区町村级名称(英文)
     */
    private String municipalEnName;
    /**
     * 日本丁目级名称(英文)
     */
    private String localitiesEnName;

    /**
     * PackageNO
     * 保税库要求的区分装袋的号码
     */
    private String packageNo;

    /**
     * 站点番号
     */
    private String sawagaSiteCode;

    /**
     * 运单所属用户ID
     */
    private Long userId;

    /**
     * 揽件人ID
     */
    private Long pickUpBy;

    /**
     * 运单状态
     */
    private Integer status;

    /**
     * 轨迹状态
     */
    private Integer trackingStatus;

    /**
     * 是否上线
     */
    private Boolean isOnline;

    /**
     * 物流轨迹
     */
    private String logisticsJourney;

    /**
     * 轨迹变化时间
     */
    private LocalDateTime trackingUpdateTime;

    /**
     * 运单来源
     */
    private Integer sourceType;

    /**
     * 运费模板类型 {@link ShippingFeeTemplateTypeEnum#getCode()}
     */
    private Integer shippingFeeTemplateType;

    /**
     * 数据创建者ID
     */
    private Long creatorId;

    private String remark;

    private Boolean isDelete;

    private String packageNumber;

    private Long masterBillId;

    private String masterBillNumber;

    /**
     * 装箱状态 (0: 待装箱, 1: 已装箱, 2: 无需装箱 - 可选)
     */
    private Integer packingStatus;

    /**
     * 所属装箱记录ID (FK -> tb_parcel_sorting_record.id, 装箱后填充)
     */
    private Long parcelSortingRecordId;

    /**
     * 揽件时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime pickUpTime;

    /**
     * 收件时间
     */
    private LocalDateTime deliveredTime;

    /**
     * 发货时间
     */
    private LocalDateTime shipmentTime;

    private LocalDateTime createTime;

    public String getTrackingNumber() {
        if (StrUtil.isNotBlank(transferredTrackingNumber)) {
            return transferredTrackingNumber;
        }
        return sawagaNumber;
    }

    public BigDecimal getEffectiveWeight() {
        if (dimensionalWeight == null) {
            return weight;
        }
        return NumberUtil.max(weight, dimensionalWeight.setScale(2, RoundingMode.HALF_UP));
    }

    public Boolean ifPendingPickup() {
        return ObjUtil.equal(status, ManifestStatus.PENDING_PICKUP.getCode());
    }

    public Boolean ifPickedUp() {
        return status >= ManifestStatus.PICKED_UP.getCode();
    }

    public Boolean ifShipped() {
        return status > ManifestStatus.PICKED_UP.getCode();
    }

    public BigDecimal getTotalCost() {
        return Optional.ofNullable(cost).orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(overLengthSurcharge).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(remoteAreaSurcharge).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(otherCost).orElse(BigDecimal.ZERO));
    }

}
