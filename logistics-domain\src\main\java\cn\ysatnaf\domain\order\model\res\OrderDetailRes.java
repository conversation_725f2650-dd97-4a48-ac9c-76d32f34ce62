package cn.ysatnaf.domain.order.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * OrderDetailRes
 *
 * <AUTHOR> <PERSON>
 * @date 2024/2/7 15:43
 */
@Data
@Schema(description = "订单明细响应结果")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetailRes {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "快递单号")
    private String expressNo;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "物品名称")
    private String itemName;

    @Schema(description = "物品数量")
    private Integer itemQuantity;

    @Schema(description = "寄件人姓名")
    private String senderName;

    @Schema(description = "寄件人性别")
    private Integer senderGender;

    @Schema(description = "寄件人手机")
    private String senderMobileNo;

    @Schema(description = "寄件人省份")
    private String senderProvince;

    @Schema(description = "寄件人城市")
    private String senderCity;

    @Schema(description = "寄件人区县")
    private String senderDistrict;

    @Schema(description = "寄件人街道、镇")
    private String senderStreet;

    @Schema(description = "寄件人详细地址")
    private String senderAddressDetail;

    @Schema(description = "收件人省")
    private String receiverPrefecture;

    @Schema(description = "收件人市")
    private String receiverMunicipal;

    @Schema(description = "收件人区")
    private String receiverLocalities;

    @Schema(description = "收件人详细地址")
    private String receiverAddressDetail;

    @Schema(description = "收件人姓名")
    private String receiverName;

    @Schema(description = "收件人手机")
    private String receiverMobileNo;

    @Schema(description = "物品重量")
    private BigDecimal weight;

    @Schema(description = "物品长度")
    private BigDecimal length;

    @Schema(description = "物品宽度")
    private BigDecimal width;

    @Schema(description = "物品高度")
    private BigDecimal height;

    @Schema(description = "物品体积重量")
    private BigDecimal dimensionalWeight;

    @Schema(description = "基本费用")
    private BigDecimal basicFee;
}
