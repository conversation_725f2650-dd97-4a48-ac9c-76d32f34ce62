package cn.ysatnaf.domain.manifest.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("division_code_mapping")
public class DivisionCode {
    @TableId(value = "prefix", type = IdType.INPUT)
    private String prefix;
    
    @TableField("division_code")
    private String divisionCode;
}