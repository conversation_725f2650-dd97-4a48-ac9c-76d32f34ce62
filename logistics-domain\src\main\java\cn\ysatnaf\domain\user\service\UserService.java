package cn.ysatnaf.domain.user.service;

import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.model.req.*;
import cn.ysatnaf.domain.user.model.res.UserInfoRes;
import cn.ysatnaf.domain.user.model.res.UserInfoSimpleRes;
import cn.ysatnaf.types.common.PageResult;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;

import java.util.List;
import java.util.Set;

/**
 * UserService
 *
 * <AUTHOR> Hang
 * @date 2023/12/21 19:17
 */
public interface UserService {

    UserEntity findByOpenid(String openid);

    void add(UserEntity newUser);

    UserEntity findByUsername(String username);

    void add(UserAddReq req);

    void update(UserUpdateReq req);

    PageResult<UserInfoRes> page(UserPageReq username);

    UserInfoRes get(Long id);

    UserEntity getById(Long userId);

    void delete(Long id, String password);

    boolean updatePassword(UserPasswordUpdateReq req);

    void validateSuperAdmin();

    void validateAdmin();

    void edit(UserEditReq req);

    void resetPassword(ResetPasswordReq req);

    PageResult<UserInfoSimpleRes> pageUserSimple(UserPageReq req);

    List<UserEntity> listBatchByIds(Set<Long> creatorIds);

    void saveByMp(WxOAuth2UserInfo userInfo);

    void bindCourier(String openid, String mobileNumber);

    void remark(UserRemarkReq req);
}
