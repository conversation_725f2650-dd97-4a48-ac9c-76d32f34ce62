package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.manifest.model.entity.MasterBill;
import cn.ysatnaf.infrastructure.persistent.po.MasterBillPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 主提单DO与Entity转换类
 * <AUTHOR>
 */
@Mapper
public interface MasterBillConverter {

    MasterBillConverter INSTANCE = Mappers.getMapper(MasterBillConverter.class);

    /**
     * PO转Entity
     */
    MasterBill toEntity(MasterBillPO po);

    /**
     * Entity转PO
     */
    MasterBillPO toPO(MasterBill entity);

    /**
     * PO列表转Entity列表
     */
    List<MasterBill> toEntityList(List<MasterBillPO> poList);
} 