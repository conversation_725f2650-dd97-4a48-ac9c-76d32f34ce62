package cn.ysatnaf.domain.fund.adapter;

import cn.ysatnaf.domain.fund.model.entity.FundAccountEntity;
import cn.ysatnaf.domain.fund.model.res.FundAccountGetRes;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface FundAccountAdapter {
    FundAccountAdapter INSTANCE = Mappers.getMapper(FundAccountAdapter.class);

    FundAccountGetRes toRes(FundAccountEntity entity);
}
