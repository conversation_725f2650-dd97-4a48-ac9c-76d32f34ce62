package cn.ysatnaf.domain.trackingnumber.model.entity;

import cn.ysatnaf.domain.trackingnumber.model.valobj.TrackingNumberStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 预报单号池领域实体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingNumberPool {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 单号字符串
     */
    private String trackingNumber;

    /**
     * 所属渠道ID (FK -> tracking_number_channels.id)
     */
    private Long channelId;

    /**
     * 单号状态 (0: 可用/Available, 1: 已分配/Allocated)
     */
    private TrackingNumberStatus status;

    /**
     * 导入批次ID (FK -> tracking_number_import_batch)
     */
    private Long importBatchId;

    /**
     * 分配批次ID (FK -> tracking_number_allocation_batch)
     */
    private Long allocationBatchId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 将数据库存储的 Integer 转换为枚举
     * @param code 状态码
     */
    public void setStatusFromCode(Integer code) {
        this.status = TrackingNumberStatus.fromCode(code);
    }

    /**
     * 获取用于数据库存储的 Integer 状态码
     * @return 状态码
     */
    public Integer getStatusCode() {
        return this.status != null ? this.status.getCode() : null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TrackingNumberPool that = (TrackingNumberPool) o;
        // 修改 equals/hashCode 以 channelId 和 trackingNumber 为准
        return Objects.equals(channelId, that.channelId) &&
               Objects.equals(trackingNumber, that.trackingNumber);
    }

    @Override
    public int hashCode() {
        // 修改 equals/hashCode 以 channelId 和 trackingNumber 为准
        return Objects.hash(channelId, trackingNumber);
    }
} 