package cn.ysatnaf.domain.manifest.service.prereport.handledata;

import cn.ysatnaf.domain.address.service.ReceiverAreaService;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.auth.model.entity.LoginUserEntity;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.valobj.SourceType;
import cn.ysatnaf.domain.manifest.service.prereport.ManifestPreReportContext;
import cn.ysatnaf.types.exception.ServiceException;

/**
 * common data converter
 */
public class CommonPreReportDataConverter extends BasePreReportDataConverter {

    public CommonPreReportDataConverter(ManifestPreReportContext context, ReceiverAreaService receiverAreaService) {
        super.context = context;
        super.receiverAreaService = receiverAreaService;
    }

    @Override
    protected void setSourceType(Manifest manifest) {
        // 获取当前操作用户
        LoginUserEntity loginUser = LoginUserHolder.getLoginUser();
        // 判断来源类型
        SourceType sourceType;
        if (loginUser.ifAdmin()) {
            sourceType = SourceType.ADMIN;
        } else if (loginUser.ifLocalUser()) {
            sourceType = SourceType.LOCAL_USER;
        } else {
            throw new ServiceException("用户角色信息错误，请联系管理员");
        }
        manifest.setSourceType(sourceType.getCode());
    }

    @Override
    protected void setInternationalExpressNumber(Manifest manifest, String expressNumber) {
        manifest.setSawagaNumber(expressNumber);
    }
}
