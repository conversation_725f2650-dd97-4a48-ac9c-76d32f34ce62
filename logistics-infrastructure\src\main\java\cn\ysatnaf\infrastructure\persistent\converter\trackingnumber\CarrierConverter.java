package cn.ysatnaf.infrastructure.persistent.converter.trackingnumber;

import cn.ysatnaf.domain.trackingnumber.model.entity.Carrier;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.CarrierPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 承运商对象转换器
 * <AUTHOR>
 */
@Mapper
public interface CarrierConverter {

    CarrierConverter INSTANCE = Mappers.getMapper(CarrierConverter.class);

    CarrierPO toPO(Carrier carrier);

    Carrier toEntity(CarrierPO carrierPO);
    
    List<Carrier> toEntityList(List<CarrierPO> carrierPOs);
    
    List<CarrierPO> toPOList(List<Carrier> carriers);

} 