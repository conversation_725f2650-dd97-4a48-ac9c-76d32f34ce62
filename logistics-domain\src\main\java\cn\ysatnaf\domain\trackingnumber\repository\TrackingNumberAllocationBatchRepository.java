package cn.ysatnaf.domain.trackingnumber.repository;

import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberAllocationBatch;
import cn.ysatnaf.types.common.PageResult;

import java.time.LocalDateTime;

/**
 * 单号分配批次记录仓库接口
 * <AUTHOR>
 */
public interface TrackingNumberAllocationBatchRepository {

    /**
     * 保存分配批次记录
     * @param batch 分配批次实体
     * @return 保存后的实体（包含生成的ID）
     */
    TrackingNumberAllocationBatch save(TrackingNumberAllocationBatch batch);

    /**
     * 根据ID查询分配批次记录
     * @param id 批次ID
     * @return 分配批次实体，如果不存在则返回null
     */
    TrackingNumberAllocationBatch findById(Long id);
    
    /**
     * 分页查询分配批次记录
     * @param allocatorId 分配者ID (可选)
     * @param requestedLocationId 请求的地点ID (可选)
     * @param requestedShipmentTypeId 请求的货物类型ID (可选)
     * @param startTime 分配开始时间 (可选)
     * @param endTime 分配结束时间 (可选)
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    PageResult<TrackingNumberAllocationBatch> pageQuery(Long allocatorId,
                                                      Long requestedLocationId,
                                                      Long requestedShipmentTypeId,
                                                      LocalDateTime startTime,
                                                      LocalDateTime endTime,
                                                      int pageNo,
                                                      int pageSize);

    /**
     * 更新指定批次的导出状态
     * @param id 批次ID
     * @param isExported 导出状态 (true 表示已导出)
     * @return 更新的记录数 (通常是 1 或 0)
     */
    int updateExportedStatus(Long id, boolean isExported);

    /**
     * 更新指定批次的打印状态
     * @param id 批次ID
     * @param isPrinted 打印状态 (true 表示已打印)
     * @return 更新的记录数 (通常是 1 或 0)
     */
    int updatePrintedStatus(Long id, boolean isPrinted);

} 