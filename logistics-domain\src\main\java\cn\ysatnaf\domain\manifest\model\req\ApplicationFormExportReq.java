package cn.ysatnaf.domain.manifest.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "导出入区申请表 入参")
@Data
public class ApplicationFormExportReq {

    @Schema(description = "所选ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> ids;

    @Schema(description = "揽件时间范围开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickUpTimeFrom;

    @Schema(description = "揽件时间范围结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date pickUpTimeTo;

    @Schema(description = "航班日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date flightDate;

    @Schema(description = "到货日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date arrivalDate;

    @Schema(description = "航班号", defaultValue = "MF809")
    private String flightNumber = "MF809";

    @Schema(description = "目的地", defaultValue = "日本")
    private String destination = "日本";

    @Schema(description = "提单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String ladingBill;

    @Schema(description = "件数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Integer quantity;

    @Schema(description = "重量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private BigDecimal weight;


}
