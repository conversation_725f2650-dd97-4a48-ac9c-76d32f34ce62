package cn.ysatnaf.infrastructure.persistent.dao.trackingnumber;

import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberPoolPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 预报单号池数据访问接口
 * <AUTHOR>
 */
@Mapper
public interface TrackingNumberPoolDao extends BaseMapper<TrackingNumberPoolPO> {

    /**
     * 批量更新单号状态和分配批次ID
     *
     * @param ids 要更新的单号ID列表
     * @param status 新的状态
     * @param allocationBatchId 分配批次ID
     * @return 更新的记录数
     */
    int updateStatusAndAllocationBatchByIds(@Param("ids") List<Long> ids, 
                                              @Param("status") Integer status, 
                                              @Param("allocationBatchId") Long allocationBatchId);

    /**
     * 批量插入单号记录 (仅包含部分列，由 Mapper XML 定义)
     * @param list PO 列表
     * @return 影响的行数
     */
    int insertBatchSomeColumn(@Param("list") List<TrackingNumberPoolPO> list);

    /**
     * 根据渠道ID和单号列表查询存在的单号
     * @param channelId 渠道ID
     * @param trackingNumbers 单号列表
     * @return 存在的单号集合
     */
    Set<String> selectExistingNumbers(@Param("channelId") Long channelId, @Param("trackingNumbers") List<String> trackingNumbers);

    /**
     * 批量查询指定渠道列表的可用单号数量
     * @param channelIds 渠道ID集合
     * @param availableStatus 可用状态值 (例如 "AVAILABLE")
     * @return List<Map<String, Object>> 每个Map包含 channel_id 和 count
     */
    List<Map<String, Object>> countAvailableByChannelIds(@Param("channelIds") Collection<Long> channelIds, 
                                                       @Param("availableStatus") Integer availableStatus);
} 