package cn.ysatnaf.domain.problem.repository;

import cn.ysatnaf.domain.problem.model.po.ProblemManifestTicketPO;

/**
 * 问题运单处理工单表 Repository 接口
 *
 * <AUTHOR>
 */
public interface ProblemManifestTicketRepository {

    /**
     * 保存问题运单工单
     *
     * @param ticket 问题运单工单PO
     */
    void save(ProblemManifestTicketPO ticket);

    /**
     * 根据运单ID删除所有相关问题工单
     *
     * @param manifestId 运单ID
     * @return 删除的记录数
     */
    int deleteByManifestId(Long manifestId);

} 