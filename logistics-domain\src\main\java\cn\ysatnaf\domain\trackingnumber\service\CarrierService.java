package cn.ysatnaf.domain.trackingnumber.service;

import cn.ysatnaf.domain.trackingnumber.model.entity.Carrier;

import java.util.List;

/**
 * 承运商基础数据服务接口
 * <AUTHOR>
 */
public interface CarrierService {

    /**
     * 获取所有启用的承运商
     * @return 承运商列表
     */
    List<Carrier> listActiveCarriers();

    /**
     * 根据ID获取承运商
     * @param id 承运商ID
     * @return 承运商，不存在则抛出异常或返回null
     */
    Carrier getCarrierById(Long id);

    /**
     * 根据Code获取承运商
     * @param code 承运商Code
     * @return 承运商，不存在则抛出异常或返回null
     */
    Carrier getCarrierByCode(String code);
    
    // 可能还需要添加、修改、删除等管理功能

} 