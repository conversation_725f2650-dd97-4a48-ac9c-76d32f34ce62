package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.log.model.dto.ManifestOperationLogDTO;
import cn.ysatnaf.domain.log.service.ManifestOperationLogService;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 运单操作日志 Web API
 */
@Slf4j
@RestController
@RequestMapping("/api/manifests/{manifestId}/logs")
@Tag(name = "Web API - 运单操作日志", description = "提供查询运单操作日志的功能")
@RequiredArgsConstructor
public class ManifestOperationLogController {

    private final ManifestOperationLogService manifestLogService;

    @GetMapping
    @Operation(summary = "根据运单ID分页查询操作日志")
    public CommonResult<PageResult<ManifestOperationLogDTO>> listLogsByManifestId(
            @Parameter(description = "运单ID", required = true) @PathVariable Long manifestId,
            @Parameter(description = "页码", required = true) @RequestParam(defaultValue = "1") Integer pageNo,
            @Parameter(description = "每页数量", required = true) @RequestParam(defaultValue = "10") Integer pageSize) {
        log.info("请求运单操作日志：manifestId={}, pageNo={}, pageSize={}", manifestId, pageNo, pageSize);
        try {
            PageResult<ManifestOperationLogDTO> pageResult = manifestLogService.listLogsByManifestId(manifestId, pageNo, pageSize);
            return CommonResult.success(pageResult);
        } catch (Exception e) {
            log.error("查询运单操作日志失败：manifestId={}", manifestId, e);
            return CommonResult.error("查询日志失败: " + e.getMessage());
        }
    }

} 