package cn.ysatnaf.domain.manifest.repository;

import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.statistics.model.dto.*;
import cn.ysatnaf.domain.statistics.model.vo.SummaryVO;
import cn.ysatnaf.domain.statistics.model.vo.TodayVO;
import cn.ysatnaf.types.common.PageResult;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Hang
 */
public interface ManifestRepository {
    Manifest getByExpressNumber(String expressNumber);

    List<String> fuzzySearchExpressNumber(String expressNumber, Integer pageNumber, Integer pageSize);

    PageResult<Manifest> searchOrder(String keyword,
                                     String expressNumber,
                                     String sawagaNumber,
                                     String orderNumber,
                                     String orderNo,
                                     String receiverName,
                                     String itemName,
                                     Integer status,
                                     Long userId,
                                     LocalDateTime createTimeStart,
                                     LocalDateTime createTimeEnd,
                                     LocalDateTime pickUpTimeStart,
                                     LocalDateTime pickUpTimeEnd,
                                     LocalDateTime shipmentTimeStart,
                                     LocalDateTime shipmentTimeEnd,
                                     Integer pageNo,
                                     Integer pageSize, Boolean isDelete);

    void insert(Manifest manifest);

    Manifest getById(Long id);

    void updateById(Manifest manifest);

    PageResult<Manifest> searchManifest(String expressNumber, String sawagaNumber, String orderNumber, LocalDateTime createTimeStart, LocalDateTime createTimeEnd, Integer pageNo, Integer pageSize);

    List<Manifest> getByIds(Collection<Long> ids);

    List<Manifest> getByExpressNumbers(Collection<String> expressNumbers);

    void updateBatchByIds(List<Manifest> manifests);

    Manifest getByOrderNumber(String orderNumber);

    List<Manifest> findShippedManifests();

    Integer count(Long userId, Date startTime, Date endTime);

    Manifest fuzzyGetBySawagaNumber(String expressNumber);

    List<Manifest> getByUserId(Long userId);

    void deleteByIds(List<Long> manifestIds);

    PageResult<Manifest> page(Long userId, Date startTime, Date endTime, int pageIndex, int pageSize);

    List<Manifest> listByPickUpTimeAndShippingFeeTemplateType(Date pickUpTimeFrom, Date pickUpTimeTo, List<Integer> shippingFeeTemplateTypes);

    Manifest getByOrderNoOrExpressNumber(String orderNo);

    void insertBatch(List<Manifest> manifests);

    List<Manifest> findShippedManifestsByShippingFeeTemplateTypes(List<Integer> templateType);

    /**
     * 根据预报时间范围查询
     *
     * @param preReportedTimeFrom 预报时间开始
     * @param preReportedTimeTo   预报时间结束
     * @param userId
     * @return 运单列表
     */
    List<Manifest> listByPreReportedTime(Date preReportedTimeFrom, Date preReportedTimeTo, Long userId);

    /**
     * 更新运单的主提单关联
     *
     * @param manifestId 运单ID
     * @param masterBillId 主提单ID
     * @param masterBillNumber 主提单号
     * @return 是否更新成功
     */
    boolean updateMasterBill(Long manifestId, Long masterBillId, String masterBillNumber);
    
    /**
     * 通过主提单ID查询关联的运单列表
     *
     * @param masterBillId 主提单ID
     * @return 运单列表
     */
    List<Manifest> getByMasterBillId(Long masterBillId);

    SummaryVO.TotalData selectTotalSummary();

    SummaryVO.CurrentMonthData selectPeriodSummary(LocalDateTime localDateTime, LocalDateTime localDateTime1);

    TodayVO selectTodaySummary(LocalDateTime start, LocalDateTime end);

    List<TemplateStatsDTO> selectTemplateStats(LocalDateTime localDateTime, LocalDateTime localDateTime1);

    List<CompanyRankingDTO> selectCompanyRanking(LocalDateTime startDate, LocalDateTime endDate, Integer limit);

    List<AmountDistributionDTO> selectAmountDistribution();

    List<TrendDTO> selectTrendData(int startHour, LocalDateTime startDate, LocalDateTime endDate);

    Map<String, Object> selectBaseStats(LocalDateTime startTime, LocalDateTime endTime);

    Map<String, Object> selectPeakDay(LocalDateTime start, LocalDateTime end, Integer startHour);

    List<TemplateCategoryDTO> selectTemplateDistribution(LocalDateTime start, LocalDateTime end);

    List<Map<String, Object>> selectRegionalDistribution(LocalDateTime start, LocalDateTime end);

    List<MonthTrendDTO> selectTwelveMonthTrend(LocalDateTime localDateTime, LocalDateTime end, Integer startHour);

    Map<String, Object> selectAmountStats(LocalDateTime lastStart, LocalDateTime lastEnd);

    Map<String, Object> selectPeakAmountDay(LocalDateTime start, LocalDateTime end, Integer startHour);

    List<TemplateAmountDTO> selectTemplateAmountDistribution(LocalDateTime start, LocalDateTime end);

    List<Map<String, Object>> selectRegionalAmountDistribution(LocalDateTime start, LocalDateTime end);

    List<MonthAmountTrendDTO> selectAmountTrend(LocalDateTime localDateTime, LocalDateTime end, Integer startHour);

    Map<String, Object> getTodayYesterdayQuantitySummary(LocalDateTime todayStart, LocalDateTime todayEnd, LocalDateTime yesterdayStart, LocalDateTime yesterdayEnd, LocalDateTime beforeYesterdayStart, LocalDateTime beforeYesterdayEnd);

    List<Map<String, Object>> getTodayHourlyStats(LocalDateTime todayStart, LocalDateTime todayEnd, int startHour);

    List<Map<String, Object>> getTemplateQuantityDistribution(LocalDateTime todayStart, LocalDateTime todayEnd);

    Map<String, Object> getTodayYesterdayBeforeAmountSummary(LocalDateTime todayStart, LocalDateTime todayEnd, LocalDateTime yesterdayStart, LocalDateTime yesterdayEnd, LocalDateTime beforeYesterdayStart, LocalDateTime beforeYesterdayEnd);

    List<Map<String, Object>> getTodayHourlyAmountStats(LocalDateTime todayStart, LocalDateTime todayEnd, int startHour);

    List<Map<String, Object>> getTemplateAmountDistribution(LocalDateTime todayStart, LocalDateTime todayEnd);

    Map<String, Object> getPeriodSummary(LocalDateTime currentStartTime, LocalDateTime currentEndTime);

    List<Map<String, Object>> getTrendData(LocalDateTime currentStartTime, LocalDateTime currentEndTime, String finalUnit, String groupFormat);

    List<Map<String, Object>> getHeatmapData(LocalDateTime currentStartTime, LocalDateTime currentEndTime);

    List<Map<String, Object>> getTemplateDistribution(LocalDateTime currentStartTime, LocalDateTime currentEndTime);

    List<Manifest> listByMasterBillId(Long masterBillId);

    /**
     * 高级搜索运单
     * @param pickUpTimeStart 揽件日期起始
     * @param pickUpTimeEnd 揽件日期截止
     * @param masterBillId 提单ID
     * @param expressNumber 预报单号
     * @param sawagaNumber 国际物流单号
     * @param shippingFeeTemplateType 运费模板类型
     * @param pageNo 页码
     * @param pageSize 每页记录数
     * @return 分页结果
     */
    PageResult<Manifest> advancedSearch(
            LocalDateTime pickUpTimeStart,
            LocalDateTime pickUpTimeEnd,
            Long masterBillId,
            String expressNumber,
            String sawagaNumber,
            List<Integer> shippingFeeTemplateType,
            Integer pageNo,
            Integer pageSize);

    /**
     * 根据主提单ID统计待装箱 (packing_status = 0) 的运单数量
     * @param masterBillId 主提单ID
     * @return 待装箱的运单数量
     */
    Long countPendingPackingByMasterBill(Long masterBillId);

    /**
     * 根据运单ID列表查询运单信息
     * @param ids 运单ID列表
     * @return 运单列表
     */
    List<Manifest> listByIds(List<Long> ids);

    /**
     * 批量更新运单的打包状态和分箱记录ID
     * @param manifestIds 需要更新的运单ID列表
     * @param packingStatus 新的打包状态 (例如: 1 表示已打包)
     * @param parcelSortingRecordId 关联的分箱记录ID
     * @return 成功更新的记录数
     */
    int batchUpdatePackingStatusAndRecordId(@Param("manifestIds") List<Long> manifestIds, @Param("packingStatus") Integer packingStatus, @Param("parcelSortingRecordId") Long parcelSortingRecordId);

    /**
     * 根据主提单ID统计关联的运单总数（不区分状态）
     * @param masterBillId 主提单ID
     * @return 运单总数
     */
    Long countByMasterBillId(Long masterBillId);

    /**
     * 根据主提单ID和模板类型列表统计关联的运单总数
     * @param masterBillId 主提单ID
     * @param templateTypes 模板类型列表
     * @return 符合条件的运单总数
     */
    Long countByMasterBillIdAndTemplateTypes(@Param("masterBillId") Long masterBillId, @Param("templateTypes") List<Integer> templateTypes);

    void updatePackingStatusAndRecordId(Long manifestId, int packingStatus, Long parcelSortingRecordId);
}
