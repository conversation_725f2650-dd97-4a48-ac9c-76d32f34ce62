package cn.ysatnaf.domain.parcelsorting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "更新箱子入参")
@Data
public class ParcelSortingBoxUpdateReq {
    @Schema(description = "箱子ID")
    @NotNull
    private Long id;

    @Schema(description = "分箱名称")
    @NotBlank
    @Length(max = 32)
    private String boxName;

    @Schema(description = "袋号")
    @NotBlank
    @Length(max = 32)
    private String packageNumber;
}
