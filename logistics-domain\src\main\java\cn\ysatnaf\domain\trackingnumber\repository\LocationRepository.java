package cn.ysatnaf.domain.trackingnumber.repository;

import cn.ysatnaf.domain.trackingnumber.model.entity.Location;

import java.util.Collection;
import java.util.List;

/**
 * 地点/区域仓库接口
 * <AUTHOR>
 */
public interface LocationRepository {

    /**
     * 根据ID查询地点
     * @param id 地点ID
     * @return 地点实体，如果不存在则返回null
     */
    Location findById(Long id);
    
    /**
     * 根据代码查询地点
     * @param code 地点代码
     * @return 地点实体，如果不存在则返回null
     */
    Location findByCode(String code);

    /**
     * 查询所有启用的地点
     * @return 启用的地点列表
     */
    List<Location> findAllActive();

    /**
     * 保存地点（新增或更新）
     * @param location 地点实体
     * @return 保存后的地点实体（可能包含生成的ID）
     */
    Location save(Location location);

    /**
     * 根据ID删除地点（通常是逻辑删除）
     * @param id 地点ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID列表批量查询地点
     * @param ids ID 集合
     * @return 地点列表
     */
    List<Location> findByIds(Collection<Long> ids);

} 