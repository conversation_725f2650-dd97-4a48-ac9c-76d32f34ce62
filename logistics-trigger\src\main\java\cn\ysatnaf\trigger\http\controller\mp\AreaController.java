package cn.ysatnaf.trigger.http.controller.mp;

import cn.ysatnaf.domain.address.model.entity.AreaEntity;
import cn.ysatnaf.domain.address.service.AreaService;
import cn.ysatnaf.trigger.http.req.address.AreaListByPidReq;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import java.util.List;

/**
 * AddressController
 *
 * <AUTHOR>
 * @date 2023/12/22 10:13
 */
@Tag(name = "地址相关")
@RequestMapping("/mp/area")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class AreaController {

    private final AreaService areaService;

    @PostMapping("/listByPid")
    @PermitAll
    @Operation(summary = "获取地址列表")
    public CommonResult<List<AreaEntity>> listByPid(@RequestBody @Validated AreaListByPidReq req) {
        return CommonResult.success(areaService.listByPid(req.getPid()));
    }
}
