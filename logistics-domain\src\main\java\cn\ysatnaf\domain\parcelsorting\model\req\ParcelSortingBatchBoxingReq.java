package cn.ysatnaf.domain.parcelsorting.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.time.LocalDateTime;

/**
 * 批量装箱请求 DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "批量装箱请求")
public class ParcelSortingBatchBoxingReq {

    @NotNull(message = "分箱记录ID不能为空")
    @Schema(description = "分箱记录ID")
    private Long parcelSortingRecordId;

    @NotNull(message = "箱子ID不能为空")
    @Schema(description = "箱子ID")
    private Long boxId;

    @NotEmpty(message = "运单列表不能为空")
    @Schema(description = "运单列表，包含运单ID和扫描时间")
    private List<ManifestScanItem> manifestScanItems;

    @Data
    @Schema(description = "运单扫描信息")
    public static class ManifestScanItem {
        @NotNull(message = "运单ID不能为空")
        @Schema(description = "运单ID")
        private Long manifestId;

        @NotNull(message = "扫描时间不能为空")
        @Schema(description = "扫描时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime scanTime;
    }
} 