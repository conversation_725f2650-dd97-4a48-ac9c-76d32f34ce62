<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.ysatnaf.infrastructure.persistent.dao.MasterBillDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.ysatnaf.infrastructure.persistent.po.MasterBillPO">
        <id column="id" property="id"/>
        <result column="master_bill_number" property="masterBillNumber"/>
        <result column="departure_date" property="departureDate"/>
        <result column="arrival_date" property="arrivalDate"/>
        <result column="origin" property="origin"/>
        <result column="destination" property="destination"/>
        <result column="carrier_code" property="carrierCode"/>
        <result column="status" property="status"/>
        <result column="total_weight" property="totalWeight"/>
        <result column="total_volume" property="totalVolume"/>
        <result column="waybill_count" property="waybillCount"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator_id" property="creatorId"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 分页查询主提单 -->
    <select id="selectPage" resultMap="BaseResultMap">
        SELECT *
        FROM tb_master_bill
        <where>
            is_deleted = 0
            <if test="masterBillNumber != null and masterBillNumber != ''">
                AND master_bill_number LIKE CONCAT('%', #{masterBillNumber}, '%')
            </if>
            <if test="departureDateStart != null">
                AND departure_date &gt;= #{departureDateStart}
            </if>
            <if test="departureDateEnd != null">
                AND departure_date &lt;= #{departureDateEnd}
            </if>
            <if test="arrivalDateStart != null">
                AND arrival_date &gt;= #{arrivalDateStart}
            </if>
            <if test="arrivalDateEnd != null">
                AND arrival_date &lt;= #{arrivalDateEnd}
            </if>
            <if test="createTimeStart != null">
                AND create_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd}
            </if>
            <if test="origin != null and origin != ''">
                AND origin = #{origin}
            </if>
            <if test="destination != null and destination != ''">
                AND destination = #{destination}
            </if>
            <if test="carrierCode != null and carrierCode != ''">
                AND carrier_code = #{carrierCode}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 更新主提单的统计信息 -->
    <update id="updateStatistics">
        UPDATE tb_master_bill
        SET total_weight   = (SELECT IFNULL(SUM(weight), 0) FROM tb_manifest WHERE master_bill_id = #{id} AND is_deleted = 0),
            total_volume   = (SELECT IFNULL(SUM(dimensional_weight), 0) FROM tb_manifest WHERE master_bill_id = #{id} AND is_deleted = 0),
            waybill_count  = (SELECT COUNT(*) FROM tb_manifest WHERE master_bill_id = #{id} AND is_deleted = 0),
            update_time    = NOW()
        WHERE id = #{id} AND is_deleted = 0
    </update>
</mapper> 