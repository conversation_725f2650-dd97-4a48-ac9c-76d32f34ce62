package cn.ysatnaf.domain.manifest.model.req;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 主提单更新请求DTO
 * <AUTHOR>
 */
@Data
public class MasterBillUpdateReq {

    /**
     * 主键ID(必填)
     */
    @NotNull(message = "提单ID不能为空")
    private Long id;

    /**
     * 提单号/航班号
     */
    private String masterBillNumber;

    /**
     * 起飞日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime departureDate;

    /**
     * 到达日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime arrivalDate;

    /**
     * 始发地
     */
    private String origin;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 承运商代码
     */
    private String carrierCode;

    /**
     * 提单状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
} 