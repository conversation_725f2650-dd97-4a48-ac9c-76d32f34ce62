package cn.ysatnaf.domain.user.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Schema(description = "用户简单信息")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoSimpleRes {
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户名")
    private String username;
}
