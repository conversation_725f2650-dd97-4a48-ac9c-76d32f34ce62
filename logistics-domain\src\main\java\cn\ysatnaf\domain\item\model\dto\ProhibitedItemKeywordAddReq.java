package cn.ysatnaf.domain.item.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Schema(description = "添加违禁关键词请求")
public class ProhibitedItemKeywordAddReq {

    @NotBlank(message = "关键词不能为空")
    @Size(max = 100, message = "关键词长度不能超过100个字符")
    @Schema(description = "违禁关键词或短语", required = true, example = "Battery")
    private String keyword;

    @NotNull(message = "启用状态不能为空")
    @Schema(description = "是否启用", required = true, defaultValue = "true")
    private Boolean isActive = true;

    @Schema(description = "备注信息", example = "根据航空规定禁止")
    private String remarks;
} 