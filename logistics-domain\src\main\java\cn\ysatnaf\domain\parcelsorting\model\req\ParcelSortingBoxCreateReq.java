package cn.ysatnaf.domain.parcelsorting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "创建箱子入参")
@Data
public class ParcelSortingBoxCreateReq {
    @Schema(description = "分箱记录ID")
    @NotNull
    private Long recordId;

    @Schema(description = "分箱名称")
    @NotBlank
    @Length(max = 32)
    private String boxName;

    @Schema(description = "袋号")
    @NotBlank
    @Length(max = 32)
    private String packageNumber;
}
