package cn.ysatnaf.infrastructure.persistent.converter.trackingnumber;

import cn.ysatnaf.domain.trackingnumber.model.entity.TrackingNumberAllocationBatch;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.TrackingNumberAllocationBatchPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 单号分配批次记录转换器
 * <AUTHOR>
 */
@Mapper
public interface TrackingNumberAllocationBatchConverter {

    TrackingNumberAllocationBatchConverter INSTANCE = Mappers.getMapper(TrackingNumberAllocationBatchConverter.class);

    @Mappings({
        @Mapping(source = "requestedLocationId", target = "requestedLocationId"),
        @Mapping(source = "requestedShipmentTypeId", target = "requestedShipmentTypeId"),
        @Mapping(source = "customerAccountId", target = "customerAccountId"),
        @Mapping(source = "id", target = "id"),
        @Mapping(source = "allocatorId", target = "allocatorId"),
        @Mapping(source = "quantityRequested", target = "quantityRequested"),
        @Mapping(source = "quantityAllocated", target = "quantityAllocated"),
        @Mapping(source = "allocationTime", target = "allocationTime"),
        @Mapping(source = "createTime", target = "createTime"),
        @Mapping(source = "updateTime", target = "updateTime"),
        @Mapping(source = "isExported", target = "isExported"),
        @Mapping(source = "isPrinted", target = "isPrinted")
    })
    TrackingNumberAllocationBatchPO toPO(TrackingNumberAllocationBatch entity);

    @Mappings({
        @Mapping(source = "requestedLocationId", target = "requestedLocationId"),
        @Mapping(source = "requestedShipmentTypeId", target = "requestedShipmentTypeId"),
        @Mapping(source = "customerAccountId", target = "customerAccountId"),
        @Mapping(source = "id", target = "id"),
        @Mapping(source = "allocatorId", target = "allocatorId"),
        @Mapping(source = "quantityRequested", target = "quantityRequested"),
        @Mapping(source = "quantityAllocated", target = "quantityAllocated"),
        @Mapping(source = "allocationTime", target = "allocationTime"),
        @Mapping(source = "createTime", target = "createTime"),
        @Mapping(source = "updateTime", target = "updateTime"),
        @Mapping(source = "isExported", target = "isExported"),
        @Mapping(source = "isPrinted", target = "isPrinted")
    })
    TrackingNumberAllocationBatch toEntity(TrackingNumberAllocationBatchPO po);

    List<TrackingNumberAllocationBatch> toEntityList(List<TrackingNumberAllocationBatchPO> poList);

} 