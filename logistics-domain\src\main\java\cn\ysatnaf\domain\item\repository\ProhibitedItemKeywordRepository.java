package cn.ysatnaf.domain.item.repository;

import cn.ysatnaf.domain.item.model.entity.ProhibitedItemKeyword;
import cn.ysatnaf.types.common.PageResult; // 使用确认的路径
import cn.ysatnaf.domain.item.model.dto.ProhibitedItemKeywordPageReq;

import java.util.List;
import java.util.Optional;

/**
 * 违禁关键词仓库接口
 */
public interface ProhibitedItemKeywordRepository {

    /**
     * 新增
     * @param keyword 实体
     * @return ID
     */
    Long insert(ProhibitedItemKeyword keyword);

    /**
     * 根据ID更新
     * @param keyword 实体
     * @return 是否成功
     */
    boolean updateById(ProhibitedItemKeyword keyword);

    /**
     * 根据ID删除
     * @param id ID
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询
     * @param id ID
     * @return 实体Optional
     */
    Optional<ProhibitedItemKeyword> findById(Long id);

    /**
     * 根据关键词查询 (用于检查重复)
     * @param keyword 关键词
     * @return 实体Optional
     */
    Optional<ProhibitedItemKeyword> findByKeyword(String keyword);

    /**
     * 分页查询
     * @param req 查询请求参数
     * @return 分页结果
     */
    PageResult<ProhibitedItemKeyword> page(ProhibitedItemKeywordPageReq req);

    /**
     * 获取所有启用的关键词
     * @return 关键词列表
     */
    List<ProhibitedItemKeyword> listActiveKeywords();
} 