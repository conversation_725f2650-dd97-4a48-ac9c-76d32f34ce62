package cn.ysatnaf.domain.cost.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.ysatnaf.domain.cost.model.entity.CostRuleEntity;
import cn.ysatnaf.domain.cost.model.req.CostCalculateReq;
import cn.ysatnaf.domain.cost.model.req.CostRangeRuleDTO;
import cn.ysatnaf.domain.cost.model.req.CostRuleUpdateReq;
import cn.ysatnaf.domain.cost.model.res.CostCalculateRes;
import cn.ysatnaf.domain.cost.repository.CostRuleRepository;
import cn.ysatnaf.domain.cost.service.CostService;
import cn.ysatnaf.types.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class CostServiceImpl implements CostService {

    private final CostRuleRepository costRuleRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(CostRuleUpdateReq req) {
        List<CostRangeRuleDTO> priceRangeRules = req.getPriceRangeRules();
        // 校验价格区间数据是否有效，每一个区间的上限和下限是否相连
        priceRangeRules.sort(Comparator.comparing(CostRangeRuleDTO::getFloor));
        for (int i = 0; i < priceRangeRules.size() - 1; i++) {
            CostRangeRuleDTO current = priceRangeRules.get(i);
            CostRangeRuleDTO next = priceRangeRules.get(i + 1);
            // 如果前一个区间的上限跟下一个区间的下限不相等，那么说明区间无效，需要报错
            if (!(current.getCeil().compareTo(next.getFloor()) == 0)) {
                throw new ServiceException("价格区间无效，请保证每个区间的左右边界与相邻区间相连");
            }
        }

        // 删除原来的同平台的计价规则
        costRuleRepository.deleteByPlatformType(req.getPlatformType());
        List<CostRuleEntity> costRuleEntities = priceRangeRules.stream()
                .map(range -> CostRuleEntity.builder()
                        .platformType(req.getPlatformType())
                        .floor(range.getFloor())
                        .ceil(range.getCeil())
                        .cost(range.getCost())
                        .throwRatio(req.getThrowRatio()).build())
                .collect(Collectors.toList());

        costRuleRepository.insertBatch(costRuleEntities);
    }

    @Override
    public List<CostRuleEntity> list() {
        return costRuleRepository.list();
    }

    @Override
    public CostCalculateRes calculate(CostCalculateReq req) {
        // 先根据当前使用平台获取计价规则
        List<CostRuleEntity> costRuleEntities = costRuleRepository.listByPlatformType(req.getPlatformType());

        if (CollUtil.isEmpty(costRuleEntities)) {
            throw new ServiceException("未查询到计价规则, 请联系管理员进行设置");
        }

        // 体积重量
        BigDecimal dimensionalWeight = BigDecimal.ZERO;
        // 判断长宽高是否为空
        if (!ObjUtil.hasNull(req.getLength(), req.getWidth(), req.getHeight())) {
            // 如果都不为空，计算体积重量
            dimensionalWeight = req.getLength()
                    .multiply(req.getWidth())
                    .multiply(req.getHeight())
                    .divide(costRuleEntities.get(0).getThrowRatio(), 1, RoundingMode.FLOOR);
        }

        // 计算重量价格
        BigDecimal weightCost = calculateCost(req.getWeight(), costRuleEntities);
        BigDecimal dimensionalWeightCost = BigDecimal.ZERO;
        // 计算体积重量价格
        if (dimensionalWeight.compareTo(BigDecimal.ZERO) > 0) {
            dimensionalWeightCost = calculateCost(dimensionalWeight, costRuleEntities);
        }
        return CostCalculateRes.builder()
                .cost(NumberUtil.max(weightCost, dimensionalWeightCost))
                .dimensionalWeight(dimensionalWeight).build();
    }

    private static BigDecimal calculateCost(BigDecimal weight, List<CostRuleEntity> costRuleEntities) {
        return costRuleEntities.stream()
                .filter(rule ->
                        Optional.ofNullable(rule.getFloor()).orElse(BigDecimal.ZERO).compareTo(weight) < 0
                                && Optional.ofNullable(rule.getCeil()).orElse(BigDecimal.valueOf(Integer.MAX_VALUE)).compareTo(weight) >= 0)
                .findFirst()
                .orElseThrow(() -> new ServiceException("计算结果不在运费模板区间内，请联系管理员重新进行设置"))
                .getCost();
    }
}
