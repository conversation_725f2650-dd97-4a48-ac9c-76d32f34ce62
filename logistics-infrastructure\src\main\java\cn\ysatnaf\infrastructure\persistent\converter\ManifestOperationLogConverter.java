package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.log.model.entity.ManifestOperationLog;
import cn.ysatnaf.infrastructure.persistent.po.ManifestOperationLogPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 运单操作日志 PO 与领域对象转换器
 */
@Mapper
public interface ManifestOperationLogConverter {

    ManifestOperationLogConverter INSTANCE = Mappers.getMapper(ManifestOperationLogConverter.class);

    /**
     * 领域对象转 PO
     * 注意：如果 PO 中 changeDetails 是 String 类型，需要特殊处理 Map<String, Object>
     * 假设 PO 中也是 Map<String, Object> 类型，并由 MyBatis-Plus JSON 处理器处理
     */
    ManifestOperationLogPO toPO(ManifestOperationLog entity);

    /**
     * PO 转领域对象
     */
    ManifestOperationLog toEntity(ManifestOperationLogPO po);

    /**
     * PO 列表转领域对象列表
     */
    List<ManifestOperationLog> toEntityList(List<ManifestOperationLogPO> poList);

    /**
     * 领域对象列表转 PO 列表
     */
    List<ManifestOperationLogPO> toPOList(List<ManifestOperationLog> entityList);

} 