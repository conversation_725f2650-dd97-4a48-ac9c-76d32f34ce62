package cn.ysatnaf.infrastructure.persistent.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 佐川物流站点编码
 * <AUTHOR>
 */
@TableName("tb_sawaga_site_code")
@Data
public class SawagaSiteCodePO {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 编码第一部分
     */
    private String codeOne;

    /**
     * 编码第二部分
     */
    private String codeTwo;
}

