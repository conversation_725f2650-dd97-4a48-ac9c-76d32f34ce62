package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.order.model.entity.OrderDetail;
import cn.ysatnaf.domain.order.repository.OrderDetailRepository;
import cn.ysatnaf.infrastructure.persistent.converter.OrderDetailConverter;
import cn.ysatnaf.infrastructure.persistent.dao.OrderDetailDao;
import cn.ysatnaf.infrastructure.persistent.po.OrderDetailPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * OrderDetailRepositoryImpl
 *
 * <AUTHOR> Hang
 * @date 2024/2/7 14:24
 */
@Repository
@RequiredArgsConstructor
public class OrderDetailRepositoryImpl implements OrderDetailRepository {

    private final OrderDetailDao orderDetailDao;

    @Override
    public void insert(OrderDetail orderDetail) {
        orderDetailDao.insert(OrderDetailConverter.INSTANCE.entity2po(orderDetail));
    }

    @Override
    public OrderDetail getByOrderId(Long orderId) {
        LambdaQueryWrapper<OrderDetailPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderDetailPO::getOrderId, orderId);
        return OrderDetailConverter.INSTANCE.po2entity(orderDetailDao.selectOne(wrapper));
    }

    @Override
    public void updateById(OrderDetail orderDetail) {
        orderDetailDao.updateById(OrderDetailConverter.INSTANCE.entity2po(orderDetail));
    }
}
