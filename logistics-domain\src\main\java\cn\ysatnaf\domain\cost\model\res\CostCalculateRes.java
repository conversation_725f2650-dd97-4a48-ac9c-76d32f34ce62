package cn.ysatnaf.domain.cost.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "计价结果")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CostCalculateRes {

    @Schema(description = "费用")
    private BigDecimal cost;

    @Schema(description = "体积重量")
    private BigDecimal dimensionalWeight;
}
