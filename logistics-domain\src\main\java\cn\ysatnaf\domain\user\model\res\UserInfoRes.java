package cn.ysatnaf.domain.user.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "用户信息")
@Data
public class UserInfoRes {
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "角色ID：1-管理员；2-普通用户")
    private String roleId;

    @Schema(description = "用户状态：0-禁用；1-启用")
    private Integer status;

    @Schema(description = "账户ID")
    private Long accountId;

    @Schema(description = "账户余额")
    private BigDecimal balance;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "普通模板ID")
    private Long generalTemplateId;

    @Schema(description = "带电模板ID")
    private Long electronicsTemplateId;

    @Schema(description = "投函模板ID")
    private Long smallParcelTemplateId;
}
