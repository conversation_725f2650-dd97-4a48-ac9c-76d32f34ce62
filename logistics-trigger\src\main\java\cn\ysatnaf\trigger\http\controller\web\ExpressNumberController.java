package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.manifest.service.ExpressNumberService;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.security.PermitAll;

/**
 * <AUTHOR> Hang
 */
@Tag(name = "运单号管理")
@RequestMapping("/web/expressNumber")
@Validated
@Slf4j
@RestController
@RequiredArgsConstructor
public class ExpressNumberController {

    private final ExpressNumberService expressNumberService;

    @PostMapping("/import/sawaga")
    @PermitAll
    @Operation(summary = "导入佐川运单号")
    public CommonResult<Integer> importSawaga(MultipartFile file) {
        return CommonResult.success(expressNumberService.importSawaga(file));
    }

    @PostMapping("/countLeftSawagaNumber")
    @PermitAll
    @Operation(summary = "查询剩余佐川运单号数量")
    public CommonResult<Integer> countLeftSawagaNumber() {
        return CommonResult.success(expressNumberService.countLeftSawagaNumber());
    }
}
