package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.manifest.model.req.InvoiceGenerationTaskSubmitReq;
import cn.ysatnaf.domain.manifest.service.InvoiceService;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@Tag(name = "发票管理")
@RequestMapping("/web/invoice")
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
public class InvoiceController {

    private final InvoiceService invoiceService;

    @PostMapping("/submitGenerationTask")
    @Operation(summary = "提交生成发票任务")
    public CommonResult<Long> submitGenerationTask(@RequestBody @Validated InvoiceGenerationTaskSubmitReq req) {
        return CommonResult.success(invoiceService.submitGenerationTask(req));
    }

    @PostMapping("/generate/{taskId}")
    @Operation(summary = "生成发票")
    public CommonResult<Boolean> generate(@PathVariable Long taskId) {
        invoiceService.generate(taskId);
        return CommonResult.success(true);
    }
}
