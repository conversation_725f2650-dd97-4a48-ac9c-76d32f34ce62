package cn.ysatnaf.domain.excel.serviice.generator;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.ysatnaf.domain.excel.serviice.generator.converter.TokyoCustomsDocumentDataExcelConverter;
import cn.ysatnaf.domain.excel.serviice.generator.row.TokyoCustomsDocumentExcelRow;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JapaneseCustomsDocumentGenerationServiceImpl
 *
 * <AUTHOR> Hang
 * @date 2024/4/8 15:40
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TokyoCustomsDocumentGenerator implements JapanCustomsDocumentGenerator {

    private static final String TEMPLATE_PATH = "templates/TokyoCustomsDocumentExportTemplate.xlsx";

    private final TokyoCustomsDocumentDataExcelConverter converter;

    @Override
    public void generate(DocumentDataContext documentDataContext) {
        List<TokyoCustomsDocumentExcelRow> manifestExcelData = converter.convert(documentDataContext.getManifests());
        BatchInfo batchInfo = documentDataContext.getBatchInfo();
        LocalDate flightDate = batchInfo.getFlightDate();
        String flightNumber = batchInfo.getFlightNumber();
        String ladingBill = batchInfo.getLadingBill();
        HttpServletResponse response = documentDataContext.getHttpServletResponse();
        // 模板注意 用{} 来表示你要用的变量 如果本来就有"{","}" 特殊字符 用"{","}"代替
        // {} 代表普通变量 {.} 代表是list的变量
        // 填充数据
        Map<String, Object> map = new HashMap<>();
        String flightDateStr = DateUtil.format(LocalDateTime.of(flightDate, LocalTime.of(0, 0)), "yyyy.MM.dd");
        map.put("flightDate", flightDateStr);
        map.put("flightNumber", flightNumber);
        map.put("ladingBill", ladingBill);

        // 处理数据 (此处是业务需要，可以自行更改)
        //获取模板 (我是将模板放到 resource文件下面了)
//        ClassPathResource classPathResource = new ClassPathResource("/data/templates/ManifestExportTemplate.xlsx");
        try (InputStream inputStream = ResourceUtil.getStream(TEMPLATE_PATH)) {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "Manifest " + flightDateStr + " " + flightNumber;
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncodeUtil.encode(fileName) + ".xlsx");

            // 生成sheet
            WriteSheet sheet1 = EasyExcel.writerSheet(0, "Manifest").build();
            ExcelWriter write = EasyExcel.write(response.getOutputStream(), TokyoCustomsDocumentExcelRow.class)
                    .withTemplate(inputStream)
                    .build();
            // 填充数据
            write.fill(manifestExcelData, sheet1);
            write.fill(map, sheet1);
            write.close();
        } catch (IOException e) {
            log.error("导出失败：", e);
            throw new ServiceException("导出失败, 请联系管理员");
        }
    }
}
