package cn.ysatnaf.domain.manifest.repository;

import cn.ysatnaf.domain.manifest.model.entity.MasterBill;
import cn.ysatnaf.types.common.PageResult;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 主提单仓库接口
 * <AUTHOR>
 */
public interface MasterBillRepository {

    /**
     * 创建主提单
     * @param masterBill 主提单实体
     * @return 创建的主提单ID
     */
    Long insert(MasterBill masterBill);

    /**
     * 更新主提单
     * @param masterBill 主提单实体
     * @return 是否更新成功
     */
    boolean update(MasterBill masterBill);

    /**
     * 通过ID删除主提单（逻辑删除）
     * @param id 主提单ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询主提单
     * @param id 主提单ID
     * @return 主提单实体
     */
    MasterBill getById(Long id);

    /**
     * 根据提单号查询主提单
     * @param masterBillNumber 提单号
     * @return 主提单实体
     */
    MasterBill getByMasterBillNumber(String masterBillNumber);

    /**
     * 分页查询主提单列表
     * @param masterBillNumber 提单号（支持模糊查询）
     * @param departureDateStart 起飞日期起始
     * @param departureDateEnd 起飞日期截止
     * @param arrivalDateStart 到达日期起始 
     * @param arrivalDateEnd 到达日期截止
     * @param createDate 创建日期
     * @param timeIntervalPoint 日期间隔点(0-23)
     * @param origin 始发地
     * @param destination 目的地
     * @param carrierCode 承运商代码
     * @param status 提单状态
     * @param pageNo 页码
     * @param pageSize 每页记录数
     * @return 主提单分页结果
     */
    PageResult<MasterBill> page(String masterBillNumber, 
                              LocalDateTime departureDateStart, 
                              LocalDateTime departureDateEnd,
                              LocalDateTime arrivalDateStart, 
                              LocalDateTime arrivalDateEnd,
                              LocalDate createDate,
                              Integer timeIntervalPoint,
                              String origin, 
                              String destination, 
                              String carrierCode, 
                              Integer status,
                              Integer pageNo, 
                              Integer pageSize);
    
    /**
     * 更新主提单的统计信息（运单数量、总重量、总体积）
     * @param masterBillId 主提单ID
     * @return 是否更新成功
     */
    boolean updateStatistics(Long masterBillId);

    List<MasterBill> listByIds(Set<Long> masterBillIds);
}