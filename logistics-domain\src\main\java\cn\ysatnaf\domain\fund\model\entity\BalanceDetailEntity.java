package cn.ysatnaf.domain.fund.model.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class BalanceDetailEntity {
    private Long id;

    private Long accountId;

    private BigDecimal changeAmount;

    private BigDecimal originalBalance;

    private BigDecimal newBalance;

    private String remark;

    private Long manifestId;

    private Long operatorId;

    private String operator;

    private LocalDateTime createTime;
}
