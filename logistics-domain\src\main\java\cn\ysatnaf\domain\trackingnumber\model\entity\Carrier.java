package cn.ysatnaf.domain.trackingnumber.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 承运商领域实体
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Carrier {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 承运商代码 (例如: SAGAWA, YAMATO, JP_POST)
     */
    private String code;

    /**
     * 承运商名称 (例如: 佐川急便, 黑猫宅急便, 日本邮政)
     */
    private String name;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 