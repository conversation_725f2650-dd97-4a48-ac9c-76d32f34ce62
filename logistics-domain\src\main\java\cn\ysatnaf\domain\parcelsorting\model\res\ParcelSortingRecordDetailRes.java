package cn.ysatnaf.domain.parcelsorting.model.res;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingBoxPO;
// import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingRecordPO; // 不再直接引用 PO
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime; // 导入时间类型
import java.util.List;

/**
 * 装箱记录详情响应 DTO（包含实时计算的件数）
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "装箱记录详情响应")
public class ParcelSortingRecordDetailRes {

    // 从原 Record PO 中提取需要的字段
    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "记录名称")
    private String recordName;

    @Schema(description = "主提单ID")
    private Long masterBillId;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // 添加实时计算的字段
    @Schema(description = "预期总件数 (实时计算)")
    private Integer totalItemsExpected;

    @Schema(description = "已装箱件数 (实时计算)")
    private Integer itemsPackedCount;

    // 保留分箱列表
    @Schema(description = "分箱列表")
    private List<ParcelSortingBoxPO> boxes;

} 