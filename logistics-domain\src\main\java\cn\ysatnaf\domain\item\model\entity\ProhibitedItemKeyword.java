package cn.ysatnaf.domain.item.model.entity; // 假设 item 领域更合适

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 物品名称违禁关键词 领域实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProhibitedItemKeyword {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 违禁关键词或短语
     */
    private String keyword;

    /**
     * 是否启用该关键词规则 (TRUE: 启用, FALSE: 禁用)
     */
    private Boolean isActive;

    /**
     * 备注信息 (例如: 解释为什么禁用, 适用范围等)
     */
    private String remarks;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 最后更新者ID
     */
    private Long updaterId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

} 