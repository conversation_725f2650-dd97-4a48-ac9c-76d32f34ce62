package cn.ysatnaf.domain.item.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@Schema(description = "更新物品名称映射请求")
public class ItemNameMappingUpdateReq {

    @NotNull(message = "ID不能为空")
    @Schema(description = "主键ID", required = true)
    private Long id;

    // 通常不允许修改 originalName，因为它是查找键
    // 如果需要修改，需要谨慎处理唯一性约束

    @Size(max = 255, message = "映射后名称长度不能超过255个字符")
    @Schema(description = "映射后的合规物品名称 (可选)", example = "普通玩具")
    private String mappedName;

    @Schema(description = "是否启用 (可选)", example = "false")
    private Boolean isActive;

    @Schema(description = "备注信息 (可选)", example = "废弃此规则")
    private String remarks;
} 