package cn.ysatnaf.domain.parcelsorting.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Schema(description = "创建分箱记录入参")
@Data
public class ParcelSoringCreateReq {

    @Schema(description = "分箱记录名称")
    @Length(max = 32)
    private String recordName;

    @Schema(description = "提单ID")
    private Long masterBillId;
}
