package cn.ysatnaf.domain.order.model.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "搜索订单返回参数")
@Data
public class OrderSearchRes {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "寄件人城市")
    private String senderCityName;

    @Schema(description = "寄件人姓名")
    private String senderName;

    @Schema(description = "收件人城市")
    private String receiverCityName;

    @Schema(description = "收件人姓名")
    private String receiverName;

    @Schema(description = "订单状态")
    private Integer status;

    @Schema(description = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Schema(description = "订单签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deliveryTime;
}
