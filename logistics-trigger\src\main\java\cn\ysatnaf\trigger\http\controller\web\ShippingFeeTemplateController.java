package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.shippingfeetemplate.model.po.ShippingFeeTemplatePO;
import cn.ysatnaf.domain.shippingfeetemplate.model.req.*;
import cn.ysatnaf.domain.shippingfeetemplate.service.ShippingFeeTemplateService;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "运费模板模块")
@RequestMapping("/web/shippingFeeTemplate")
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
public class ShippingFeeTemplateController {

    private final ShippingFeeTemplateService shippingFeeTemplateService;

    @PostMapping("/create")
    @PermitAll
    @Operation(summary = "创建运费模板")
    public CommonResult<Boolean> create(@RequestBody @Validated ShippingFeeTemplateCreateReq req) {
        shippingFeeTemplateService.create(req);
        return CommonResult.success(true);
    }

    @PostMapping("/update")
    @PermitAll
    @Operation(summary = "更新运费模板")
    public CommonResult<Boolean> update(@RequestBody @Validated ShippingFeeTemplateUpdateReq req) {
        shippingFeeTemplateService.update(req);
        return CommonResult.success(true);
    }

    @PostMapping("/delete")
    @PermitAll
    @Operation(summary = "删除运费模板")
    public CommonResult<Boolean> delete(@RequestBody @Validated ShippingFeeTemplateDeleteReq req) {
        shippingFeeTemplateService.delete(req.getId());
        return CommonResult.success(true);
    }

    @PostMapping("/list")
    @PermitAll
    @Operation(summary = "运费模板列表")
    public CommonResult<List<ShippingFeeTemplatePO>> list(@RequestBody @Validated ShippingFeeTemplateListReq req) {
        List<ShippingFeeTemplatePO> list = shippingFeeTemplateService.list(req);
        return CommonResult.success(list);
    }

    @PostMapping("/bind")
    @PermitAll
    @Operation(summary = "绑定运费模板")
    public CommonResult<Boolean> bind(@RequestBody @Validated ShippingFeeTemplateBindReq req) {
        shippingFeeTemplateService.bind(req);
        return CommonResult.success(true);
    }
}