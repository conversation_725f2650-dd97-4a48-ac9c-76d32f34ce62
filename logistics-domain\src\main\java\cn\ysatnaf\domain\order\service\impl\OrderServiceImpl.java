package cn.ysatnaf.domain.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.address.model.entity.AddressBookEntity;
import cn.ysatnaf.domain.address.model.entity.ReceiverAddressBookEntity;
import cn.ysatnaf.domain.address.service.AddressBookService;
import cn.ysatnaf.domain.address.service.ReceiverAddressBookService;
import cn.ysatnaf.domain.manifest.service.ExpressNumberService;
import cn.ysatnaf.domain.order.adapter.OrderAdapter;
import cn.ysatnaf.domain.order.model.entity.OrderDetail;
import cn.ysatnaf.domain.order.model.entity.OrderEntity;
import cn.ysatnaf.domain.order.model.req.*;
import cn.ysatnaf.domain.order.model.res.MpOrderPageRes;
import cn.ysatnaf.domain.order.model.res.OrderDetailRes;
import cn.ysatnaf.domain.order.model.res.OrderSearchRes;
import cn.ysatnaf.domain.order.model.valobj.OrderStatusVO;
import cn.ysatnaf.domain.order.repository.OrderRepository;
import cn.ysatnaf.domain.order.service.OrderDetailService;
import cn.ysatnaf.domain.order.service.OrderService;
import cn.ysatnaf.domain.user.model.entity.UserEntity;
import cn.ysatnaf.domain.user.service.UserService;
import cn.ysatnaf.types.common.ErrorCodeConstants;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.exception.ServiceException;
import cn.ysatnaf.types.util.ServiceExceptionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {

    private final OrderRepository orderRepository;

    private final AddressBookService addressBookService;

    private final ReceiverAddressBookService receiverAddressBookService;

    private final ExpressNumberService expressNumberService;

    private final OrderDetailService orderDetailService;

    private final UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(OrderCreateReq req) {
        OrderEntity orderEntity = new OrderEntity();
        orderEntity.setOpenid(req.getOpenid());

        // todo 设置订单号
//        orderEntity.setOrderNo(expressNumberService.getOrderNumber(finalSawagaNumber));

        // 设置物品信息
        orderEntity.setItemName(req.getItemName());
        orderEntity.setItemQuantity(req.getItemQuantity());

        // 从地址簿中获取寄件人信息
        AddressBookEntity addressBookEntity = addressBookService.getById(req.getSenderAddressBookId());
        if (addressBookEntity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ADDRESS_NOT_EXISTS);
        }
        orderEntity.setSenderName(addressBookEntity.getName());
        orderEntity.setSenderSex(addressBookEntity.getGender());
        orderEntity.setSenderPhone(addressBookEntity.getPhone());
        orderEntity.setSenderProvinceCode(addressBookEntity.getProvinceCode());
        orderEntity.setSenderProvinceName(addressBookEntity.getProvinceName());
        orderEntity.setSenderCityCode(addressBookEntity.getCityCode());
        orderEntity.setSenderCityName(addressBookEntity.getCityName());
        orderEntity.setSenderDistrictCode(addressBookEntity.getDistrictCode());
        orderEntity.setSenderDistrictName(addressBookEntity.getDistrictName());
        orderEntity.setSenderStreetCode(addressBookEntity.getStreetCode());
        orderEntity.setSenderStreetName(addressBookEntity.getStreetName());
        orderEntity.setCommitteeCode(addressBookEntity.getCommitteeCode());
        orderEntity.setCommitteeName(addressBookEntity.getCommitteeName());
        orderEntity.setSenderAddressDetail(addressBookEntity.getDetail());

        // 从收件人地址簿获取收件人信息
        ReceiverAddressBookEntity receiverAddressBookEntity = receiverAddressBookService.getById(req.getReceiverAddressBookId());
        if (receiverAddressBookEntity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ADDRESS_NOT_EXISTS);
        }
        orderEntity.setReceiverAreaId(receiverAddressBookEntity.getReceiverAreaId());
        orderEntity.setReceiverPrefectureName(receiverAddressBookEntity.getPrefectureName());
        orderEntity.setReceiverMunicipalName(receiverAddressBookEntity.getMunicipalName());
        orderEntity.setReceiverLocalitiesName(receiverAddressBookEntity.getLocalitiesName());
        orderEntity.setReceiverPrefectureEnName(receiverAddressBookEntity.getPrefectureEnName());
        orderEntity.setReceiverMunicipalEnName(receiverAddressBookEntity.getMunicipalEnName());
        orderEntity.setReceiverLocalitiesEnName(receiverAddressBookEntity.getLocalitiesEnName());
        orderEntity.setReceiverAddressDetail(receiverAddressBookEntity.getAddressDetail());
        orderEntity.setReceiverName(receiverAddressBookEntity.getName());
        orderEntity.setReceiverPhone(receiverAddressBookEntity.getPhone());

        orderEntity.setStatus(OrderStatusVO.PENDING_PICK_UP.getCode());

        orderRepository.insert(orderEntity);

        // 设置订单详情
        OrderDetail orderDetail = OrderDetail.builder()
                .orderId(orderEntity.getId())
                .weight(req.getEstimatedWeight())
                .basicFee(req.getEstimatedPrice())
                .build();
        // 保存订单详情
        orderDetailService.insert(orderDetail);

        // todo 让管理端知道有新的订单
        return orderEntity.getOrderNo();
    }

    @Override
    public PageResult<OrderSearchRes> search(MpOrderSearchReq req) {
        PageResult<OrderEntity> pageResult = orderRepository.search(
                req.getOpenid(),
                req.getExpressNo(),
                req.getOrderNo(),
                req.getStatus(),
                req.getPageNo(),
                req.getPageSize());
        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }

        return new PageResult<>(OrderAdapter.INSTANCE.entityList2searchResList(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pickUp(OrderPickUpReq req) {
        OrderEntity orderEntity = orderRepository.getById(req.getOrderId());
        if (ObjUtil.isNull(orderEntity)) {
            throw new ServiceException("订单不存在");
        }
        OrderDetail orderDetail = orderDetailService.getByOrderId(req.getOrderId());
        if (ObjUtil.isNull(orderDetail)) {
            throw new ServiceException("订单不存在");
        }
        orderDetail.setWeight(req.getWeight());
        orderDetail.setLength(req.getLength());
        orderDetail.setWidth(req.getWidth());
        orderDetail.setHeight(req.getHeight());
        orderDetail.setDimensionalWeight(req.getDimensionalWeight());
        orderDetail.setBasicFee(req.getBasicFee());
        // 更新订单明细
        orderDetailService.updateById(orderDetail);
        // 分配物流单号（佐川号码）
        orderEntity.setExpressNo(expressNumberService.getSawagaNumber());
        // 更新订单状态
        orderEntity.setStatus(OrderStatusVO.PENDING_PAYMENT.getCode());
        orderRepository.updateById(orderEntity);
        return true;
    }

    @Override
    public OrderDetailRes getById(Long id) {
        OrderEntity order = orderRepository.getById(id);
        if (ObjUtil.isNull(order)) {
            return null;
        }
        OrderDetailRes orderDetailRes = OrderDetailRes.builder()
                .id(order.getId())
                .expressNo(order.getExpressNo())
                .orderNo(order.getOrderNo())
                .itemName(order.getItemName())
                .itemQuantity(order.getItemQuantity())
                .senderName(order.getSenderName())
                .senderGender(order.getSenderSex())
                .senderMobileNo(order.getSenderPhone())
                .senderProvince(order.getSenderProvinceName())
                .senderCity(order.getSenderCityName())
                .senderDistrict(order.getSenderCityName())
                .senderStreet(order.getSenderStreetName())
                .senderAddressDetail(order.getSenderAddressDetail())
                .receiverName(order.getReceiverName())
                .receiverMobileNo(order.getReceiverPhone())
                .receiverPrefecture(order.getReceiverPrefectureName())
                .receiverMunicipal(order.getReceiverMunicipalName())
                .receiverLocalities(order.getReceiverLocalitiesName())
                .receiverAddressDetail(order.getReceiverAddressDetail()).build();

        OrderDetail orderDetail = orderDetailService.getByOrderId(id);
        if (ObjUtil.isNull(orderDetail)) {
            return orderDetailRes;
        }
        orderDetailRes.setWeight(orderDetail.getWeight());
        orderDetailRes.setLength(orderDetail.getLength());
        orderDetailRes.setWidth(orderDetail.getWidth());
        orderDetailRes.setHeight(orderDetail.getHeight());
        orderDetailRes.setDimensionalWeight(orderDetail.getDimensionalWeight());
        orderDetailRes.setBasicFee(orderDetail.getBasicFee());
        return orderDetailRes;
    }

    @Override
    public void assignOrder(Long orderId, Long courierId) {
        OrderEntity orderEntity = orderRepository.getById(orderId);
        if (ObjUtil.isNull(orderEntity)) {
            throw new ServiceException("订单不存在");
        }

        UserEntity userEntity = userService.getById(courierId);
        if (!userEntity.ifCourier()) {
            throw new ServiceException("该用户不是快递员，无法分配订单");
        }

        orderEntity.setExpressNo(expressNumberService.getSawagaNumber());
        orderEntity.setCourierId(courierId);
        orderEntity.setStatus(OrderStatusVO.PENDING_PICK_UP.getCode());
        orderRepository.updateById(orderEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancel(OrderCancelReq req) {
        OrderEntity order = orderRepository.getById(req.getOrderId());
        if (ObjUtil.isNull(order)) {
            throw new ServiceException("订单不存在");
        }

        if (!StrUtil.equals(order.getOpenid(), req.getOpenid())) {
            throw new ServiceException("该订单不属于此用户");
        }

        order.setStatus(OrderStatusVO.CANCEL.getCode());
        orderRepository.updateById(order);

        // 还原佐川单号为未使用
        expressNumberService.revertSawagaNumber(order.getExpressNo());
        return true;
    }

    @Override
    public PageResult<MpOrderPageRes> pageMpOrder(MpOrderPageReq req) {
        PageResult<OrderEntity> pageResult = orderRepository.pageMpOrder(req.getStatus(), req.getPageNo(), req.getPageSize());
        List<OrderEntity> list = pageResult.getList();
        if (CollUtil.isEmpty(list)) {
            return new PageResult<>(pageResult.getTotal());
        }
        List<MpOrderPageRes> mpOrderPageResList = OrderAdapter.INSTANCE.entity2pageMpResList(list);
        return new PageResult<>(mpOrderPageResList, pageResult.getTotal());
    }

    @Override
    public Integer countMpOrder(Integer status) {
        return orderRepository.countByStatus(status);
    }
}
