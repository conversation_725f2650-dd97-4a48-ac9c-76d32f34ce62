package cn.ysatnaf.infrastructure.persistent.repository.trackingnumber;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.trackingnumber.model.entity.Location;
import cn.ysatnaf.domain.trackingnumber.repository.LocationRepository;
import cn.ysatnaf.infrastructure.persistent.converter.trackingnumber.LocationConverter;
import cn.ysatnaf.infrastructure.persistent.dao.trackingnumber.LocationDao;
import cn.ysatnaf.infrastructure.persistent.po.trackingnumber.LocationPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 地点/区域仓库实现
 * <AUTHOR>
 */
@Repository
public class LocationRepositoryImpl extends ServiceImpl<LocationDao, LocationPO> implements LocationRepository {

    private final LocationConverter converter = LocationConverter.INSTANCE;

    @Override
    public Location findById(Long id) {
        LocationPO po = getById(id);
        return converter.toEntity(po);
    }

    @Override
    public Location findByCode(String code) {
        LambdaQueryWrapper<LocationPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LocationPO::getCode, code).last("LIMIT 1");
        LocationPO po = getOne(queryWrapper);
        return converter.toEntity(po);
    }

    @Override
    public List<Location> findAllActive() {
        LambdaQueryWrapper<LocationPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(LocationPO::getIsActive, true);
        List<LocationPO> poList = list(queryWrapper);
        return converter.toEntityList(poList);
    }

    @Override
    public Location save(Location location) {
        LocationPO po = converter.toPO(location);
        saveOrUpdate(po);
        return converter.toEntity(po);
    }

    @Override
    public boolean deleteById(Long id) {
        // Consider logical delete
        return removeById(id);
    }

    @Override
    public List<Location> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<LocationPO> poList = listByIds(ids);
        return converter.toEntityList(poList);
    }
}