package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.valobj.ManifestStatus;
import cn.ysatnaf.domain.manifest.model.valobj.TrackingStatus;
import cn.ysatnaf.domain.manifest.repository.ManifestRepository;
import cn.ysatnaf.domain.statistics.model.dto.*;
import cn.ysatnaf.domain.statistics.model.vo.SummaryVO;
import cn.ysatnaf.domain.statistics.model.vo.TodayVO;
import cn.ysatnaf.infrastructure.persistent.converter.ManifestConverter;
import cn.ysatnaf.infrastructure.persistent.dao.ManifestDao;
import cn.ysatnaf.infrastructure.persistent.po.ManifestPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class ManifestRepositoryImpl implements ManifestRepository {

    private final ManifestDao manifestDao;

    @Override
    public PageResult<Manifest> searchOrder(String keyword,
                                            String expressNumber,
                                            String sawagaNumber,
                                            String orderNumber,
                                            String orderNo,
                                            String receiverName,
                                            String itemName,
                                            Integer status,
                                            Long userId,
                                            LocalDateTime createTimeStart,
                                            LocalDateTime createTimeEnd,
                                            LocalDateTime pickUpTimeStart,
                                            LocalDateTime pickUpTimeEnd,
                                            LocalDateTime shipmentTimeStart,
                                            LocalDateTime shipmentTimeEnd,
                                            Integer pageNo,
                                            Integer pageSize, Boolean isDelete) {
        Boolean isOnline = null;
        if (status != null && status == 35) {
            // 未上线
            status = ManifestStatus.SHIPPED.getCode();
            isOnline = false;
        }
        QueryWrapper<ManifestPO> wrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(keyword)) {
            wrapper.eq(ObjUtil.isNotEmpty(status) && ObjUtil.notEqual(status, ManifestStatus.ALL.getCode()), "status", status)
                    .eq(isOnline != null, "is_online", isOnline)
                    .eq(ObjUtil.isNotNull(userId), "user_id", userId)
                    .eq("is_delete", isDelete)
                    .ge(createTimeStart != null, "create_time", createTimeStart)
                    .le(createTimeEnd != null, "create_time", createTimeEnd)
                    .ge(pickUpTimeStart != null, "pick_up_time", pickUpTimeStart)
                    .le(pickUpTimeEnd != null, "pick_up_time", pickUpTimeEnd)
                    .ge(shipmentTimeStart != null, "shipment_time", shipmentTimeStart)
                    .le(shipmentTimeEnd != null, "shipment_time", shipmentTimeEnd)
                    .orderByDesc("create_time")
                    .orderByDesc("id")
                    .and(w -> w.like("express_number", keyword)
                            .or().like("sawaga_number", keyword)
                            .or().like("transferred_tracking_number", keyword)
                            .or().like("order_no", keyword)
                            .or().like("receiver_name", keyword)
                            .or().like("receiver_phone", keyword)
                            .or().like("receiver_address", keyword)
                            .or().like("receiver_zip_code", keyword));
        } else if (StrUtil.isNotBlank(itemName)) {
            Integer total = manifestDao.countContainsItemName(
                    status,
                    userId,
                    itemName,
                    isDelete,
                    expressNumber,
                    sawagaNumber,
                    orderNumber,
                    orderNo,
                    receiverName,
                    createTimeStart,
                    createTimeEnd,
                    pickUpTimeStart,
                    pickUpTimeEnd,
                    shipmentTimeStart,
                    shipmentTimeEnd);
            if (total == 0) {
                return PageResult.empty(0L);
            }
            int offset = (pageNo - 1) * pageSize;
            int limit = pageSize;
            List<ManifestPO> manifestPOList = manifestDao.selectContainsItemName(
                    status,
                    userId,
                    itemName,
                    isDelete,
                    expressNumber,
                    sawagaNumber,
                    orderNumber,
                    orderNo,
                    receiverName,
                    createTimeStart,
                    createTimeEnd,
                    pickUpTimeStart,
                    pickUpTimeEnd,
                    shipmentTimeStart,
                    shipmentTimeEnd,
                    offset,
                    limit);
            List<Manifest> entityList = ManifestConverter.INSTANCE.toEntityList(manifestPOList);
            return new PageResult<>(entityList, total.longValue());
        } else {
            wrapper.eq(ObjUtil.isNotEmpty(status) && ObjUtil.notEqual(status, ManifestStatus.ALL.getCode()), "status", status)
                    .eq(isOnline != null, "is_online", isOnline)
                    .eq(ObjUtil.isNotNull(userId), "user_id", userId)
                    .eq("is_delete", isDelete)
                    .like(StrUtil.isNotBlank(expressNumber), "express_number", expressNumber)
                    .like(StrUtil.isNotBlank(sawagaNumber), "sawaga_number", sawagaNumber)
                    .like(StrUtil.isNotBlank(sawagaNumber), "transferred_tracking_number", sawagaNumber)
                    .like(StrUtil.isNotBlank(orderNumber), "order_number", orderNumber)
                    .like(StrUtil.isNotBlank(orderNo), "order_no", orderNo)
                    .like(StrUtil.isNotBlank(receiverName), "receiver_name", receiverName)
                    .ge(createTimeStart != null, "create_time", createTimeStart)
                    .le(createTimeEnd != null, "create_time", createTimeEnd)
                    .ge(pickUpTimeStart != null, "pick_up_time", pickUpTimeStart)
                    .le(pickUpTimeEnd != null, "pick_up_time", pickUpTimeEnd)
                    .ge(shipmentTimeStart != null, "shipment_time", shipmentTimeStart)
                    .le(shipmentTimeEnd != null, "shipment_time", shipmentTimeEnd)
                    .orderByDesc("create_time")
                    .orderBy(true, false, "CAST(" + "order_number" + " AS UNSIGNED)")
                    .orderByDesc("id");
        }
        Page<ManifestPO> manifestPOPage = manifestDao.selectPage(new Page<>(pageNo, pageSize), wrapper);
        if (CollUtil.isEmpty(manifestPOPage.getRecords())) {
            return PageResult.empty(manifestPOPage.getTotal());
        }
        List<Manifest> manifests = ManifestConverter.INSTANCE.toEntityList(manifestPOPage.getRecords());
        return new PageResult<>(manifests, manifestPOPage.getTotal());
    }


    @Override
    public Manifest getByExpressNumber(String expressNumber) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ManifestPO::getExpressNumber, expressNumber);
        return ManifestConverter.INSTANCE.toEntity(manifestDao.selectOne(wrapper));
    }

    @Override
    public List<String> fuzzySearchExpressNumber(String expressNumber, Integer pageNumber, Integer pageSize) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(StrUtil.isNotBlank(expressNumber), ManifestPO::getExpressNumber, expressNumber);
        wrapper.eq(ManifestPO::getIsDelete, false);
        wrapper.select(ManifestPO::getExpressNumber);

        Page<ManifestPO> selectPage = manifestDao.selectPage(new Page<>(pageNumber, pageSize), wrapper);
        if (CollUtil.isEmpty(selectPage.getRecords())) {
            return Collections.emptyList();
        }
        return selectPage.getRecords().stream().map(ManifestPO::getExpressNumber).distinct().collect(Collectors.toList());
    }

    @Override
    public void insert(Manifest manifest) {
        ManifestPO manifestPO = ManifestConverter.INSTANCE.toPO(manifest);
        manifestDao.insert(manifestPO);
        manifest.setId(manifestPO.getId());
    }

    @Override
    public Manifest getById(Long id) {
        return ManifestConverter.INSTANCE.toEntity(manifestDao.selectById(id));
    }

    @Override
    public void updateById(Manifest manifest) {
        manifestDao.updateById(ManifestConverter.INSTANCE.toPO(manifest));
    }

    @Override
    public PageResult<Manifest> searchManifest(String expressNumber, String sawagaNumber, String orderNo, LocalDateTime createTimeStart, LocalDateTime createTimeEnd, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<ManifestPO> manifestPOLambdaQueryWrapper = buildSearchWrapper(expressNumber, sawagaNumber, orderNo, createTimeStart, createTimeEnd);
        Page<ManifestPO> manifestPOPage = manifestDao.selectPage(new Page<>(pageNo, pageSize), manifestPOLambdaQueryWrapper);
        if (CollUtil.isEmpty(manifestPOPage.getRecords())) {
            return PageResult.empty(manifestPOPage.getTotal());
        }
        return new PageResult<>(ManifestConverter.INSTANCE.toEntityList(manifestPOPage.getRecords()), manifestPOPage.getTotal());
    }

    @NotNull
    private static LambdaQueryWrapper<ManifestPO> buildSearchWrapper(String expressNumber, String sawagaNumber, String orderNo, LocalDateTime createTimeStart, LocalDateTime createTimeEnd) {
        LambdaQueryWrapper<ManifestPO> manifestPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        manifestPOLambdaQueryWrapper
                .eq(ManifestPO::getIsDelete, false)
                .likeRight(StrUtil.isNotBlank(expressNumber), ManifestPO::getExpressNumber, expressNumber)
                .likeRight(StrUtil.isNotBlank(sawagaNumber), ManifestPO::getSawagaNumber, sawagaNumber)
                .likeRight(StrUtil.isNotBlank(orderNo), ManifestPO::getOrderNo, orderNo)
                .ge(ManifestPO::getStatus, ManifestStatus.PICKED_UP.getCode())
                .ge(createTimeStart != null, ManifestPO::getPickUpTime, createTimeStart)
                .le(createTimeEnd != null, ManifestPO::getPickUpTime, createTimeEnd)
                .orderByDesc(ManifestPO::getPickUpTime)
                .orderByDesc(ManifestPO::getId);
        return manifestPOLambdaQueryWrapper;
    }

    @Override
    public List<Manifest> getByIds(Collection<Long> ids) {
        return ManifestConverter.INSTANCE.toEntityList(manifestDao.selectBatchIds(ids));
    }

    @Override
    public List<Manifest> getByExpressNumbers(Collection<String> expressNumbers) {
        return ManifestConverter.INSTANCE.toEntityList(manifestDao.selectList(new LambdaQueryWrapper<ManifestPO>().in(ManifestPO::getExpressNumber, expressNumbers)));
    }

    @Override
    public void updateBatchByIds(List<Manifest> manifests) {
        manifestDao.updateBatchById(ManifestConverter.INSTANCE.toPOList(manifests));
    }

    @Override
    public Manifest getByOrderNumber(String orderNumber) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ManifestPO::getOrderNumber, orderNumber);
        return ManifestConverter.INSTANCE.toEntity(manifestDao.selectOne(wrapper));
    }

    @Override
    public List<Manifest> findShippedManifests() {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ManifestPO::getStatus, ManifestStatus.SHIPPED.getCode());
        wrapper.eq(ManifestPO::getIsDelete, false);
        return ManifestConverter.INSTANCE.toEntityList(manifestDao.selectList(wrapper));
    }

    @Override
    public Integer count(Long userId, Date startTime, Date endTime) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjUtil.isNotNull(userId), ManifestPO::getUserId, userId);
        wrapper.ge(ManifestPO::getStatus, ManifestStatus.SHIPPED.getCode());
        wrapper.ge(ObjUtil.isNotNull(startTime), ManifestPO::getCreateTime, startTime);
        wrapper.le(ObjUtil.isNotNull(endTime), ManifestPO::getCreateTime, endTime);
        wrapper.eq(ManifestPO::getIsDelete, false);
        return manifestDao.selectCount(wrapper).intValue();
    }

    @Override
    public Manifest fuzzyGetBySawagaNumber(String expressNumber) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(ManifestPO::getSawagaNumber, expressNumber);
        Page<ManifestPO> manifestPOPage = manifestDao.selectPage(new Page<>(1, 1), wrapper);
        if (CollUtil.isEmpty(manifestPOPage.getRecords())) {
            return null;
        }
        return ManifestConverter.INSTANCE.toEntity(manifestPOPage.getRecords().get(0));
    }

    @Override
    public List<Manifest> getByUserId(Long userId) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ManifestPO::getUserId, userId);
        return ManifestConverter.INSTANCE.toEntityList(manifestDao.selectList(wrapper));
    }

    @Override
    public void deleteByIds(List<Long> manifestIds) {
        manifestDao.deleteBatchIds(manifestIds);
    }

    @Override
    public PageResult<Manifest> page(Long userId, Date startTime, Date endTime, int pageIndex, int pageSize) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjUtil.isNotNull(userId), ManifestPO::getUserId, userId);
        wrapper.ge(ManifestPO::getStatus, ManifestStatus.SHIPPED.getCode());
        wrapper.ge(ObjUtil.isNotNull(startTime), ManifestPO::getShipmentTime, startTime);
        wrapper.le(ObjUtil.isNotNull(endTime), ManifestPO::getShipmentTime, endTime);
        wrapper.eq(ManifestPO::getIsDelete, false);
        Page<ManifestPO> manifestPOPage = manifestDao.selectPage(new Page<>(pageIndex, pageSize), wrapper);
        if (CollUtil.isEmpty(manifestPOPage.getRecords())) {
            return new PageResult<>(0L);
        }
        List<Manifest> manifests = ManifestConverter.INSTANCE.toEntityList(manifestPOPage.getRecords());
        return new PageResult<>(manifests, manifestPOPage.getTotal());
    }

    @Override
    public List<Manifest> listByPickUpTimeAndShippingFeeTemplateType(Date pickUpTimeFrom, Date pickUpTimeTo, List<Integer> shippingFeeTemplateTypes) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ManifestPO::getIsDelete, false);
        wrapper.in(CollUtil.isNotEmpty(shippingFeeTemplateTypes), ManifestPO::getShippingFeeTemplateType, shippingFeeTemplateTypes);
        wrapper.ge(ManifestPO::getStatus, ManifestStatus.PICKED_UP.getCode());
        wrapper.ge(ObjUtil.isNotNull(pickUpTimeFrom), ManifestPO::getPickUpTime, pickUpTimeFrom);
        wrapper.le(ObjUtil.isNotNull(pickUpTimeTo), ManifestPO::getPickUpTime, pickUpTimeTo);
        return ManifestConverter.INSTANCE.toEntityList(manifestDao.selectList(wrapper));
    }

    @Override
    public Manifest getByOrderNoOrExpressNumber(String orderNo) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        // order_no = order_no or sawaga_number = order_no
        wrapper.eq(ManifestPO::getOrderNo, orderNo).or().eq(ManifestPO::getSawagaNumber, orderNo).or().eq(ManifestPO::getTransferredTrackingNumber, orderNo);
        return ManifestConverter.INSTANCE.toEntity(manifestDao.selectOne(wrapper));
    }

    @Override
    public void insertBatch(List<Manifest> manifests) {
        List<ManifestPO> poList = ManifestConverter.INSTANCE.toPOList(manifests);
        manifestDao.insertBatchSomeColumn(poList);
    }

    @Override
    public List<Manifest> findShippedManifestsByShippingFeeTemplateTypes(List<Integer> templateTypes) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ManifestPO::getStatus, ManifestStatus.SHIPPED.getCode());
        wrapper.eq(ManifestPO::getIsDelete, false);
        wrapper.in(ManifestPO::getShippingFeeTemplateType, templateTypes);
        return ManifestConverter.INSTANCE.toEntityList(manifestDao.selectList(wrapper));
    }

    @Override
    public List<Manifest> listByPreReportedTime(Date preReportedTimeFrom, Date preReportedTimeTo, Long userId) {
        LambdaQueryWrapper<ManifestPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjUtil.isNotNull(userId), ManifestPO::getUserId, userId);
        wrapper.ge(ManifestPO::getCreateTime, preReportedTimeFrom);
        wrapper.le(ManifestPO::getCreateTime, preReportedTimeTo);
        wrapper.eq(ManifestPO::getIsDelete, false);
        return ManifestConverter.INSTANCE.toEntityList(manifestDao.selectList(wrapper));
    }

    @Override
    public SummaryVO.TotalData selectTotalSummary() {
        return manifestDao.selectTotalSummary();
    }

    @Override
    public SummaryVO.CurrentMonthData selectPeriodSummary(LocalDateTime start, LocalDateTime end) {
        return manifestDao.selectPeriodSummary(start, end);
    }

    @Override
    public TodayVO selectTodaySummary(LocalDateTime start, LocalDateTime end) {
        return manifestDao.selectTodaySummary(start, end);
    }

    @Override
    public List<TemplateStatsDTO> selectTemplateStats(LocalDateTime start, LocalDateTime end) {
        return manifestDao.selectTemplateStats(start, end);
    }

    @Override
    public List<CompanyRankingDTO> selectCompanyRanking(LocalDateTime startDate, LocalDateTime endDate, Integer limit) {
        return manifestDao.selectCompanyRanking(startDate, endDate, limit);
    }

    @Override
    public List<AmountDistributionDTO> selectAmountDistribution() {
        return manifestDao.selectAmountDistribution();
    }

    @Override
    public List<TrendDTO> selectTrendData(int startHour, LocalDateTime startDate, LocalDateTime endDate) {
        return manifestDao.selectTrendData(startHour, startDate, endDate);
    }

    @Override
    public Map<String, Object> selectBaseStats(LocalDateTime startTime, LocalDateTime endTime) {
        return manifestDao.selectBaseStats(startTime, endTime);
    }

    @Override
    public Map<String, Object> selectPeakDay(LocalDateTime start, LocalDateTime end, Integer startHour) {
        return manifestDao.selectPeakDay(start, end, startHour);
    }

    @Override
    public List<TemplateCategoryDTO> selectTemplateDistribution(LocalDateTime start, LocalDateTime end) {
        return manifestDao.selectTemplateDistribution(start, end);
    }

    @Override
    public List<Map<String, Object>> selectRegionalDistribution(LocalDateTime start, LocalDateTime end) {
        return manifestDao.selectRegionalDistribution(start, end);
    }

    @Override
    public List<MonthTrendDTO> selectTwelveMonthTrend(LocalDateTime localDateTime, LocalDateTime end, Integer startHour) {
        return manifestDao.selectTwelveTrend(localDateTime, end, startHour);
    }

    @Override
    public Map<String, Object> selectAmountStats(LocalDateTime lastStart, LocalDateTime lastEnd) {
        return manifestDao.selectAmountStats(lastStart, lastEnd);
    }

    @Override
    public Map<String, Object> selectPeakAmountDay(LocalDateTime start, LocalDateTime end, Integer startHour) {
        return manifestDao.selectPeakAmountDay(start, end, startHour);
    }

    @Override
    public List<TemplateAmountDTO> selectTemplateAmountDistribution(LocalDateTime start, LocalDateTime end) {
        return manifestDao.selectTemplateAmountDistribution(start, end);
    }

    @Override
    public List<Map<String, Object>> selectRegionalAmountDistribution(LocalDateTime start, LocalDateTime end) {
        return manifestDao.selectRegionalAmountDistribution(start, end);
    }

    @Override
    public List<MonthAmountTrendDTO> selectAmountTrend(LocalDateTime localDateTime, LocalDateTime end, Integer startHour) {
        return manifestDao.selectAmountTrend(localDateTime, end, startHour);
    }

    @Override
    public Map<String, Object> getTodayYesterdayQuantitySummary(LocalDateTime todayStart, LocalDateTime todayEnd, LocalDateTime yesterdayStart, LocalDateTime yesterdayEnd, LocalDateTime beforeYesterdayStart, LocalDateTime beforeYesterdayEnd) {
        return manifestDao.getTodayYesterdayQuantitySummary(todayStart, todayEnd, yesterdayStart, yesterdayEnd, beforeYesterdayStart, beforeYesterdayEnd);
    }

    @Override
    public List<Map<String, Object>> getTodayHourlyStats(LocalDateTime todayStart, LocalDateTime todayEnd, int startHour) {
        return manifestDao.getTodayHourlyStats(todayStart, todayEnd, startHour);
    }

    @Override
    public List<Map<String, Object>> getTemplateQuantityDistribution(LocalDateTime start, LocalDateTime end) {
        return manifestDao.getTemplateQuantityDistribution(start, end);
    }

    @Override
    public Map<String, Object> getTodayYesterdayBeforeAmountSummary(LocalDateTime todayStart, LocalDateTime todayEnd, LocalDateTime yesterdayStart, LocalDateTime yesterdayEnd, LocalDateTime beforeYesterdayStart, LocalDateTime beforeYesterdayEnd) {
        return manifestDao.getTodayYesterdayBeforeAmountSummary(todayStart, todayEnd, yesterdayStart, yesterdayEnd, beforeYesterdayStart, beforeYesterdayEnd);
    }

    @Override
    public List<Map<String, Object>> getTodayHourlyAmountStats(LocalDateTime todayStart, LocalDateTime todayEnd, int startHour) {
        return manifestDao.getTodayHourlyAmountStats(todayStart, todayEnd, startHour);
    }

    @Override
    public List<Map<String, Object>> getTemplateAmountDistribution(LocalDateTime todayStart, LocalDateTime todayEnd) {
        return manifestDao.getTemplateAmountDistribution(todayStart, todayEnd);
    }

    @Override
    public Map<String, Object> getPeriodSummary(LocalDateTime currentStartTime, LocalDateTime currentEndTime) {
        return manifestDao.getPeriodSummary(currentStartTime, currentEndTime);
    }

    @Override
    public List<Map<String, Object>> getTrendData(LocalDateTime currentStartTime, LocalDateTime currentEndTime, String unit, String groupFormat) {
        return manifestDao.getTrendData(currentStartTime, currentEndTime, unit, groupFormat);
    }

    @Override
    public List<Map<String, Object>> getHeatmapData(LocalDateTime currentStartTime, LocalDateTime currentEndTime) {
        return manifestDao.getHeatmapData(currentStartTime, currentEndTime);
    }

    @Override
    public List<Map<String, Object>> getTemplateDistribution(LocalDateTime currentStartTime, LocalDateTime currentEndTime) {
        return manifestDao.getTemplateDistribution(currentStartTime, currentEndTime);
    }

    @Override
    public List<Manifest> listByMasterBillId(Long masterBillId) {
        return ManifestConverter.INSTANCE.toEntityList(
                manifestDao.selectList(
                        new LambdaQueryWrapper<ManifestPO>()
                                .eq(ManifestPO::getMasterBillId, masterBillId)
                                .eq(ManifestPO::getIsDelete, false)));
    }

    @Override
    public boolean updateMasterBill(Long manifestId, Long masterBillId, String masterBillNumber) {
        if (manifestId == null) {
            return false;
        }
        return manifestDao.updateMasterBill(manifestId, masterBillId, masterBillNumber) > 0;
    }

    @Override
    public List<Manifest> getByMasterBillId(Long masterBillId) {
        if (masterBillId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ManifestPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ManifestPO::getMasterBillId, masterBillId)
                .eq(ManifestPO::getIsDelete, false);
        List<ManifestPO> manifestPOs = manifestDao.selectList(queryWrapper);
        return ManifestConverter.INSTANCE.toEntityList(manifestPOs);
    }

    @Override
    public PageResult<Manifest> advancedSearch(
            LocalDateTime pickUpTimeStart,
            LocalDateTime pickUpTimeEnd,
            Long masterBillId,
            String expressNumber,
            String sawagaNumber,
            List<Integer> shippingFeeTemplateTypes,
            Integer pageNo,
            Integer pageSize) {
        
        // 创建自定义查询条件
        Page<ManifestPO> page = new Page<>(pageNo, pageSize);
        
        // 使用MyBatis-Plus条件构造器
        LambdaQueryWrapper<ManifestPO> wrapper = Wrappers.lambdaQuery();
        
        // 设置基本条件
        wrapper.eq(ManifestPO::getIsDelete, 0);
        
        // 揽件时间范围
        if (pickUpTimeStart != null) {
            wrapper.ge(ManifestPO::getPickUpTime, pickUpTimeStart);
        }
        if (pickUpTimeEnd != null) {
            wrapper.le(ManifestPO::getPickUpTime, pickUpTimeEnd);
        }
        
        // 提单ID
        if (masterBillId != null) {
            wrapper.eq(ManifestPO::getMasterBillId, masterBillId);
        }
        
        // 预报单号
        if (StrUtil.isNotBlank(expressNumber)) {
            wrapper.like(ManifestPO::getExpressNumber, expressNumber);
        }
        
        // 国际物流单号
        if (StrUtil.isNotBlank(sawagaNumber)) {
            wrapper.like(ManifestPO::getSawagaNumber, sawagaNumber);
        }
        
        // 货物类型
        if (CollUtil.isNotEmpty(shippingFeeTemplateTypes)) {
            wrapper.in(ManifestPO::getShippingFeeTemplateType, shippingFeeTemplateTypes);
        }

        // 按照揽件时间倒序排序
        wrapper.gt(ManifestPO::getStatus, 1);
        wrapper.orderByDesc(ManifestPO::getPickUpTime);
        
        // 执行分页查询
        Page<ManifestPO> manifestPOPage = manifestDao.selectPage(page, wrapper);
        
        // 转换结果
        if (CollUtil.isEmpty(manifestPOPage.getRecords())) {
            return new PageResult<>(manifestPOPage.getTotal());
        }
        
        // 转换为领域对象
        List<Manifest> manifests = ManifestConverter.INSTANCE.toEntityList(manifestPOPage.getRecords());
        
        return new PageResult<>(manifests, manifestPOPage.getTotal());
    }

    @Override
    public Long countPendingPackingByMasterBill(Long masterBillId) {
        if (masterBillId == null) {
            return 0L;
        }
        LambdaQueryWrapper<ManifestPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ManifestPO::getMasterBillId, masterBillId)
                    .eq(ManifestPO::getPackingStatus, 0) // 0: 待装箱
                    .eq(ManifestPO::getIsDelete, false); // 通常也需要排除已删除的
        // 使用 manifestDao 进行 count 查询
        return manifestDao.selectCount(queryWrapper);
    }

    @Override
    public List<Manifest> listByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return ManifestConverter.INSTANCE.toEntityList(manifestDao.selectBatchIds(ids));
    }

    @Override
    public int batchUpdatePackingStatusAndRecordId(List<Long> manifestIds, Integer packingStatus, Long parcelSortingRecordId) {
        if (CollUtil.isEmpty(manifestIds)) {
            return 0;
        }
        // 调用 ManifestDao (Mapper) 中的对应方法
        // 注意：ManifestDao 中需要有 batchUpdatePackingStatusAndRecordId 方法的定义和 XML 实现
        /*
         <update id="batchUpdatePackingStatusAndRecordId">
        UPDATE tb_manifest
        SET
            packing_status = #{packingStatus},
            parcel_sorting_record_id = #{parcelSortingRecordId},
            update_time = NOW()
        WHERE id IN
        <foreach item="item" collection="manifestIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
         */
        LambdaUpdateWrapper<ManifestPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ManifestPO::getId, manifestIds)
                .set(ManifestPO::getPackingStatus, packingStatus)
                .set(ManifestPO::getParcelSortingRecordId, parcelSortingRecordId);
        return manifestDao.update(updateWrapper);
    }

    @Override
    public Long countByMasterBillId(Long masterBillId) {
        if (masterBillId == null) {
            return 0L;
        }
        LambdaQueryWrapper<ManifestPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ManifestPO::getMasterBillId, masterBillId)
                .eq(ManifestPO::getIsDelete, false); // 同样排除已删除的
        return manifestDao.selectCount(queryWrapper);
    }

    @Override
    public Long countByMasterBillIdAndTemplateTypes(Long masterBillId, List<Integer> templateTypes) {
        if (masterBillId == null || CollUtil.isEmpty(templateTypes)) {
            return 0L;
        }
        // 调用 DAO 层方法
        /*
         * <!-- 根据主提单ID和模板类型列表统计关联的运单总数 -->
         *     <select id="countByMasterBillIdAndTemplateTypes" resultType="long">
         *         SELECT COUNT(*)
         *         FROM tb_manifest
         *         WHERE master_bill_id = #{masterBillId}
         *           AND is_delete = 0
         *           AND shipping_fee_template_type IN
         *             <foreach item="item" collection="templateTypes" open="(" separator="," close=")">
         *                 #{item}
         *             </foreach>
         *     </select>
         */
        LambdaQueryWrapper<ManifestPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ManifestPO::getMasterBillId, masterBillId)
                .eq(ManifestPO::getIsDelete, false) // 同样排除已删除的
                .in(ManifestPO::getShippingFeeTemplateType, templateTypes);
        return manifestDao.selectCount(queryWrapper);
    }

    @Override
    public void updatePackingStatusAndRecordId(Long manifestId, int packingStatus, Long parcelSortingRecordId) {
        LambdaUpdateWrapper<ManifestPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ManifestPO::getId, manifestId)
                .set(ManifestPO::getPackingStatus, packingStatus)
                .set(ManifestPO::getParcelSortingRecordId, parcelSortingRecordId);
        manifestDao.update(updateWrapper);
    }

}
