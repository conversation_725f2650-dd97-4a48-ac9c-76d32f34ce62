package cn.ysatnaf.infrastructure.persistent.converter;

import cn.ysatnaf.domain.address.model.entity.ReceiverAddressBookEntity;
import cn.ysatnaf.infrastructure.persistent.po.ReceiverAddressBookPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * ReceiverAddressBookConverter
 *
 * <AUTHOR>
 * @date 2023/12/22 15:46
 */
@Mapper
public interface ReceiverAddressBookConverter {
    ReceiverAddressBookConverter INSTANCE = Mappers.getMapper(ReceiverAddressBookConverter.class);


    ReceiverAddressBookPO entity2po(ReceiverAddressBookEntity receiverAddressBookEntity);

    ReceiverAddressBookEntity po2entity(ReceiverAddressBookPO receiverAddressBookPO);

    List<ReceiverAddressBookEntity> po2entityList(List<ReceiverAddressBookPO> selectList);

    List<ReceiverAddressBookPO> entity2poList(List<ReceiverAddressBookEntity> receiverAddressBookEntityList);
}
