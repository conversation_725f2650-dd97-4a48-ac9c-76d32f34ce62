package cn.ysatnaf.domain.excel.serviice.generator;

import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import lombok.Data;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Data
public class DocumentDataContext {

    /**
     * 物流信息
     */
    private List<Manifest> manifests;

    /**
     * 批次信息
     */
    private BatchInfo batchInfo;

    /**
     * 响应
     */
    private HttpServletResponse httpServletResponse;

    /**
     * 构造器
     */
    public DocumentDataContext(List<Manifest> manifests, BatchInfo batchInfo, HttpServletResponse httpServletResponse) {
        this.manifests = manifests;
        this.batchInfo = batchInfo;
        this.httpServletResponse = httpServletResponse;
    }
}
