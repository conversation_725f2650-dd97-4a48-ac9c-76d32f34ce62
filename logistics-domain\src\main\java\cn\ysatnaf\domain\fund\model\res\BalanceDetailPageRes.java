package cn.ysatnaf.domain.fund.model.res;

import cn.ysatnaf.domain.manifest.model.dto.ManifestOrderDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Hang
 */
@Schema(description = "账户余额变动明细")
@Data
public class BalanceDetailPageRes {
    
    @Schema(description = "明细ID")
    private Long id;
    
    @Schema(description = "增减情况")
    private BigDecimal changeAmount;
    
    @Schema(description = "余额")
    private BigDecimal balance;

    @Schema(description = "操作内容")
    private String remark;

    @Schema(description = "运单ID")
    private Long manifestId;

    @Schema(description = "相关物流信息")
    private ManifestOrderDTO manifest;
    
    @Schema(description = "操作者ID")
    private Long operatorId;
    
    @Schema(description = "操作者")
    private String operator;
    
    @Schema(description = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operationTime;
}
