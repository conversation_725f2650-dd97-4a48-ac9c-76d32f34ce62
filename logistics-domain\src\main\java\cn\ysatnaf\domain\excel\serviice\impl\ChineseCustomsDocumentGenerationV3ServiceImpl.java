package cn.ysatnaf.domain.excel.serviice.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.util.RandomUtil;
import cn.ysatnaf.domain.excel.serviice.ChineseCustomsDocumentGenerationService;
import cn.ysatnaf.domain.manifest.model.aggregate.ManifestAggregate;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import cn.ysatnaf.domain.manifest.model.excel.ApplicationFormExcelDatumV3;
import cn.ysatnaf.domain.excel.serviice.generator.row.TokyoCustomsDocumentExcelRow;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ChineseCustomsDocumentGenerationService
 * 中国海关文件生成服务
 *
 * <AUTHOR> Hang
 * @date 2024/3/25 14:40
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ChineseCustomsDocumentGenerationV3ServiceImpl implements ChineseCustomsDocumentGenerationService {

    @Override
    public void generate(String packageNumber, List<ManifestAggregate> manifestAggregates, HttpServletResponse httpServletResponse) {
        List<ApplicationFormExcelDatumV3> excelData = getApplicationFormExcelData(packageNumber, manifestAggregates);
        try {
            exportApplicationForm(excelData, "", httpServletResponse);
        } catch (IOException e) {
            log.error("导出文件失败: ", e);
            throw new ServiceException(9999, "导出文件失败");
        }
    }

    @Override
    public void generate(List<ManifestAggregate> manifestAggregates, String recordName, HttpServletResponse httpServletResponse) {
        List<ApplicationFormExcelDatumV3> excelData = manifestAggregates.stream().map(
                        manifestAggregate -> {
                            Manifest manifest = manifestAggregate.getManifest();
                            List<ManifestItem> manifestItems = manifestAggregate.getManifestItems();
                            BigDecimal totalPrice = RandomUtil.randomBigDecimal(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                            return ApplicationFormExcelDatumV3.builder()
                                    .packageNumber(manifest.getPackageNumber())
                                    .orderNo(manifest.getOrderNo())
                                    .expressNumber(manifest.getTrackingNumber())
                                    .goodsDescription(manifestItems.stream().map(ManifestItem::getName).collect(Collectors.joining(",")))
                                    .quantity(manifestItems.size())
                                    .unitPrice(totalPrice.divide(BigDecimal.valueOf(manifestItems.size()), 2, RoundingMode.HALF_UP))
                                    .totalPrice(totalPrice)
                                    .currencySystem("CNY")
                                    .weight(manifest.getWeight())
                                    .transactionUnit("件").build();
                        }
                )
                .collect(Collectors.toList());
        try {
            exportApplicationForm(excelData, recordName, httpServletResponse);
        } catch (IOException e) {
            log.error("导出文件失败: ", e);
            throw new ServiceException(9999, "导出文件失败");
        }
    }

    private List<ApplicationFormExcelDatumV3> getApplicationFormExcelData(String packageNumber, List<ManifestAggregate> manifestAggregates) {
        return manifestAggregates.stream().map(
                        manifestAggregate -> {
                            Manifest manifest = manifestAggregate.getManifest();
                            List<ManifestItem> manifestItems = manifestAggregate.getManifestItems();
                            BigDecimal totalPrice = RandomUtil.randomBigDecimal(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                            return ApplicationFormExcelDatumV3.builder()
                                    .packageNumber(packageNumber)
                                    .orderNo(manifest.getOrderNo())
                                    .expressNumber(manifest.getTrackingNumber())
                                    .goodsDescription(manifestItems.stream().map(ManifestItem::getName).collect(Collectors.joining(",")))
                                    .quantity(manifestItems.size())
                                    .unitPrice(totalPrice.divide(BigDecimal.valueOf(manifestItems.size()), 2, RoundingMode.HALF_UP))
                                    .totalPrice(totalPrice)
                                    .currencySystem("CNY")
                                    .weight(manifest.getWeight())
                                    .transactionUnit("件").build();
                        }
                )
                .collect(Collectors.toList());
    }

    private void exportApplicationForm(List<ApplicationFormExcelDatumV3> excelData,
                                       String recordName, HttpServletResponse response) throws IOException {
        // 填充数据

        //获取模板 (我是将模板放到 resource文件下面了)
        try (InputStream inputStream = ResourceUtil.getStream("templates/ApplicationFormTemplateV3.xlsx")) {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "入区申请表" + recordName;
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncodeUtil.encode(fileName) + ".xlsx");

            // 生成sheet
            WriteSheet sheet1 = EasyExcel.writerSheet(0, "Sheet1").build();
            ExcelWriter write = EasyExcel.write(response.getOutputStream(), TokyoCustomsDocumentExcelRow.class)
                    .withTemplate(inputStream)
                    .build();
            // 填充数据
            write.fill(excelData, sheet1);
            write.close();
        } catch (IOException e) {
            // 异常处理
            throw new RuntimeException("导出Excel时发生错误", e);
        }
    }
}
