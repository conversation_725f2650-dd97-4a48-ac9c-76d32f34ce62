
package cn.ysatnaf.domain.shippingfeetemplate.model.req;

import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "查看运费模板列表入参")
@Data
public class ShippingFeeTemplateListReq {

    /**
     * {@link ShippingFeeTemplateTypeEnum#getCode()}
     */
    @Schema(description = "模板类型：1-普通模板；2-带电模板；3-投函模板")
    @NotNull
    private Integer type;
}
