package cn.ysatnaf.domain.statistics.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class MonthAmountDetailDTO {
    // 基本统计信息
    private BigDecimal totalAmount;         // 总运单金额
    private BigDecimal averageAmount;       // 平均单价
    private PeakDayDTO peakDay;             // 最高日金额信息
    private BigDecimal monthOnMonthChange;  // 较上月变化百分比
    
    // 月度走势
    private List<MonthlyTrendDTO> monthlyTrend;
    
    // 模板类型金额分布
    private TemplateDistributionDTO templateDistribution;
    
    // 价格分布
    private List<PriceRangeDTO> priceDistribution;
    
    @Data
    public static class PeakDayDTO {
        private String date;          // 最高日期
        private BigDecimal amount;    // 最高金额
    }
    
    @Data
    public static class MonthlyTrendDTO {
        private String month;         // 月份
        private BigDecimal amount;    // 金额
    }
    
    @Data
    public static class TemplateDistributionDTO {
        private List<TemplateTypeDTO> categories;  // 模板类型分类
    }
    
    @Data
    public static class TemplateTypeDTO {
        private String type;          // 模板类型名称
        private BigDecimal amount;    // 金额
        private BigDecimal ratio;     // 占比
    }
    
    @Data
    public static class PriceRangeDTO {
        private String range;         // 价格区间
        private Integer count;        // 数量
    }
}