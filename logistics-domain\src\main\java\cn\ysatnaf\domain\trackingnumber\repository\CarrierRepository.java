package cn.ysatnaf.domain.trackingnumber.repository;

import cn.ysatnaf.domain.trackingnumber.model.entity.Carrier;

import java.util.Collection;
import java.util.List;

/**
 * 承运商仓库接口
 * <AUTHOR>
 */
public interface CarrierRepository {

    /**
     * 根据ID查询承运商
     * @param id 承运商ID
     * @return 承运商实体，如果不存在则返回null
     */
    Carrier findById(Long id);
    
    /**
     * 根据代码查询承运商
     * @param code 承运商代码
     * @return 承运商实体，如果不存在则返回null
     */
    Carrier findByCode(String code);

    /**
     * 查询所有启用的承运商
     * @return 启用的承运商列表
     */
    List<Carrier> findAllActive();

    /**
     * 保存承运商（新增或更新）
     * @param carrier 承运商实体
     * @return 保存后的承运商实体（可能包含生成的ID）
     */
    Carrier save(Carrier carrier);

    /**
     * 根据ID删除承运商（通常是逻辑删除）
     * @param id 承运商ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);

    /**
     * 根据ID列表批量查询承运商
     * @param ids ID 集合
     * @return 承运商列表
     */
    List<Carrier> findByIds(Collection<Long> ids);

} 