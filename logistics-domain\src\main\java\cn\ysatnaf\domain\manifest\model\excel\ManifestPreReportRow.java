package cn.ysatnaf.domain.manifest.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR> Hang
 */
@Data
//@AllArgsConstructor
//@NoArgsConstructor
//@Builder
public class ManifestPreReportRow {

    @ExcelProperty("快递单号")
    private String expressNumber;

    @ExcelProperty("商店订单号（客户自定）")
    private String orderNumber;

    @ExcelProperty("重量/KG（统一0.5）")
    private BigDecimal weight = BigDecimal.valueOf(0.5);

    @ExcelProperty("数量")
    private Integer quantity;

    @ExcelProperty("价值（JPY）（统一1000日元）")
    private BigDecimal price = BigDecimal.valueOf(1000);

    @ExcelProperty("邮编")
    private String receiverZipCode;

    @ExcelProperty("收件人")
    private String receiverName;

    @ExcelProperty("收件地址")
    private String receiverAddress;

    @ExcelProperty("收件人电话")
    private String receiverPhone;

    @ExcelProperty("物品中文名称")
    private String itemName;
}
