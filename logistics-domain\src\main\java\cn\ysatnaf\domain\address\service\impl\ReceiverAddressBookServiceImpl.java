package cn.ysatnaf.domain.address.service.impl;

import cn.ysatnaf.domain.address.model.entity.ReceiverAddressBookEntity;
import cn.ysatnaf.domain.address.model.entity.ReceiverAreaEntity;
import cn.ysatnaf.domain.address.repository.ReceiverAddressBookRepository;
import cn.ysatnaf.domain.address.service.ReceiverAddressBookService;
import cn.ysatnaf.domain.address.service.ReceiverAreaService;
import cn.ysatnaf.types.common.ErrorCodeConstants;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.util.ServiceExceptionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * ReceiverAddressBookServiceImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 15:33
 */
@Service
@RequiredArgsConstructor
public class ReceiverAddressBookServiceImpl implements ReceiverAddressBookService {

    private final ReceiverAddressBookRepository receiverAddressBookRepository;

    private final ReceiverAreaService receiverAreaService;


    @Override
    public Boolean add(String openid, Long receiverAreaId, String addressDetail, String name, String phone, Boolean isDefault) {
        ReceiverAreaEntity receiverAreaEntity = receiverAreaService.getById(receiverAreaId);
        if (receiverAreaEntity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ZIP_WRONG);
        }
        ReceiverAddressBookEntity receiverAddressBookEntity = new ReceiverAddressBookEntity();
        receiverAddressBookEntity.setOpenid(openid);
        receiverAddressBookEntity.setReceiverAreaId(receiverAreaId);
        receiverAddressBookEntity.setPrefectureName(receiverAreaEntity.getPrefectureName());
        receiverAddressBookEntity.setMunicipalName(receiverAreaEntity.getMunicipalName());
        receiverAddressBookEntity.setLocalitiesName(receiverAreaEntity.getLocalitiesName());
        receiverAddressBookEntity.setPrefectureEnName(receiverAreaEntity.getPrefectureEnName());
        receiverAddressBookEntity.setMunicipalEnName(receiverAreaEntity.getMunicipalEnName());
        receiverAddressBookEntity.setLocalitiesEnName(receiverAreaEntity.getLocalitiesEnName());
        receiverAddressBookEntity.setAddressDetail(addressDetail);
        receiverAddressBookEntity.setName(name);
        receiverAddressBookEntity.setPhone(phone);
        receiverAddressBookEntity.setIsDefault(isDefault);
        receiverAddressBookRepository.insert(receiverAddressBookEntity);
        return true;
    }

    @Override
    public Boolean delete(Long id) {
        receiverAddressBookRepository.delete(id);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(Long id, Long receiverAreaId, String openid, String addressDetail, String name, String phone, Boolean isDefault) {
        // 如果是更新成默认地址，那么其他地址需要更新成非默认的
        if (Optional.ofNullable(isDefault).orElse(false)) {
            List<ReceiverAddressBookEntity> receiverAddressBookEntityList = receiverAddressBookRepository.listByOpenid(openid);
            receiverAddressBookEntityList.forEach(book -> book.setIsDefault(false));
            receiverAddressBookRepository.updateBatchById(receiverAddressBookEntityList);
        }
        ReceiverAddressBookEntity receiverAddressBookEntity = new ReceiverAddressBookEntity();
        receiverAddressBookEntity.setId(id);
        receiverAddressBookEntity.setReceiverAreaId(receiverAreaId);
        ReceiverAreaEntity receiverAreaEntity = receiverAreaService.getById(receiverAreaId);
        if (receiverAreaEntity == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ZIP_WRONG);
        }
        receiverAddressBookEntity.setPrefectureName(receiverAreaEntity.getPrefectureName());
        receiverAddressBookEntity.setMunicipalName(receiverAreaEntity.getMunicipalName());
        receiverAddressBookEntity.setLocalitiesName(receiverAreaEntity.getLocalitiesName());
        receiverAddressBookEntity.setPrefectureEnName(receiverAreaEntity.getPrefectureEnName());
        receiverAddressBookEntity.setMunicipalEnName(receiverAreaEntity.getMunicipalEnName());
        receiverAddressBookEntity.setLocalitiesEnName(receiverAreaEntity.getLocalitiesEnName());
        receiverAddressBookEntity.setAddressDetail(addressDetail);
        receiverAddressBookEntity.setName(name);
        receiverAddressBookEntity.setPhone(phone);
        receiverAddressBookEntity.setIsDefault(isDefault);
        receiverAddressBookRepository.update(receiverAddressBookEntity);
        return true;
    }

    @Override
    public PageResult<ReceiverAddressBookEntity> page(String openid, Integer pageNo, Integer pageSize) {
        return receiverAddressBookRepository.page(openid, pageNo, pageSize);
    }

    @Override
    public ReceiverAddressBookEntity getById(Long receiverAddressBookId) {
        return receiverAddressBookRepository.getById(receiverAddressBookId);
    }

    @Override
    public ReceiverAddressBookEntity getDefault(String openid) {
        return receiverAddressBookRepository.getDefaultByOpenid(openid);
    }
}
