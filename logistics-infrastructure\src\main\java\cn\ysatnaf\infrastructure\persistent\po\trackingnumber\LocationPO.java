package cn.ysatnaf.infrastructure.persistent.po.trackingnumber;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 地点/区域持久化对象
 * <AUTHOR>
 */
@Data
@TableName("locations")
public class LocationPO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 地点代码 (例如: TKY, OSK)
     */
    private String code;

    /**
     * 地点名称 (例如: 东京, 大阪)
     */
    private String name;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 