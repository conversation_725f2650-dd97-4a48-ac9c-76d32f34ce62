package cn.ysatnaf.domain.fund.adapter;

import cn.ysatnaf.domain.fund.model.entity.BalanceDetailEntity;
import cn.ysatnaf.domain.fund.model.res.BalanceDetailPageRes;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface BalanceDetailAdapter {
    BalanceDetailAdapter INSTANCE = Mappers.getMapper(BalanceDetailAdapter.class);

    @Mapping(source = "createTime", target = "operationTime")
    BalanceDetailPageRes toBalanceDetailPageRes(BalanceDetailEntity balanceDetailEntity);

    List<BalanceDetailPageRes> toBalanceDetailPageResList(List<BalanceDetailEntity> balanceDetailEntityList);
}
