package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.statistics.model.dto.TimeAnalysisDTO;
import cn.ysatnaf.domain.statistics.service.impl.TimeAnalysisService;
import cn.ysatnaf.types.common.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

@Slf4j
@RestController
@RequestMapping("/api/statistics/time-analysis")
public class TimeAnalysisController {

    @Autowired
    private TimeAnalysisService timeAnalysisService;

    @GetMapping("/data")
    public CommonResult<TimeAnalysisDTO> getTimeAnalysisData(
            @RequestParam(defaultValue = "day") String unit,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "0") int startHour) {
        try {
            log.info("Fetching time analysis data: unit={}, startDate={}, endDate={}, startHour={}",
                    unit, startDate, endDate, startHour);

            // 验证时间范围
            if (startDate.isAfter(endDate)) {
                return CommonResult.error("开始日期不能晚于结束日期");
            }

            // 计算时间跨度
            long daysBetween = ChronoUnit.DAYS.between(startDate, endDate) + 1;
            log.info("Time span: {} days", daysBetween);

            // 对于超长时间范围给出警告
            if (daysBetween > 365) {
                log.warn("Long time range detected: {} days", daysBetween);
            }

            // 获取分析数据
            TimeAnalysisDTO data = timeAnalysisService.getTimeAnalysisData(unit, startDate, endDate, startHour);
            return CommonResult.success(data);
        } catch (Exception e) {
            log.error("Error getting time analysis data", e);
            return CommonResult.error("获取时间维度分析数据失败: " + e.getMessage());
        }
    }
}