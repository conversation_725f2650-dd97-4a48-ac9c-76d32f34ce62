package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.address.model.entity.SawagaAreaEntity;
import cn.ysatnaf.domain.address.repository.SawagaAreaRepository;
import cn.ysatnaf.infrastructure.persistent.converter.SawagaAreaConverter;
import cn.ysatnaf.infrastructure.persistent.dao.SawagaAreaDao;
import cn.ysatnaf.infrastructure.persistent.po.SawagaAreaPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class SawagaAreaRepositoryImpl implements SawagaAreaRepository {

    private final SawagaAreaDao sawagaAreaDao;

    @Override
    public SawagaAreaEntity getByPrefectureName(String prefectureName) {
        LambdaQueryWrapper<SawagaAreaPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SawagaAreaPO::getPrefectureName, prefectureName);
        SawagaAreaPO sawagaAreaPO = sawagaAreaDao.selectOne(wrapper);
        return SawagaAreaConverter.INSTANCE.toEntity(sawagaAreaPO);
    }
}
