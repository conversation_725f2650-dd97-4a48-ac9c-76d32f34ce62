package cn.ysatnaf.domain.manifest.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.auth.LoginUserHolder;
import cn.ysatnaf.domain.excel.serviice.generator.BatchInfo;
import cn.ysatnaf.domain.excel.serviice.generator.DocumentDataContext;
import cn.ysatnaf.domain.excel.serviice.generator.DocumentGeneratorFactory;
import cn.ysatnaf.domain.excel.serviice.generator.JapanCustomsDocumentGenerator;
import cn.ysatnaf.domain.log.model.entity.OperationLogEntity;
import cn.ysatnaf.domain.log.model.valobj.OperationType;
import cn.ysatnaf.domain.log.service.OperationLogService;
import cn.ysatnaf.domain.manifest.adapter.MasterBillAdapter;
import cn.ysatnaf.domain.manifest.model.entity.MasterBill;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.req.MasterBillCreateReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillExportReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillGenerateBarcodePdfReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillPageReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillUpdateReq;
import cn.ysatnaf.domain.manifest.model.res.MasterBillDetailRes;
import cn.ysatnaf.domain.manifest.model.valobj.Destination;
import cn.ysatnaf.domain.manifest.model.valobj.MasterBillStatus;
import cn.ysatnaf.domain.manifest.repository.ManifestRepository;
import cn.ysatnaf.domain.manifest.repository.MasterBillRepository;
import cn.ysatnaf.domain.manifest.service.MasterBillService;
import cn.ysatnaf.domain.manifest.service.OperateManifestService;
import cn.ysatnaf.domain.pdf.service.BarcodePdfService;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.types.common.PageResult;
import cn.ysatnaf.types.exception.ServiceException;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 主提单服务实现类
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MasterBillServiceImpl implements MasterBillService {

    private final MasterBillRepository masterBillRepository;

    private final ManifestRepository manifestRepository;
    
    private final OperateManifestService operateManifestService;
    
    private final DocumentGeneratorFactory documentGeneratorFactory;
    
    private final OperationLogService operationLogService;
    
    private final BarcodePdfService barcodePdfService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(MasterBillCreateReq req) {
        // 检查提单号是否已存在
        MasterBill existingBill = masterBillRepository.getByMasterBillNumber(req.getMasterBillNumber());
        if (existingBill != null) {
            throw new ServiceException("提单号已存在");
        }

        // 转换并补充默认值
        MasterBill masterBill = MasterBillAdapter.INSTANCE.createReq2Entity(req);
        
        // 如果状态为空，默认为待起飞
        if (masterBill.getStatus() == null) {
            masterBill.setStatus(MasterBillStatus.PENDING_DEPARTURE.getCode());
        }
        
        // 设置初始统计数据
        masterBill.setTotalWeight(java.math.BigDecimal.ZERO);
        masterBill.setTotalVolume(java.math.BigDecimal.ZERO);
        masterBill.setWaybillCount(0);
        
        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        masterBill.setCreateTime(now);
        masterBill.setUpdateTime(now);

        // 设置是否删除标志
        masterBill.setIsDeleted(false);

        // 设置创建人ID
        Long userId = LoginUserHolder.getLoginUser().getId();
        masterBill.setCreatorId(userId);

        // 插入数据
        return masterBillRepository.insert(masterBill);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MasterBillUpdateReq req) {
        // 检查提单是否存在
        MasterBill existingBill = masterBillRepository.getById(req.getId());
        if (existingBill == null) {
            throw new ServiceException("提单不存在");
        }

        // 如果修改了提单号，检查新提单号是否已存在
        if (StrUtil.isNotBlank(req.getMasterBillNumber()) && 
            !req.getMasterBillNumber().equals(existingBill.getMasterBillNumber())) {
            MasterBill billWithSameNumber = masterBillRepository.getByMasterBillNumber(req.getMasterBillNumber());
            if (billWithSameNumber != null && !billWithSameNumber.getId().equals(req.getId())) {
                throw new ServiceException("提单号已存在");
            }
        }

        // 转换请求为实体
        MasterBill masterBill = MasterBillAdapter.INSTANCE.updateReq2Entity(req);
        masterBill.setUpdateTime(LocalDateTime.now());

        // 更新数据
        return masterBillRepository.update(masterBill);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        if (!LoginUserHolder.getLoginUser().ifAdmin()) {
            throw new ServiceException("权限不足");
        }
        // 检查提单是否存在
        MasterBill existingBill = masterBillRepository.getById(id);
        if (existingBill == null) {
            throw new ServiceException("提单不存在");
        }

        // 执行删除
        return masterBillRepository.deleteById(id);
    }

    @Override
    public MasterBillDetailRes getDetail(Long id) {
        // 检查提单是否存在
        MasterBill masterBill = masterBillRepository.getById(id);
        if (masterBill == null) {
            return null;
        }

        // 转换为响应DTO
        return MasterBillAdapter.INSTANCE.entity2DetailRes(masterBill);
    }

    @Override
    public PageResult<MasterBillDetailRes> page(MasterBillPageReq req) {
        // 查询分页数据
        PageResult<MasterBill> pageResult = masterBillRepository.page(
                req.getMasterBillNumber(),
                req.getDepartureDateStart(),
                req.getDepartureDateEnd(),
                req.getArrivalDateStart(),
                req.getArrivalDateEnd(),
                req.getCreateDate(),
                req.getTimeIntervalPoint(),
                req.getOrigin(),
                req.getDestination(),
                req.getCarrierCode(),
                req.getStatus(),
                req.getPageNo(),
                req.getPageSize()
        );

        // 转换为响应DTO
        List<MasterBill> records = pageResult.getList();
        List<MasterBillDetailRes> detailResList = MasterBillAdapter.INSTANCE.entityList2DetailResList(records);
        
        // 返回分页结果
        return new PageResult<>(detailResList, pageResult.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignManifest(Long manifestId, Long masterBillId) {
        // 检查主提单是否存在
        MasterBill masterBill = null;
        if (masterBillId != null) {
            masterBill = masterBillRepository.getById(masterBillId);
            if (masterBill == null) {
                throw new ServiceException("提单不存在");
            }
        }

        // 更新运单的主提单ID和提单号
        boolean result;
        if (masterBill != null) {
            result = manifestRepository.updateMasterBill(manifestId, masterBillId, masterBill.getMasterBillNumber());
        } else {
            // 如果masterBillId为null，则解除运单与主提单的关联
            result = manifestRepository.updateMasterBill(manifestId, null, null);
        }

        // 更新主提单的统计信息
        if (result && masterBillId != null) {
            masterBillRepository.updateStatistics(masterBillId);
        }

        return result;
    }

    @Override
    public boolean updateStatistics(Long masterBillId) {
        if (ObjectUtil.isNull(masterBillId)) {
            return false;
        }
        
        return masterBillRepository.updateStatistics(masterBillId);
    }
    
    @Override
    public void exportCustomsDocument(MasterBillExportReq req, HttpServletResponse response) {
        // 检查主提单是否存在
        MasterBill masterBill = masterBillRepository.getById(req.getMasterBillId());
        if (masterBill == null) {
            throw new ServiceException("提单不存在");
        }
        
        // 获取主提单下的所有运单
        List<Manifest> manifests = manifestRepository.getByMasterBillId(req.getMasterBillId());
        if (manifests.isEmpty()) {
            throw new ServiceException("该主提单下没有运单，无法导出海关文件");
        }
        
        // 根据目的地和模板类型筛选运单
        List<Manifest> filteredManifests = new ArrayList<>();
        List<Integer> shippingFeeTemplateTypes = mapTemplateTypesByDestination(req.getDestination(), req.getShippingFeeTemplateTypes());
        
        for (Manifest manifest : manifests) {
            // 检查运单的模板类型是否符合要求
            if (shippingFeeTemplateTypes.contains(manifest.getShippingFeeTemplateType())) {
                filteredManifests.add(manifest);
            }
        }
        
        if (filteredManifests.isEmpty()) {
            throw new ServiceException("该主提单下没有符合条件的运单，无法导出海关文件");
        }
        
        // 将运单状态更新为已发货
        operateManifestService.ship(filteredManifests);
        
        try {
            // 记录操作日志
            OperationLogEntity operationLogEntity = OperationLogEntity.builder()
                    .operatorId(LoginUserHolder.getLoginUser().getId())
                    .operationType(OperationType.MANIFEST_EXPORT_JP_CUSTOMS_DOCUMENTS.getValue())
                    .operation(OperationType.MANIFEST_EXPORT_JP_CUSTOMS_DOCUMENTS.getDescription())
                    .newInfo(JSON.toJSONString(req))
                    .build();
            operationLogService.log(operationLogEntity);
            
            // 准备批次信息
            BatchInfo batchInfo = new BatchInfo();
            batchInfo.setFlightDate(req.getFlightDate().toLocalDate());
            batchInfo.setLadingBill(req.getLadingBill() != null ? req.getLadingBill() : masterBill.getMasterBillNumber());
            batchInfo.setFlightNumber(req.getFlightNumber());
            
            // 获取对应目的地的文档生成器
            Destination destination = Destination.values()[0];
            for (Destination dest : Destination.values()) {
                if (dest.getCode().equals(req.getDestination())) {
                    destination = dest;
                    break;
                }
            }
            JapanCustomsDocumentGenerator generator = documentGeneratorFactory.getGeneratorByDestination(destination);
            
            // 生成并导出文档
            DocumentDataContext documentDataContext = new DocumentDataContext(filteredManifests, batchInfo, response);
            generator.generate(documentDataContext);

            // 更新提单状态
            masterBill.setStatus(MasterBillStatus.COMPLETED.getCode());
            masterBillRepository.update(masterBill);
        } catch (IOException e) {
            log.error("导出海关文件失败: ", e);
            throw new ServiceException(9999, "导出海关文件失败");
        }
    }
    
    /**
     * 根据目的地映射运费模板类型
     * @param destination 目的地代码
     * @param templateTypes 原始模板类型列表
     * @return 映射后的模板类型列表
     */
    private List<Integer> mapTemplateTypesByDestination(Integer destination, List<Integer> templateTypes) {
        if (templateTypes == null || templateTypes.isEmpty()) {
            return Collections.emptyList();
        }
        
        List<Integer> mappedTypes = new ArrayList<>();
        
        if (destination.equals(Destination.OSAKA.getCode())) {
            // 大阪目的地的模板类型映射
            for (Integer type : templateTypes) {
                if (type.equals(ShippingFeeTemplateTypeEnum.GENERAL.getCode())) {
                    mappedTypes.add(ShippingFeeTemplateTypeEnum.GENERAL_OSAKA.getCode());
                } else if (type.equals(ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode())) {
                    mappedTypes.add(ShippingFeeTemplateTypeEnum.SMALL_PARCEL_OSAKA.getCode());
                }
            }
        } else if (destination.equals(Destination.TOKYO.getCode())) {
            // 东京目的地直接使用原始模板类型
            mappedTypes.addAll(templateTypes);
        }
        
        return mappedTypes;
    }

    @Override
    public void generateBarcodePdf(MasterBillGenerateBarcodePdfReq req, HttpServletResponse response) {
        // 检查主提单是否存在
        MasterBill masterBill = masterBillRepository.getById(req.getMasterBillId());
        if (masterBill == null) {
            throw new ServiceException("提单不存在");
        }
        
        // 获取主提单下的所有运单
        List<Manifest> manifests = manifestRepository.getByMasterBillId(req.getMasterBillId());
        if (manifests.isEmpty()) {
            throw new ServiceException("该主提单下没有运单，无法生成条码PDF");
        }
        
        // 根据目的地和模板类型筛选运单
        List<Manifest> filteredManifests = new ArrayList<>();
        List<Integer> shippingFeeTemplateTypes = mapTemplateTypesByDestination(req.getDestination(), req.getShippingFeeTemplateTypes());
        
        for (Manifest manifest : manifests) {
            // 检查运单的模板类型是否符合要求
            if (shippingFeeTemplateTypes.contains(manifest.getShippingFeeTemplateType())) {
                filteredManifests.add(manifest);
            }
        }
        
        if (filteredManifests.isEmpty()) {
            throw new ServiceException("该主提单下没有符合条件的运单，无法生成条码PDF");
        }
        
        // 调用条码PDF生成服务
        barcodePdfService.generate(filteredManifests, response);
    }

    @Override
    public List<MasterBill> listByIds(Set<Long> masterBillIds) {
        return masterBillRepository.listByIds(masterBillIds);
    }
} 