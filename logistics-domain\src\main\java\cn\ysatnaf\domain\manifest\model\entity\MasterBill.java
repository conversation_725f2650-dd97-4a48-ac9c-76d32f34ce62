package cn.ysatnaf.domain.manifest.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 主提单实体类
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MasterBill {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 提单号/航班号
     */
    private String masterBillNumber;

    /**
     * 起飞日期
     */
    private LocalDateTime departureDate;

    /**
     * 到达日期
     */
    private LocalDateTime arrivalDate;

    /**
     * 始发地
     */
    private String origin;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 承运商代码
     */
    private String carrierCode;

    /**
     * 提单状态
     */
    private Integer status;

    /**
     * 总重量
     */
    private BigDecimal totalWeight;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;

    /**
     * 运单数量
     */
    private Integer waybillCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 是否删除
     */
    private Boolean isDeleted;
} 