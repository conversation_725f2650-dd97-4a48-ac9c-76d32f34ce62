package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.shippingfee.model.req.ShippingFeeCalculateReq;
import cn.ysatnaf.domain.shippingfee.model.res.ShippingFeeCalculateRes;
import cn.ysatnaf.domain.shippingfee.service.ShippingFeeService;
import cn.ysatnaf.types.common.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.security.PermitAll;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Tag(name = "运费模块")
@RequestMapping("/web/shippingFee")
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
public class ShippingFeeController {

    private final ShippingFeeService shippingFeeService;

    @PostMapping("/calculate")
    @PermitAll
    @Operation(summary = "计算运费")
    public CommonResult<ShippingFeeCalculateRes> calculate(@RequestBody @Validated ShippingFeeCalculateReq req) {
        return CommonResult.success(shippingFeeService.calculate(req));
    }
}