package cn.ysatnaf.domain.tracking.model.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.Tracking;
import cn.ysatnaf.domain.manifest.repository.ManifestRepository;
import cn.ysatnaf.domain.manifest.repository.TrackingRepository;
import cn.ysatnaf.domain.tracking.model.dto.LogisticsData;
import cn.ysatnaf.domain.tracking.model.dto.ShippingInfo;
import cn.ysatnaf.domain.tracking.model.dto.TimelineItem;
import cn.ysatnaf.domain.tracking.model.service.LogisticsService;
import cn.ysatnaf.types.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class LogisticsServiceImpl implements LogisticsService {

    @Autowired
    private ManifestRepository manifestRepository;
    
    @Autowired
    private TrackingRepository trackingRepository;

    @Override
    public LogisticsData queryLogisticsInfo(String trackingNumber) {

        
        Manifest manifest = manifestRepository.getByExpressNumber(trackingNumber);
        
        if (manifest == null) {
            throw new ServiceException("未找到该物流单号相关信息: " + trackingNumber);
        }
        
        // 2. 查询物流轨迹
        List<Tracking> trackings = trackingRepository.getByManifestId(manifest.getId());
        if (CollUtil.isEmpty(trackings)) {
            return null;
        }

        // 3. 组装响应数据
        LogisticsData data = new LogisticsData();
        data.setTrackingNumber(manifest.getSawagaNumber());
        data.setTransferNumber(manifest.getTransferredTrackingNumber());
        
        // 设置运费信息
        ShippingInfo shippingInfo = new ShippingInfo();
        shippingInfo.setFee(manifest.getTotalCost());
        data.setShipping(shippingInfo);
        
        // 设置轨迹信息
        List<TimelineItem> timeline = new ArrayList<>();
        Integer finalStatus = trackings.get(trackings.size() - 1).getStatus();
        for (Tracking track : trackings) {
            if (StrUtil.isBlank(track.getTrack())) {
                continue;
            }
            TimelineItem item = new TimelineItem();
            item.setLocation(track.getPlace());
            item.setDescription(track.getTrack());
            if (track.getTrack().startsWith("快件已到达福州处理中心")) {
                item.setTime(convertToDate(manifest.getPickUpTime()));
            } else {
                item.setTime(track.getTime());
                item.setLocation(track.getPlace());
            }
            item.setStatus(track.getStatus());
            timeline.add(item);
        }
        TimelineItem timelineItem = new TimelineItem();
        timelineItem.setDescription("已完成预报[中国]");
        timelineItem.setTime(convertToDate(manifest.getCreateTime()));
        timeline.add(timelineItem);
        data.setTimeline(timeline);
        data.setStatus(finalStatus);
        return data;
    }

    public static Date convertToDate(LocalDateTime localDateTime) {
        // 关键点：必须指定时区（LocalDateTime 不含时区信息）
        return Date.from(
                localDateTime.atZone(ZoneId.systemDefault()) // 使用系统默认时区
                        .toInstant()
        );
    }
}