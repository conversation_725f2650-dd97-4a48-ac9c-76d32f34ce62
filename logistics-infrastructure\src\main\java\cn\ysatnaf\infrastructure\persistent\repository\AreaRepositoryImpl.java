package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.address.model.entity.AreaEntity;
import cn.ysatnaf.domain.address.repository.AreaRepository;
import cn.ysatnaf.infrastructure.persistent.converter.AreaConverter;
import cn.ysatnaf.infrastructure.persistent.dao.AreaDao;
import cn.ysatnaf.infrastructure.persistent.po.AreaPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AreaRepositoryImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 11:33
 */
@Repository
@RequiredArgsConstructor
public class AreaRepositoryImpl implements AreaRepository {

    private final AreaDao areaDao;

    @Override
    public List<AreaEntity> listByPid(Long pid) {
        LambdaQueryWrapper<AreaPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AreaPO::getPid, pid);
        List<AreaPO> poList = areaDao.selectList(wrapper);
        return poList.stream().map(AreaConverter.INSTANTCE::po2entity).collect(Collectors.toList());
    }
}
