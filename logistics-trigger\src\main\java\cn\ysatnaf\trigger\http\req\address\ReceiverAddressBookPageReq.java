package cn.ysatnaf.trigger.http.req.address;

import cn.ysatnaf.types.common.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AddressBookGetReq
 *
 * <AUTHOR>
 * @date 2023/12/22 11:07
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分页获取地址列表 入参")
@Data
public class ReceiverAddressBookPageReq extends PageParam {

    @Schema(description = "微信公众号openid")
    private String openid;
}
