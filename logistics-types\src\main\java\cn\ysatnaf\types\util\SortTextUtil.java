package cn.ysatnaf.types.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SortTextUtil
 *
 * <AUTHOR>
 * @date 2024/1/24 9:51
 */
public class SortTextUtil {
    public static void main(String[] args) {
        String address = "2-5-6ルヴ333ィラージュ301";

        // 定义正则表达式，匹配包含汉字、片假名和平假名的日文部分
        String regex = "[一-龯ァ-ヴー]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(address);

        int startIndex = 0;
        List<String> nonJpText = new ArrayList<>();
        // 查找并输出日文部分
        while (matcher.find()) {
            String japaneseText = matcher.group();
            System.out.println("提取到的日文部分：" + japaneseText);

            // 获取日文部分的开始索引和结束索引
            int japaneseStartIndex = matcher.start();
            int japaneseEndIndex = matcher.end();
            if (japaneseStartIndex != startIndex) {
                System.out.println(address.substring(startIndex, japaneseStartIndex));
            }
            startIndex = japaneseEndIndex;
        }
        if (startIndex < address.length() - 1) {
            System.out.println(address.substring(startIndex));
        }
    }
}
