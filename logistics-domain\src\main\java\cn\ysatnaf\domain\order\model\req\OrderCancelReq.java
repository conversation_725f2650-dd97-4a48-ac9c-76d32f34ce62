package cn.ysatnaf.domain.order.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * OrderCancelReq
 *
 * <AUTHOR>
 * @date 2024/2/7 16:37
 */
@Schema(description = "取消订单 入参")
@Data
public class OrderCancelReq {

    @Schema(description = "openid")
    @NotBlank
    private String openid;

    @Schema(description = "订单ID")
    @NotNull
    private Long orderId;

}
