package cn.ysatnaf.infrastructure.persistent.po.item;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 物品名称违禁关键词表 持久化对象
 */
@Data
@TableName("prohibited_item_keywords")
public class ProhibitedItemKeywordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 违禁关键词或短语
     */
    @TableField("keyword")
    private String keyword;

    /**
     * 是否启用该关键词规则 (TRUE: 启用, FALSE: 禁用)
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 备注信息 (例如: 解释为什么禁用, 适用范围等)
     */
    @TableField("remarks")
    private String remarks;

    /**
     * 创建者ID
     */
    @TableField(value = "creator_id", fill = FieldFill.INSERT) // 假设使用MyBatisPlus自动填充
    private Long creatorId;

    /**
     * 最后更新者ID
     */
    @TableField(value = "updater_id", fill = FieldFill.INSERT_UPDATE) // 假设使用MyBatisPlus自动填充
    private Long updaterId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

} 