package cn.ysatnaf.domain.parcelsorting.model.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * ParcelSortingRecord
 *
 * <AUTHOR> Hang
 * @date 2024/3/29 18:54
 */
@EqualsAndHashCode(callSuper = true)
@TableName("tb_parcel_sorting_record")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ParcelSortingRecordPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "记录名称")
    private String recordName;

    @Schema(description = "主提单ID")
    private Long masterBillId;

    @Schema(description = "是否删除")
    private Boolean isDelete;
}
