package cn.ysatnaf.infrastructure.persistent.repository;

import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import cn.ysatnaf.domain.manifest.repository.ManifestItemRepository;
import cn.ysatnaf.infrastructure.persistent.converter.ManifestItemConverter;
import cn.ysatnaf.infrastructure.persistent.dao.ManifestItemDao;
import cn.ysatnaf.infrastructure.persistent.po.ManifestItemPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Repository
@RequiredArgsConstructor
public class ManifestItemRepositoryImpl implements ManifestItemRepository {

    private final ManifestItemDao manifestItemDao;

    @Override
    public void insertBatch(List<ManifestItem> manifestItems) {
        manifestItemDao.insertBatchSomeColumn(ManifestItemConverter.INSTANCE.toPOList(manifestItems));
    }

    @Override
    public void deleteByManifestId(Long manifestId) {
        LambdaQueryWrapper<ManifestItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ManifestItemPO::getManifestId, manifestId);
        manifestItemDao.delete(queryWrapper);
    }

    @Override
    public List<ManifestItem> listByManifestId(Long manifestId) {
        LambdaQueryWrapper<ManifestItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ManifestItemPO::getManifestId, manifestId);
        return ManifestItemConverter.INSTANCE.toEntityList(manifestItemDao.selectList(queryWrapper));
    }

    @Override
    public List<ManifestItem> listBatchByManifestIds(Collection<Long> manifestIds) {
        if (manifestIds != null && !manifestIds.isEmpty()) {
            LambdaQueryWrapper<ManifestItemPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(ManifestItemPO::getManifestId, manifestIds);
            return ManifestItemConverter.INSTANCE.toEntityList(manifestItemDao.selectList(queryWrapper));
        }
        return Collections.emptyList();
    }

    @Override
    public void deleteByManifestIds(List<Long> deleteItemManifestIds) {
        LambdaQueryWrapper<ManifestItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ManifestItemPO::getManifestId, deleteItemManifestIds);
        manifestItemDao.delete(queryWrapper);
    }
}
