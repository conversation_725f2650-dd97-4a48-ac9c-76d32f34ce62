package cn.ysatnaf.domain.manifest.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.IdUtil;
import cn.ysatnaf.domain.manifest.model.aggregate.ManifestAggregate;
import cn.ysatnaf.domain.manifest.model.entity.Manifest;
import cn.ysatnaf.domain.manifest.model.entity.ManifestItem;
import cn.ysatnaf.domain.manifest.model.req.InvoiceGenerationTaskSubmitReq;
import cn.ysatnaf.domain.manifest.model.req.MasterBillInvoiceExportReq;
import cn.ysatnaf.domain.manifest.model.valobj.Destination;
import cn.ysatnaf.domain.manifest.producer.GenerateInvoiceProducer;
import cn.ysatnaf.domain.manifest.service.InvoiceService;
import cn.ysatnaf.domain.manifest.service.ManifestService;
import cn.ysatnaf.domain.shippingfeetemplate.model.vo.ShippingFeeTemplateTypeEnum;
import cn.ysatnaf.domain.task.model.po.TaskPO;
import cn.ysatnaf.domain.task.service.TaskService;
import cn.ysatnaf.types.exception.ServiceException;
import cn.ysatnaf.types.util.OSSUtil;
import com.alibaba.fastjson2.JSON;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR> Hang
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceServiceImpl implements InvoiceService {

    private final TaskService taskService;

    private final GenerateInvoiceProducer generateInvoiceProducer;

    private final ManifestService manifestService;

    private final OSSUtil ossUtil;

    @Override
    public Long submitGenerationTask(InvoiceGenerationTaskSubmitReq req) {
        Long taskId = taskService.createGenerateInvoiceTask(req);
        generateInvoiceProducer.produce(taskId);
        return taskId;
    }

    @Override
    public void generate(Long taskId) {
        TaskPO taskPO = taskService.getById(taskId);
        taskService.start(taskPO);
        TimeInterval timer = DateUtil.timer();
        log.info("生成发票任务-[{}]-开始执行", taskId);
        InvoiceGenerationTaskSubmitReq req = JSON.parseObject(taskPO.getTaskParam(), InvoiceGenerationTaskSubmitReq.class);
        
        // 创建临时文件
        File tempZipFile = null;
        try {
            tempZipFile = Files.createTempFile("invoices_" + taskId, ".zip").toFile();
            log.info("生成发票任务-[{}]-创建临时文件: {}", taskId, tempZipFile.getPath());
            
            try (FileOutputStream fos = new FileOutputStream(tempZipFile);
                 BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fos);
                 ZipOutputStream zipOutputStream = new ZipOutputStream(bufferedOutputStream)) {
                
                // 获取要生成发票的运单数据
                log.info("生成发票任务-[{}]-开始获取运单数据", taskId);
                if (CollUtil.isNotEmpty(req.getShippingFeeTemplateTypes()) && req.getShippingFeeTemplateTypes().contains(ShippingFeeTemplateTypeEnum.GENERAL.getCode())) {
                    req.getShippingFeeTemplateTypes().add(ShippingFeeTemplateTypeEnum.SPECIAL.getCode());
                }
                List<ManifestAggregate> manifestAggregates = manifestService.searchManifestAggregate(
                        req.getIds(),
                        req.getPickUpTimeFrom(),
                        req.getPickUpTimeTo(),
                        req.getShippingFeeTemplateTypes());
                if (CollUtil.isEmpty(manifestAggregates)) {
                    taskPO.setProgress(100d);
                    throw new ServiceException("没有符合筛选条件的订单");
                }

                log.info("生成发票任务-[{}]-获取到{}条运单数据，开始生成PDF", taskId, manifestAggregates.size());
                int totalCount = manifestAggregates.size();
                for (int completedCount = 0; completedCount < manifestAggregates.size(); completedCount++) {
                    ManifestAggregate manifestAggregate = manifestAggregates.get(completedCount);
                    doGenerateInvoice(manifestAggregate, zipOutputStream);
                    
                    // 更新进度，PDF生成占总进度的90%
                    double progress = ((double) (completedCount + 1) / (double) totalCount) * 90;
                    if (completedCount % 10 == 0 || completedCount == totalCount - 1) {
                        taskPO.setProgress(progress);
                        taskService.updateById(taskPO);
                        log.info("生成发票任务-[{}]-PDF生成进度: {}/{}, {}%", taskId, completedCount + 1, totalCount, String.format("%.1f", progress));
                    }
                }
                zipOutputStream.finish();
                log.info("生成发票任务-[{}]-PDF生成完成，ZIP文件大小: {} bytes", taskId, tempZipFile.length());
            }
            
            // 更新进度到95%，准备上传
            taskPO.setProgress(95d);
            taskService.updateById(taskPO);
            log.info("生成发票任务-[{}]-开始上传文件到OSS", taskId);
            
            // 先测试OSS连接
            if (!ossUtil.testConnection()) {
                throw new ServiceException("OSS连接失败，请检查网络或配置");
            }
            
            // 直接上传文件到 OSS，避免内存溢出
            String fileName = IdUtil.fastSimpleUUID() + "_Invoices.zip";
            long uploadStartTime = System.currentTimeMillis();
            
            try {
                log.info("生成发票任务-[{}]-开始上传文件到OSS，文件大小: {} bytes", taskId, tempZipFile.length());
                
                // 添加上传超时监控
                Thread uploadTimeoutThread = new Thread(() -> {
                    try {
                        Thread.sleep(600000); // 10分钟超时
                        log.warn("生成发票任务-[{}]-OSS上传可能超时，已等待10分钟", taskId);
                    } catch (InterruptedException e) {
                        // 正常结束，不需要处理
                    }
                });
                uploadTimeoutThread.setDaemon(true);
                uploadTimeoutThread.start();
                
                // 进行OSS上传
                ossUtil.upload(fileName, tempZipFile);
                
                // 取消超时监控
                uploadTimeoutThread.interrupt();
                
                long uploadTime = System.currentTimeMillis() - uploadStartTime;
                log.info("生成发票任务-[{}]-OSS上传成功，文件名: {}, 用时: {}ms", taskId, fileName, uploadTime);
                
                // 更新进度到99%
                taskPO.setProgress(99d);
                taskService.updateById(taskPO);
                
                String downloadUrl = "https://zebra-logistics-files.oss-cn-fuzhou.aliyuncs.com/" + fileName;
                // 记录 OSS 路径到数据库（示例）
                taskPO.setDownloadUrl(downloadUrl);
                log.info("生成发票任务-[{}]-设置下载链接: {}", taskId, downloadUrl);
                
                // 完成任务
                taskService.completed(taskPO);
                log.info("生成发票任务-[{}]-任务完成", taskId);
                
            } catch (Exception uploadEx) {
                long uploadTime = System.currentTimeMillis() - uploadStartTime;
                log.error("生成发票任务-[{}]-OSS上传失败，用时: {}ms", taskId, uploadTime, uploadEx);
                
                // 根据异常类型提供不同的错误信息
                String errorMessage;
                if (uploadEx.getMessage().contains("timeout") || uploadEx.getMessage().contains("超时")) {
                    errorMessage = "文件上传超时，请检查网络连接";
                } else if (uploadEx.getMessage().contains("network") || uploadEx.getMessage().contains("网络")) {
                    errorMessage = "网络连接异常，请稍后重试";
                } else if (uploadEx.getMessage().contains("access") || uploadEx.getMessage().contains("权限")) {
                    errorMessage = "OSS访问权限不足";
                } else {
                    errorMessage = "文件上传失败: " + uploadEx.getMessage();
                }
                
                throw new ServiceException(errorMessage);
            }
            
        } catch (ServiceException e) {
            log.error("生成发票任务-[{}]-业务异常", taskId, e);
            taskService.failed(taskPO, e.getMessage());
        } catch (Exception e) {
            log.error("生成发票任务-[{}]-系统异常", taskId, e);
            taskService.failed(taskPO, "系统异常，请联系管理员: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (tempZipFile != null && tempZipFile.exists()) {
                try {
                    Files.delete(tempZipFile.toPath());
                    log.info("生成发票任务-[{}]-临时文件清理成功: {}", taskId, tempZipFile.getPath());
                } catch (IOException e) {
                    log.warn("生成发票任务-[{}]-删除临时文件失败: {}", taskId, tempZipFile.getPath(), e);
                }
            }
        }
        log.info("生成发票任务-[{}]-执行结束，总用时: {}ms", taskId, timer.intervalMs());
    }

    @Override
    public void masterBillGenerate(Long taskId) {
        TaskPO taskPO = taskService.getById(taskId);
        taskService.start(taskPO);
        TimeInterval timer = DateUtil.timer();
        log.info("生成发票任务-[{}]-开始执行", taskId);
        MasterBillInvoiceExportReq req = JSON.parseObject(taskPO.getTaskParam(), MasterBillInvoiceExportReq.class);
        
        // 创建临时文件
        File tempZipFile = null;
        try {
            tempZipFile = Files.createTempFile("invoices_" + taskId, ".zip").toFile();
            log.info("生成发票任务-[{}]-创建临时文件: {}", taskId, tempZipFile.getPath());
            
            try (FileOutputStream fos = new FileOutputStream(tempZipFile);
                 BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(fos);
                 ZipOutputStream zipOutputStream = new ZipOutputStream(bufferedOutputStream)) {
                
                // 获取要生成发票的运单数据
                log.info("生成发票任务-[{}]-开始获取运单数据，主单号: {}", taskId, req.getMasterBillId());
                List<ManifestAggregate> manifestAggregates =  manifestService.listByMasterBillId(req.getMasterBillId());
                // 根据目的地和模板类型筛选运单
                List<ManifestAggregate> filteredManifests = new ArrayList<>();
                List<Integer> shippingFeeTemplateTypes = mapTemplateTypesByDestination(req.getDestination(), req.getShippingFeeTemplateTypes());

                for (ManifestAggregate manifestAggregate : manifestAggregates) {
                    // 检查运单的模板类型是否符合要求
                    if (shippingFeeTemplateTypes.contains(manifestAggregate.getManifest().getShippingFeeTemplateType())) {
                        filteredManifests.add(manifestAggregate);
                    }
                }
                if (CollUtil.isEmpty(filteredManifests)) {
                    taskPO.setProgress(100d);
                    throw new ServiceException("没有符合筛选条件的订单");
                }

                log.info("生成发票任务-[{}]-筛选后获取到{}条运单数据，开始生成PDF", taskId, filteredManifests.size());
                int totalCount = filteredManifests.size();
                for (int completedCount = 0; completedCount < filteredManifests.size(); completedCount++) {
                    ManifestAggregate manifestAggregate = filteredManifests.get(completedCount);
                    doGenerateInvoice(manifestAggregate, zipOutputStream);
                    
                    // 更新进度，PDF生成占总进度的90%
                    double progress = ((double) (completedCount + 1) / (double) totalCount) * 90;
                    if (completedCount % 10 == 0 || completedCount == totalCount - 1) {
                        taskPO.setProgress(progress);
                        taskService.updateById(taskPO);
                        log.info("生成发票任务-[{}]-PDF生成进度: {}/{}, {}%", taskId, completedCount + 1, totalCount, String.format("%.1f", progress));
                    }
                }
                zipOutputStream.finish();
                log.info("生成发票任务-[{}]-PDF生成完成，ZIP文件大小: {} bytes", taskId, tempZipFile.length());
            }
            
            // 更新进度到95%，准备上传
            taskPO.setProgress(95d);
            taskService.updateById(taskPO);
            log.info("生成发票任务-[{}]-开始上传文件到OSS", taskId);
            
            // 先测试OSS连接
            if (!ossUtil.testConnection()) {
                throw new ServiceException("OSS连接失败，请检查网络或配置");
            }
            
            // 直接上传文件到 OSS，避免内存溢出
            String fileName = IdUtil.fastSimpleUUID() + "_Invoices.zip";
            long uploadStartTime = System.currentTimeMillis();
            
            try {
                log.info("生成发票任务-[{}]-开始上传文件到OSS，文件大小: {} bytes", taskId, tempZipFile.length());
                
                // 添加上传超时监控
                Thread uploadTimeoutThread = new Thread(() -> {
                    try {
                        Thread.sleep(600000); // 10分钟超时
                        log.warn("生成发票任务-[{}]-OSS上传可能超时，已等待10分钟", taskId);
                    } catch (InterruptedException e) {
                        // 正常结束，不需要处理
                    }
                });
                uploadTimeoutThread.setDaemon(true);
                uploadTimeoutThread.start();
                
                // 进行OSS上传
                ossUtil.upload(fileName, tempZipFile);
                
                // 取消超时监控
                uploadTimeoutThread.interrupt();
                
                long uploadTime = System.currentTimeMillis() - uploadStartTime;
                log.info("生成发票任务-[{}]-OSS上传成功，文件名: {}, 用时: {}ms", taskId, fileName, uploadTime);
                
                // 更新进度到99%
                taskPO.setProgress(99d);
                taskService.updateById(taskPO);
                
                String downloadUrl = "https://zebra-logistics-files.oss-cn-fuzhou.aliyuncs.com/" + fileName;
                // 记录 OSS 路径到数据库（示例）
                taskPO.setDownloadUrl(downloadUrl);
                log.info("生成发票任务-[{}]-设置下载链接: {}", taskId, downloadUrl);
                
                // 完成任务
                taskService.completed(taskPO);
                log.info("生成发票任务-[{}]-任务完成", taskId);
                
            } catch (Exception uploadEx) {
                long uploadTime = System.currentTimeMillis() - uploadStartTime;
                log.error("生成发票任务-[{}]-OSS上传失败，用时: {}ms", taskId, uploadTime, uploadEx);
                
                // 根据异常类型提供不同的错误信息
                String errorMessage;
                if (uploadEx.getMessage().contains("timeout") || uploadEx.getMessage().contains("超时")) {
                    errorMessage = "文件上传超时，请检查网络连接";
                } else if (uploadEx.getMessage().contains("network") || uploadEx.getMessage().contains("网络")) {
                    errorMessage = "网络连接异常，请稍后重试";
                } else if (uploadEx.getMessage().contains("access") || uploadEx.getMessage().contains("权限")) {
                    errorMessage = "OSS访问权限不足";
                } else {
                    errorMessage = "文件上传失败: " + uploadEx.getMessage();
                }
                
                throw new ServiceException(errorMessage);
            }
            
        } catch (ServiceException e) {
            log.error("生成发票任务-[{}]-业务异常", taskId, e);
            taskService.failed(taskPO, e.getMessage());
        } catch (Exception e) {
            log.error("生成发票任务-[{}]-系统异常", taskId, e);
            taskService.failed(taskPO, "系统异常，请联系管理员: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (tempZipFile != null && tempZipFile.exists()) {
                try {
                    Files.delete(tempZipFile.toPath());
                    log.info("生成发票任务-[{}]-临时文件清理成功: {}", taskId, tempZipFile.getPath());
                } catch (IOException e) {
                    log.warn("生成发票任务-[{}]-删除临时文件失败: {}", taskId, tempZipFile.getPath(), e);
                }
            }
        }
        log.info("生成发票任务-[{}]-执行结束，总用时: {}ms", taskId, timer.intervalMs());
    }

    /**
     * 根据目的地映射运费模板类型
     * @param destination 目的地代码
     * @param templateTypes 原始模板类型列表
     * @return 映射后的模板类型列表
     */
    private List<Integer> mapTemplateTypesByDestination(Integer destination, List<Integer> templateTypes) {
        if (templateTypes == null || templateTypes.isEmpty()) {
            return Collections.emptyList();
        }

        List<Integer> mappedTypes = new ArrayList<>();

        if (destination.equals(Destination.OSAKA.getCode())) {
            // 大阪目的地的模板类型映射
            for (Integer type : templateTypes) {
                if (type.equals(ShippingFeeTemplateTypeEnum.GENERAL.getCode())) {
                    mappedTypes.add(ShippingFeeTemplateTypeEnum.GENERAL_OSAKA.getCode());
                } else if (type.equals(ShippingFeeTemplateTypeEnum.SMALL_PARCEL.getCode())) {
                    mappedTypes.add(ShippingFeeTemplateTypeEnum.SMALL_PARCEL_OSAKA.getCode());
                }
            }
        } else if (destination.equals(Destination.TOKYO.getCode())) {
            // 东京目的地直接使用原始模板类型
            mappedTypes.addAll(templateTypes);
        }

        return mappedTypes;
    }

    private void doGenerateInvoice(ManifestAggregate manifestAggregate, ZipOutputStream zipOutputStream) {
        PdfFont font = getFont();
        Manifest manifest = manifestAggregate.getManifest();
        // 创建 PdfWriter 和 PdfDocument
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(arrayOutputStream);

        PdfDocument pdfDocument = new PdfDocument(writer);

        // 创建 Document
        Document document = new Document(pdfDocument);
        // 设置页面的边距
        documentMargins(document);

        //编写PDF主体的文档内容 , 这一块是主要编写位置
        setContent(document, manifestAggregate, font);

        document.close();   //document文档要在输出前关闭，不然会提示"java.nio.file.NoSuchFileException: editable.pdf"
        pdfDocument.close();// 关闭对象

        try {
            writer.close();
            zipOutputStream.putNextEntry(new ZipEntry(manifest.getOrderNo() + ".pdf"));
            zipOutputStream.write(arrayOutputStream.toByteArray());
            zipOutputStream.closeEntry();
            arrayOutputStream.close();
        } catch (IOException e) {
            log.error("关闭输出流失败: ", e);
            throw new ServiceException(e.getMessage());
        }

    }

    /**
     * 获取设置的字体
     *
     * @return PdfFont 字体
     */
    public static PdfFont getFont() {
        // 设置中文字体
        PdfFont font;
        try {
            ClassPathResource classPathResource = new ClassPathResource("fonts/PingFang.ttf");
            font = PdfFontFactory.createFont(classPathResource.getPath());
        } catch (IOException e) {
            log.error("获取字体失败: ", e);
            throw new ServiceException(9999, "获取字体失败");
        }
        return font;
    }

    /**
     * 设置页面的边距
     *
     * @param document 内容文档
     */
    private static void documentMargins(Document document) {
        // 上、右、下、左
        int margins = 10;
        document.setMargins(margins, margins, margins, margins);
    }

    /**
     * PDF主体内容
     *
     * @param document 文档
     */
    private void setContent(Document document, ManifestAggregate manifestAggregate, PdfFont font) {
        // 创建运单
        createWayBill(document, manifestAggregate, font);

        // 创建发票
        createInvoice(document, manifestAggregate, font);
    }

    private void createInvoice(Document document, ManifestAggregate manifestAggregate, PdfFont font) {
        // 创建标题
        createInvoiceTitle(document, font);

        // 创建发票表格
        createInvoiceTable(document, manifestAggregate, font);
    }

    private void createInvoiceTable(Document document, ManifestAggregate manifestAggregate, PdfFont font) {
        /********************************  表格由 Table 区域编写 ******************************************/
        Table shipTable = new Table(UnitValue.createPercentArray(new float[]{50, 0.5f, 50})).setMarginLeft(40).setMarginRight(40);
        String shipFromCellContent = "Fuzhou sufeng Information Technology Co.,Ltd\r\n" +
                "\r\n" +
                "1902, 19th Floor, Building B, Netcom Smart Center, No. 11 Keji East Road, Shangjie Town, Minhou Count···";
        Cell spaceCell = new Cell().add(new Paragraph("\r\n"));
        spaceCell.setBorder(Border.NO_BORDER);
        // 第一行
        Cell shipFrom = new Cell().add(new Paragraph("Ship From:"));
        shipFrom.setBorder(Border.NO_BORDER)
                .setTextAlignment(TextAlignment.LEFT)
                .setHorizontalAlignment(HorizontalAlignment.LEFT)
                .setVerticalAlignment(VerticalAlignment.TOP);
        Cell shipTo = new Cell().add(new Paragraph("Ship To:"));
        shipTo
                .setBorder(Border.NO_BORDER)
                .setTextAlignment(TextAlignment.LEFT)
                .setHorizontalAlignment(HorizontalAlignment.RIGHT)
                .setVerticalAlignment(VerticalAlignment.TOP);

        shipTable.addCell(shipFrom).addCell(spaceCell).addCell(shipTo);

        // 第二行
        Cell shipFromCell = new Cell().add(new Paragraph(shipFromCellContent).setFontSize(10));
        shipFromCell.setPaddings(10, 10, 10, 10);
        shipFromCell.setWidth(200);

        shipTable.addCell(shipFromCell);

        // 添加间隙
        shipTable.addCell(spaceCell);
        List<ManifestItem> manifestItems = manifestAggregate.getManifestItems();
        Manifest manifest = manifestAggregate.getManifest();
        String shipToCellContent = manifest.getReceiverName() + "\r\n" + manifest.getReceiverName() + "\r\n" + manifest.getReceiverAddress();
        Cell shipToCell = new Cell().add(new Paragraph(shipToCellContent).setFontSize(10));
        shipToCell.setPaddings(10, 10, 10, 10);
        shipToCell.setWidth(200);
        shipToCell.setFont(font);

        shipTable.addCell(shipToCell);
        // ----------------------------------- 创建最下面的表格 ----------------------------------
        // 创建表格并设置列数和默认宽度
        Table table = new Table(4).setMarginLeft(40).setMarginRight(40);
        table.setMarginTop(2);
        table.setWidth(UnitValue.createPercentValue(100));  //页面总宽固定

        // 添加表头
        Cell headerDescriptionCell = new Cell().add(new Paragraph("DESCRIPTION")
                        .setTextAlignment(TextAlignment.CENTER))
                .setBackgroundColor(new DeviceRgb(240, 240, 240));
        Cell headerQuantityCell = new Cell().add(new Paragraph("QUANTITY")
                        .setTextAlignment(TextAlignment.CENTER))
                .setBackgroundColor(new DeviceRgb(240, 240, 240));
        Cell headerUnitPriceCell = new Cell().add(new Paragraph("UNIT PRICE(JPY)")
                        .setTextAlignment(TextAlignment.CENTER))
                .setBackgroundColor(new DeviceRgb(240, 240, 240));
        Cell headerTotalValueCell = new Cell().add(new Paragraph("TOTAL VALUE(JPY)")
                        .setTextAlignment(TextAlignment.CENTER))
                .setBackgroundColor(new DeviceRgb(240, 240, 240));
        table.addHeaderCell(headerDescriptionCell);
        table.addHeaderCell(headerQuantityCell);
        table.addHeaderCell(headerUnitPriceCell);
        table.addHeaderCell(headerTotalValueCell);

        // 添加表格内容
        for (ManifestItem entity : manifestItems) {
            table.addCell(new Cell().add(new Paragraph(entity.getNameEn()).setTextAlignment(TextAlignment.LEFT)));
            table.addCell(new Cell().add(new Paragraph(entity.getQuantity().toString()).setTextAlignment(TextAlignment.CENTER)));
            table.addCell(new Cell().add(new Paragraph(entity.getValue().divide(BigDecimal.valueOf(entity.getQuantity()), 2, RoundingMode.HALF_UP).toString()).setTextAlignment(TextAlignment.RIGHT)));
            table.addCell(new Cell().add(new Paragraph(entity.getValue().setScale(2, RoundingMode.HALF_UP).toString()).setTextAlignment(TextAlignment.RIGHT)));
        }

        table.addCell(new Cell(1, 2));
        table.addCell(new Cell().add(new Paragraph("Total")
                .setTextAlignment(TextAlignment.CENTER)
                .setBold()
                .setBackgroundColor(new DeviceRgb(240, 240, 240))));
        table.addCell(new Cell().add(new Paragraph(manifest.getValue().setScale(2, RoundingMode.HALF_UP).toString())
                .setTextAlignment(TextAlignment.RIGHT)
                .setBold()
                .setBackgroundColor(new DeviceRgb(240, 240, 240))));
        // 设置表格样式
        table.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 将表格添加到文档中
        document.add(shipTable);
        document.add(table);
    }

    private void createInvoiceTitle(Document document, PdfFont font) {
        // 创建段落标题
        Paragraph paragraphTitle = new Paragraph().setFont(font);
        // setBold:设置粗体，setItalic:斜体，setUnderline:下划线
        paragraphTitle.add("INVOICE").setBold().setFontSize(20);
        // 设置段落的对齐方式为居中
        paragraphTitle.setTextAlignment(TextAlignment.CENTER);
        document.add(paragraphTitle);
    }

    private void createWayBill(Document document, ManifestAggregate manifestAggregate, PdfFont font) {
        // 创建上方表格
        Table top = new Table(UnitValue.createPercentArray(new float[]{8, 1.2f})).setFont(font).setBorderTop(Border.NO_BORDER);

        // 创建左上角表格
        top.addCell(createWayBillTopLeft(manifestAggregate.getManifest(), font));

        // 创建右上方表格
        top.addCell(createWayBillTopRight(manifestAggregate, font));

        document.add(top);

        // 创建下方表格
        document.add(createWayBillBottom(manifestAggregate, font));

    }

    private Table createWayBillBottom(ManifestAggregate manifestAggregate, PdfFont font) {
        Manifest manifest = manifestAggregate.getManifest();
        List<ManifestItem> manifestItems = manifestAggregate.getManifestItems();
        Table bottom = new Table(10).setFont(font);
        // 第一行
        bottom.addCell(createTitleCellWithBackGroundColor(1, 7, "").add(new Paragraph("DETAILED DESCRIPTION OF CONTENT/物品详细描述").setFixedLeading(5).setTextAlignment(TextAlignment.CENTER)).setWidth(550));
        bottom.addCell(createTitleCellWithBackGroundColor(1, 3, "PAYMENT BY/付款方式").setWidth(75));


        // 第二行
        bottom.addCell(new Cell(1, 1).add(new Paragraph(manifestItems.stream().map(ManifestItem::getNameEn).collect(Collectors.joining("\r\n"))).setFontSize(8).setFixedLeading(10)).setBorderRight(Border.NO_BORDER).setBorderBottom(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 3).setBorder(Border.NO_BORDER));
        bottom.addCell(createTitleCellWithoutBottomBorder(1, 3, "DECLARED VALUE FOR CUSTOMS/海关申报价值").setBorderLeft(Border.NO_BORDER));
        bottom.addCell(new Cell(3, 1).add(new Paragraph("CA").setFontSize(8)));
        bottom.addCell(new Cell(3, 1).add(new Paragraph("CR").setFontSize(8)));
        bottom.addCell(new Cell(3, 1).add(new Paragraph("CC").setFontSize(8)));

        // 第三行
        bottom.addCell(new Cell(1, 4).setBorderTop(Border.NO_BORDER).setBorderBottom(Border.NO_BORDER).setBorderRight(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 3).add(new Paragraph("JPY " + manifest.getValue().setScale(2, RoundingMode.HALF_UP)).setFixedLeading(5).setFontSize(10).setTextAlignment(TextAlignment.CENTER)).setBorder(Border.NO_BORDER));

        // 第四行
        bottom.addCell(new Cell(1, 2).add(new Paragraph("PLEASE ATTACH COMMERCIAL INVOICE FOR SPS/包裹请随附发票").setFontSize(5)).setBorderTop(Border.NO_BORDER).setBorderRight(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 5).setBorderTop(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER));

        // 第五行
        bottom.addCell(createTitleCellWithoutBottomBorder(1, 1, "INSURANCE/保险").setBorderRight(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 2).setBorder(Border.NO_BORDER));
        bottom.addCell(createTitleCellWithoutBottomBorder(1, 2, "DIMENSION(cm)/尺寸（厘米）"));
        bottom.addCell(createTitleCellWithoutBottomBorder(1, 2, "HSCODE/海关编码"));
        bottom.addCell(createTitleCellWithBackGroundColor(1, 3, "TOTAL CHARGE/总金额"));

        // 第六行
        bottom.addCell(new Cell().add(new Paragraph("YES/是").setTextAlignment(TextAlignment.CENTER).setFontSize(5)).setBorderTop(Border.NO_BORDER).setBorderRight(Border.NO_BORDER).setBorderBottom(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 2).add(new Paragraph("AMOUNT/金额").setFontSize(5)).setBorder(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 2).setBorderTop(Border.NO_BORDER).setBorderBottom(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 2).setBorderTop(Border.NO_BORDER).setBorderBottom(Border.NO_BORDER));
        bottom.addCell(new Cell(4, 3));

        // 第七行
        bottom.addCell(new Cell().add(new Paragraph("NO/否").setTextAlignment(TextAlignment.CENTER).setFontSize(5)).setBorderTop(Border.NO_BORDER).setBorderRight(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 2).setBorderTop(Border.NO_BORDER).setBorderLeft(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 2).add(new Paragraph("LENGTH/长        WIDTH/宽        HEIGHT/高").setFontSize(5)).setBorderTop(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 2).setBorderTop(Border.NO_BORDER));

        // 第八行
        bottom.addCell(createTitleCellWithoutBottomBorder(1, 1, "PICKUP BY/取件人员签字"));
        bottom.addCell((createTitleCellWithoutBottomBorder(1, 1, "DATE/日期")));
        bottom.addCell((createTitleCellWithoutBottomBorder(1, 1, "TIME/时间")));
        bottom.addCell((createTitleCellWithoutBottomBorder(1, 1, "CONSIGNEE'S SIGNATURE/收件人签字")));
        bottom.addCell((createTitleCellWithoutBottomBorder(1, 2, "DATE/日期")));
        bottom.addCell((createTitleCellWithoutBottomBorder(1, 1, "TIME/时间")));

        // 第九行
        bottom.addCell(new Cell().setHeight(15).setBorderTop(Border.NO_BORDER));
        bottom.addCell(new Cell().setHeight(15).setBorderTop(Border.NO_BORDER));
        bottom.addCell(new Cell().setHeight(15).setBorderTop(Border.NO_BORDER));
        bottom.addCell(new Cell().setHeight(15).setBorderTop(Border.NO_BORDER));
        bottom.addCell(new Cell(1, 2).setHeight(15).setBorderTop(Border.NO_BORDER));
        bottom.addCell(new Cell().setHeight(15).setBorderTop(Border.NO_BORDER));
        return bottom;
    }

    private Table createWayBillTopRight(ManifestAggregate manifestAggregate, PdfFont font) {
        Table topRight = new Table(1).setFont(font).setWidth(75).setMargin(-2.5f).setHeight(UnitValue.createPercentValue(100)).setBorder(Border.NO_BORDER);
        topRight.addCell(createTitleCellWithBackGroundColor(1, 1, "DESTINATION/目的地"));
        topRight.addCell(new Cell().setHeight(33));
        topRight.addCell(createTitleCellWithoutBottomBorder(1, 1, "NUMBER OF PKG/包裹件数"));
        topRight.addCell(new Cell().add(new Paragraph(manifestAggregate.getManifestItems().size() + "")).setBorderTop(Border.NO_BORDER).setHeight(15).setFontSize(8));
        topRight.addCell(createTitleCellWithoutBottomBorder(1, 1, "WEIGHT(kg)/重量（公斤）"));
        topRight.addCell(new Cell().add(new Paragraph(manifestAggregate.getManifest().getWeight().setScale(1, RoundingMode.HALF_UP).toString())).setBorderTop(Border.NO_BORDER).setHeight(15).setFontSize(8));
        topRight.addCell(createTitleCellWithoutBottomBorder(1, 1, "DIMENSIONAL WEIGHT(kg) /体积重量（公斤）"));
        topRight.addCell(new Cell().setHeight(15).setBorderTop(Border.NO_BORDER));
        topRight.addCell(createTitleCellWithoutBottomBorder(1, 1, "CHARGEABLE WEIGHT(kg) /计费重量（公斤）"));
        topRight.addCell(new Cell().setHeight(15).setBorderTop(Border.NO_BORDER));
        topRight.addCell(createTitleCellWithBackGroundColor(1, 1, "EXTRA CHARGE/其他费用"));
        topRight.addCell(new Cell().setHeight(15));
        topRight.addCell(createTitleCellWithBackGroundColor(1, 1, "SERVICE/服务类型"));
        topRight.addCell(new Cell().setMinHeight(15).setBorderBottom(Border.NO_BORDER));
        return topRight;
    }

    private Table createWayBillTopLeft(Manifest manifest, PdfFont font) {
        Table topLeft = new Table(8).setFont(font).setMargin(-2.5f).setBorderTop(Border.NO_BORDER);
        // 宽度行
        topLeft.addCell(new Cell().setWidth(100).setBorder(Border.NO_BORDER).setHeight(1));
        topLeft.addCell(new Cell().setWidth(70).setBorder(Border.NO_BORDER).setHeight(1));
        topLeft.addCell(new Cell().setWidth(20).setBorder(Border.NO_BORDER).setHeight(1));
        topLeft.addCell(new Cell().setWidth(170).setBorder(Border.NO_BORDER).setHeight(1));
        topLeft.addCell(new Cell().setWidth(35).setBorder(Border.NO_BORDER).setHeight(1));
        topLeft.addCell(new Cell().setWidth(70).setBorder(Border.NO_BORDER).setHeight(1));
        topLeft.addCell(new Cell().setWidth(18).setBorder(Border.NO_BORDER).setHeight(1));
        topLeft.addCell(new Cell().setWidth(80).setBorder(Border.NO_BORDER).setHeight(1));

        // 第一行空行
        topLeft.addCell(new Cell(1, 3).setBorder(Border.NO_BORDER));
        Image invoiceLogo = null;
        try {
            ClassPathResource classPathResource = new ClassPathResource("classpath:images/invoiceLogo.png");
            invoiceLogo = new Image(ImageDataFactory.create(classPathResource.getPath())).setHeight(50);
        } catch (MalformedURLException e) {
            log.error("加载logo错误: ", e);
            throw new ServiceException(9999, "加载logo错误");
        }
        topLeft.addCell(new Cell(4, 2).add(invoiceLogo).setBorder(Border.NO_BORDER));

        // 生成条形码图片
        Image barcodeImage = generateBarcodeImage(manifest.getOrderNo().toUpperCase());
        topLeft.addCell(new Cell(3, 3).add(barcodeImage).setBorder(Border.NO_BORDER));

        // 第二行表头
        // REGISTER CODE/注册码
        topLeft.addCell(createTitleCellWithBackGroundColor(1, 1, "REGISTER CODE/注册码"));
        topLeft.addCell(createTitleCellWithBackGroundColor(1, 2, "ORGIN/始发地"));

        // 第三行 待填
        topLeft.addCell(new Cell(1, 1).setHeight(20).setBorderBottom(Border.NO_BORDER));
        topLeft.addCell(new Cell(1, 2).setHeight(20).setBorderBottom(Border.NO_BORDER));

        topLeft.addCell(new Cell(1, 1).setHeight(15).setBorderTop(Border.NO_BORDER));
        topLeft.addCell(new Cell(1, 2).setHeight(15).setBorderTop(Border.NO_BORDER));
        topLeft.addCell(new Cell(1, 3).add(new Paragraph(manifest.getOrderNo()).setTextAlignment(TextAlignment.CENTER).setFontSize(8)).setBorder(Border.NO_BORDER));

        // 第四行 表头
        topLeft.addCell(createTitleCellWithBackGroundColor(1, 3, "SHIPPER'S ACCOUNT 发件人账号"));
        topLeft.addCell(createTitleCellWithBackGroundColor(1, 2, "ITEM TYPE,PLEASE CHECK/快件类型，请选择"));
        topLeft.addCell(createTitleCellWithBackGroundColor(1, 2, "PAY OR'S ACCOUNT/付费方账号"));
        topLeft.addCell(createTitleCellWithBackGroundColor(1, 1, "REFERENCE NO/参考号"));

        // 第五行 待填
        topLeft.addCell(new Cell(1, 3).setHeight(20));
        topLeft.addCell(new Cell(1, 2).setHeight(20));
        topLeft.addCell(new Cell(1, 2).setHeight(20));
        topLeft.addCell(new Cell(1, 1).setHeight(20));

        // 第六行 表头
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 4, "FROM(SHIPPER)/由（发件公司） （PLEASE PRINT IN ENGLISH/请用英文打印）"));
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 4, "FROM(SHIPPER)/由（发件公司） （PLEASE PRINT IN ENGLISH/请用英文打印）"));

        // 第七行
        topLeft.addCell(new Cell(1, 4).add(new Paragraph("Fuzhou Sufeng Information Technology Co., ···")).setFontSize(8).setHeight(20).setBorderTop(Border.NO_BORDER));
        topLeft.addCell(new Cell(1, 4).add(new Paragraph(manifest.getReceiverName())).setFontSize(8).setHeight(20).setBorderTop(Border.NO_BORDER));

        // 第八行 表头
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 4, "DETAILED ADDRESS/详细地址：P.O.BOX ADDRESS NOT ACCEPTED/请勿使用邮政信箱地址"));
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 4, "DETAILED ADDRESS/详细地址：P.O.BOX ADDRESS NOT ACCEPTED/请勿使用邮政信箱地址"));

        // 第九行
        topLeft.addCell(new Cell(1, 4).add(new Paragraph("1902, 19th Floor, Building B, Netcom Smart Center, No. 11 Keji East Road, Shangjie Town, Minhou Count")).setFontSize(8).setHeight(50).setBorderTop(Border.NO_BORDER));
        topLeft.addCell(new Cell(1, 4).add(new Paragraph(manifest.getReceiverAddress())).setFontSize(8).setHeight(50).setBorderTop(Border.NO_BORDER).setBorderBottom(Border.NO_BORDER));

        // 第十行
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 2, "SHIPPER'S SIGNATURE/发件人签字"));
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 2, "PHONE/电话"));
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 2, "POSTCODE/邮编"));
        topLeft.addCell(new Cell(2, 2).setBorder(Border.NO_BORDER));

        // 第十一行
        int height11 = 15;
        topLeft.addCell(new Cell(1, 2).setHeight(height11).setBorderTop(Border.NO_BORDER));
        topLeft.addCell(new Cell(1, 2).add(new Paragraph("0")).setFontSize(8).setHeight(height11).setBorderTop(Border.NO_BORDER));
        topLeft.addCell(new Cell(1, 2).add(new Paragraph(manifest.getReceiverZipCode())).setFontSize(8).setHeight(height11).setBorderTop(Border.NO_BORDER));

        // 第十二行
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 2, "REGISTRATION NO.IN CUSTOMS/海关注册"));
        topLeft.addCell(new Cell(1, 2).setBorderBottom(Border.NO_BORDER));
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 2, "ATTN/收件人"));
        topLeft.addCell(createTitleCellWithoutBottomBorder(1, 2, "PHONE/电话"));

        // 第十三行
        topLeft.addCell(new Cell(1, 2).setBorderTop(Border.NO_BORDER).setHeight(15));
        topLeft.addCell(new Cell(1, 2).setBorderTop(Border.NO_BORDER).setHeight(15));
        topLeft.addCell(new Cell(1, 2).setBorderTop(Border.NO_BORDER).setHeight(15));
        topLeft.addCell(new Cell(1, 2).setBorderTop(Border.NO_BORDER).add(new Paragraph(manifest.getReceiverPhone())).setFontSize(8).setHeight(15));
        return topLeft;
    }

    private static Cell createTitleCell(int rowspan, int colspan, String text) {
        return new Cell(rowspan, colspan)
                .add(new Paragraph(text).setFixedLeading(5))
                .setMinHeight(10)
                .setFontSize(5)
                .setPaddingBottom(-2)
                ;
    }

    private static Cell createTitleCellWithBackGroundColor(int rowspan, int colspan, String text) {
        return createTitleCell(rowspan, colspan, text)
                .setBackgroundColor(new DeviceRgb(255, 128, 128));
    }

    private static Cell createTitleCellWithoutBottomBorder(int rowspan, int colspan, String text) {
        return createTitleCell(rowspan, colspan, text)
                .setBorderBottom(Border.NO_BORDER);
    }

    private static Image generateBarcodeImage(String billNo) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            BitMatrix bitMatrix = new MultiFormatWriter().encode(billNo, BarcodeFormat.CODE_128, 300, 100);
            MatrixToImageWriter.writeToStream(bitMatrix, "png", outputStream);
            return new Image(ImageDataFactory.create(outputStream.toByteArray())).setHeight(40).setWidth(200);
        } catch (Exception e) {
            log.error("生成条形码失败", e);
            throw new RuntimeException(e);
        }
    }
}
