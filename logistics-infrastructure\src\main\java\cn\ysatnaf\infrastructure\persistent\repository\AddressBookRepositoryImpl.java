package cn.ysatnaf.infrastructure.persistent.repository;

import cn.hutool.core.collection.CollUtil;
import cn.ysatnaf.domain.address.model.entity.AddressBookEntity;
import cn.ysatnaf.domain.address.repository.AddressBookRepository;
import cn.ysatnaf.infrastructure.persistent.converter.AddressBookConverter;
import cn.ysatnaf.infrastructure.persistent.dao.AddressBookDao;
import cn.ysatnaf.infrastructure.persistent.po.AddressBookPO;
import cn.ysatnaf.types.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AddressBookRepositoryImpl
 *
 * <AUTHOR> Hang
 * @date 2023/12/22 10:48
 */
@Repository
@RequiredArgsConstructor
public class AddressBookRepositoryImpl implements AddressBookRepository {

    private final AddressBookDao addressBookDao;

    @Override
    public Boolean insert(AddressBookEntity addressBookEntity) {
        return addressBookDao.insert(AddressBookConverter.INSTANCE.entity2po(addressBookEntity)) == 1;
    }

    @Override
    public Boolean delete(Long id) {
        return addressBookDao.deleteById(id) == 1;
    }

    @Override
    public Boolean updateById(AddressBookEntity addressBookEntity) {
        return addressBookDao.updateById(AddressBookConverter.INSTANCE.entity2po(addressBookEntity)) == 1;
    }

    @Override
    public PageResult<AddressBookEntity> page(String openid, Integer pageNo, Integer pageSize) {
        LambdaQueryWrapper<AddressBookPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AddressBookPO::getOpenid, openid);
        wrapper.orderByDesc(AddressBookPO::getIsDefault);
        Page<AddressBookPO> addressBookPOPage = addressBookDao.selectPage(new Page<>(pageNo, pageSize), wrapper);
        if (addressBookPOPage.getSize() == 0) {
            return PageResult.empty(addressBookPOPage.getTotal());
        }
        List<AddressBookEntity> records = addressBookPOPage
                .getRecords().stream().map(AddressBookConverter.INSTANCE::po2entity)
                .collect(Collectors.toList());
        return new PageResult<>(records, addressBookPOPage.getTotal());
    }

    @Override
    public AddressBookEntity getById(Long sendAddressBookId) {
        return AddressBookConverter.INSTANCE.po2entity(addressBookDao.selectById(sendAddressBookId));
    }

    @Override
    public List<AddressBookEntity> listByOpenid(String openid) {
        LambdaQueryWrapper<AddressBookPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AddressBookPO::getOpenid, openid);
        return AddressBookConverter.INSTANCE.po2entityList(addressBookDao.selectList(wrapper));
    }

    @Override
    public void updateBatchByIds(List<AddressBookEntity> bookEntities) {
        addressBookDao.updateBatchById(AddressBookConverter.INSTANCE.entity2poList(bookEntities));
    }

    @Override
    public AddressBookEntity getDefaultByOpenid(String openid) {
        LambdaQueryWrapper<AddressBookPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AddressBookPO::getOpenid, openid);
        wrapper.eq(AddressBookPO::getIsDefault, true);
        return AddressBookConverter.INSTANCE.po2entity(addressBookDao.selectOne(wrapper));
    }

    @Override
    public AddressBookEntity getFirstOneByOpenid(String openid) {
        LambdaQueryWrapper<AddressBookPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AddressBookPO::getOpenid, openid);
        Page<AddressBookPO> page = addressBookDao.selectPage(new Page<>(1, 1), wrapper);
        if (CollUtil.isEmpty(page.getRecords())) {
            return null;
        }
        return AddressBookConverter.INSTANCE.po2entity(page.getRecords().get(0));
    }
}
