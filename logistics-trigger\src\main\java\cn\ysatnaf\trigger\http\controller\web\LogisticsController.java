package cn.ysatnaf.trigger.http.controller.web;

import cn.ysatnaf.domain.tracking.model.dto.LogisticsData;
import cn.ysatnaf.domain.tracking.model.service.LogisticsService;
import cn.ysatnaf.types.common.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

@RestController
@RequestMapping("/api/logistics")
public class LogisticsController {

    @Autowired
    private LogisticsService logisticsService;
    
    @GetMapping("/tracking/{trackingNumber}")
    public CommonResult<LogisticsData> trackLogistics(@PathVariable("trackingNumber") @NotBlank(message = "运单号不能为空") String trackingNumber) {
        return CommonResult.success(logisticsService.queryLogisticsInfo(trackingNumber));
    }
}