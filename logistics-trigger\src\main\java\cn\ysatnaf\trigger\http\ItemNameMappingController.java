package cn.ysatnaf.trigger.http;

import cn.ysatnaf.domain.item.model.dto.ItemNameMappingAddReq;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingDTO;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingPageReq;
import cn.ysatnaf.domain.item.model.dto.ItemNameMappingUpdateReq;
import cn.ysatnaf.domain.item.service.ItemNameMappingService;
import cn.ysatnaf.types.common.CommonResult; // 假设统一响应结果类
import cn.ysatnaf.types.common.PageResult; // 假设分页结果类
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 物品名称映射管理 API
 */
@Tag(name = "管理端 - 物品名称映射管理")
@RestController
@RequestMapping("/api/item-mappings")
@RequiredArgsConstructor
@Validated
public class ItemNameMappingController {

    private final ItemNameMappingService itemNameMappingService;

    @PostMapping("/upsert")
    @Operation(summary = "添加或更新物品名称映射 (Upsert)")
    // @PreAuthorize("@ss.hasPermi('item:mapping:upsert')") // Example permission
    public CommonResult<Long> upsertMapping(@Valid @RequestBody ItemNameMappingAddReq req) {
        Long id = itemNameMappingService.upsertMapping(req);
        return CommonResult.success(id);
    }

    @PostMapping
    @Operation(summary = "添加物品名称映射")
    // @PreAuthorize("@ss.hasPermi('item:mapping:add')")
    public CommonResult<Long> addMapping(@Valid @RequestBody ItemNameMappingAddReq req) {
        Long id = itemNameMappingService.addMapping(req);
        return CommonResult.success(id);
    }

    @PutMapping
    @Operation(summary = "更新物品名称映射")
    // @PreAuthorize("@ss.hasPermi('item:mapping:update')")
    public CommonResult<Boolean> updateMapping(@Valid @RequestBody ItemNameMappingUpdateReq req) {
        boolean success = itemNameMappingService.updateMapping(req);
        return CommonResult.success(success);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除物品名称映射")
    @Parameter(name = "id", description = "映射ID", required = true, example = "1")
    // @PreAuthorize("@ss.hasPermi('item:mapping:delete')")
    public CommonResult<Boolean> deleteMapping(@PathVariable("id") Long id) {
        boolean success = itemNameMappingService.deleteMapping(id);
        return CommonResult.success(success);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取物品名称映射详情")
    @Parameter(name = "id", description = "映射ID", required = true, example = "1")
    // @PreAuthorize("@ss.hasPermi('item:mapping:query')")
    public CommonResult<ItemNameMappingDTO> getMappingById(@PathVariable("id") Long id) {
        ItemNameMappingDTO dto = itemNameMappingService.getMappingById(id);
        return CommonResult.success(dto);
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询物品名称映射")
    // @PreAuthorize("@ss.hasPermi('item:mapping:list')")
    public CommonResult<PageResult<ItemNameMappingDTO>> pageMappings(@Valid ItemNameMappingPageReq req) {
        PageResult<ItemNameMappingDTO> pageResult = itemNameMappingService.pageMappings(req);
        return CommonResult.success(pageResult);
    }

    // findAndUseActiveMappedName 通常是内部服务调用，不暴露为 API
} 