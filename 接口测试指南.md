# 外部系统录入运单接口测试指南

## 🚀 快速测试

### 测试环境准备

1. **确保服务启动**：logistics 服务正常运行
2. **获取认证 token**：准备有效的 JWT token
3. **准备测试数据**：确保数据库中有有效的 location 和 shipmentType 数据

### 基础功能测试

#### 1. 正常流程测试

```bash
curl -X POST "http://localhost:8080/web/manifest/addWithAllocatedExpress" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "orderNumber": "TEST001",
    "locationId": 1,
    "shipmentTypeId": 1,
    "zipCode": "1000001",
    "receiverName": "测试用户",
    "receiverAddress": "东京都千代田区测试地址1-1-1",
    "receiverPhone": "09012345678",
    "items": [
      {
        "name": "测试商品",
        "weight": 0.5,
        "quantity": 1,
        "price": 100.00
      }
    ]
  }'
```

#### 2. 参数校验测试

```bash
# 测试缺少必填参数
curl -X POST "http://localhost:8080/web/manifest/addWithAllocatedExpress" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "orderNumber": "",
    "locationId": null,
    "shipmentTypeId": null
  }'
```

#### 3. 无效数据测试

```bash
# 测试无效的地点ID和货物类型ID
curl -X POST "http://localhost:8080/web/manifest/addWithAllocatedExpress" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "orderNumber": "TEST002",
    "locationId": 999999,
    "shipmentTypeId": 999999,
    "zipCode": "1000001",
    "receiverName": "测试用户",
    "receiverAddress": "东京都千代田区测试地址1-1-1",
    "receiverPhone": "09012345678",
    "items": [
      {
        "name": "测试商品",
        "weight": 0.5,
        "quantity": 1,
        "price": 100.00
      }
    ]
  }'
```

## 📋 测试检查清单

### ✅ 功能测试

- [ ] 正常请求返回成功结果
- [ ] 自动分配的单号不重复
- [ ] 运单数据正确保存到数据库
- [ ] 物品信息正确关联
- [ ] 操作日志正确记录

### ✅ 参数校验测试

- [ ] 缺少必填参数返回 400 错误
- [ ] 无效的 locationId 返回错误
- [ ] 无效的 shipmentTypeId 返回错误
- [ ] 无效的电话号码格式返回错误
- [ ] 无效的邮编格式返回错误

### ✅ 异常情况测试

- [ ] 单号库存不足时的错误处理
- [ ] 网络用户佐川单号库存不足的错误处理
- [ ] 数据库连接异常的处理
- [ ] 高并发情况下的数据一致性

### ✅ 业务逻辑测试

- [ ] 不同用户类型的单号分配逻辑
- [ ] 网络用户的佐川单号分配
- [ ] 管理员代录功能
- [ ] 分配批次记录的创建

## 🔍 验证步骤

### 1. 数据库验证

```sql
-- 检查运单是否创建成功
SELECT * FROM manifest WHERE express_number = '分配的单号';

-- 检查物品是否关联成功
SELECT * FROM manifest_item WHERE manifest_id = '运单ID';

-- 检查单号分配记录
SELECT * FROM tracking_number_allocation_batch WHERE customer_account_id = '用户ID';

-- 检查单号状态
SELECT * FROM tracking_number_pool WHERE tracking_number = '分配的单号';
```

### 2. 日志验证

```bash
# 查看应用日志
tail -f logs/application.log | grep "外部系统API-录入运单"

# 查看单号分配日志
tail -f logs/application.log | grep "分配单号"
```

## ⚠️ 常见问题

### 问题 1：渠道单号库存不足

**现象**：返回错误"渠道 [渠道名] 单号库存不足"
**解决**：

1. 检查对应渠道的单号库存
2. 导入更多单号到该渠道
3. 或使用其他有库存的地点/货物类型组合

### 问题 2：无法找到匹配的渠道

**现象**：返回错误"无法为此地点和货物类型找到有效的单号渠道"
**解决**：

1. 检查渠道配置表是否有对应的记录
2. 确认渠道是否已启用
3. 验证 locationId 和 shipmentTypeId 是否正确

### 问题 3：网络用户佐川单号不足

**现象**：返回错误"系统内部物流单号库存不足"
**解决**：

1. 检查佐川单号库存
2. 补充佐川单号
3. 或将用户类型改为非网络用户

## 📊 性能测试

### 单个请求性能

```bash
# 使用ab工具测试
ab -n 100 -c 10 -T "application/json" -H "Authorization: Bearer YOUR_TOKEN" \
   -p test_data.json http://localhost:8080/web/manifest/addWithAllocatedExpress
```

### 并发测试

```bash
# 使用wrk工具测试并发性能
wrk -t10 -c100 -d30s -s test_script.lua http://localhost:8080/web/manifest/addWithAllocatedExpress
```

## 💡 测试建议

1. **环境隔离**：使用独立的测试环境，避免影响生产数据
2. **数据清理**：测试完成后清理测试数据
3. **监控观察**：观察系统资源使用情况和响应时间
4. **边界测试**：测试各种边界条件和异常情况
5. **安全测试**：验证权限控制和参数过滤功能

---

**测试完成后请记得**：

- [ ] 清理测试数据
- [ ] 检查日志是否有异常
- [ ] 验证系统状态正常
- [ ] 更新测试报告
