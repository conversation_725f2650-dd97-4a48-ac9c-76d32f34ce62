package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.infrastructure.persistent.po.ManifestItemPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ManifestItemDao extends BaseMapper<ManifestItemPO> {
    void insertBatchSomeColumn(@Param("list") List<ManifestItemPO> list);
}
