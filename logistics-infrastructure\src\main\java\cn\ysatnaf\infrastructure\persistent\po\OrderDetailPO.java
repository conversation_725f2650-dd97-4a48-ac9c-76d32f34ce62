package cn.ysatnaf.infrastructure.persistent.po;

import cn.ysatnaf.domain.po.BasePO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * OrderDetailPO
 * 订单明细
 * <AUTHOR> Hang
 * @date 2024/2/7 11:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tb_order_detail")
public class OrderDetailPO extends BasePO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 长度
     */
    private BigDecimal length;

    /**
     * 宽度
     */
    private BigDecimal width;

    /**
     * 高度
     */
    private BigDecimal height;

    /**
     * 体积重量
     */
    private BigDecimal dimensionalWeight;

    /**
     * 基本费用
     */
    private BigDecimal basicFee;
}
