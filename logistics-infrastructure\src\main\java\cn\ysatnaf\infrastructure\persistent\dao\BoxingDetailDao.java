package cn.ysatnaf.infrastructure.persistent.dao;

import cn.ysatnaf.domain.parcelsorting.model.po.ParcelSortingPackagePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> Hang
 */
@Mapper
public interface BoxingDetailDao extends BaseMapper<ParcelSortingPackagePO> {

    /**
     * 批量插入包裹信息（选择性插入列）
     * @param list 包裹列表
     * @return 成功插入的记录数
     */
    int insertBatchSomeColumn(@Param("list") List<ParcelSortingPackagePO> list);
}
