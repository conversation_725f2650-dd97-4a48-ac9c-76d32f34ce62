package cn.ysatnaf.domain.manifest.model.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 主提单分页查询请求DTO
 * <AUTHOR>
 */
@Data
public class MasterBillPageReq {

    /**
     * 提单号/航班号（支持模糊查询）
     */
    private String masterBillNumber;

    /**
     * 起飞日期起始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime departureDateStart;

    /**
     * 起飞日期截止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime departureDateEnd;

    /**
     * 到达日期起始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime arrivalDateStart;

    /**
     * 到达日期截止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime arrivalDateEnd;

    /**
     * 创建日期
     */
    @Schema(description = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createDate;

    /**
     * 日期间隔点(0-23)
     * 例如：选择了18，那么3月2日18:00之前的记录属于3月1日，3月2日18:00到3月3日18:00之间的属于3月2日
     */
    private Integer timeIntervalPoint = 18;

    /**
     * 始发地
     */
    private String origin;

    /**
     * 目的地
     */
    private String destination;

    /**
     * 承运商代码
     */
    private String carrierCode;

    /**
     * 提单状态
     */
    private Integer status;

    /**
     * 页码，从1开始
     */
    private Integer pageNo = 1;

    /**
     * 每页记录数
     */
    private Integer pageSize = 10;
} 